/*
 * @Author: chenn26
 * @Date: 2023-05-19 15:25:58
 * @LastEditors: chenn26
 * @LastEditTime: 2023-05-22 20:02:29
 * @Description 镇级大屏
 */

import request from '@/router/axios';

/**
 * @description: 右上角镇级列表
 * @param {*}
 * @return {*}
 * @author: chenn26
 */
export const getTownList = () => {
  return request({
    url: '/api/blade-system/dept/list',
    method: 'get',
  })
}


export const getCommunicate = (params) => {
  return request({
    url: '/api/big-screen/communicate',
    method: 'get',
    params: params
  })
}

export const getVillageScenic = (params) => {
  return request({
    url:"/api/admin/village-scenic-spot/page",
    method:'get',
    params: params
  })
}

export const getInformationRelease = (params) => {
  return request({
    url:"/api/information_release/list",
    method:'get',
    params: params
  })
}

export const getCommunicateDetail = (params) => {
  return request({
    url:"/api/communicate/detail",
    method:'get',
    params:params
  })
}
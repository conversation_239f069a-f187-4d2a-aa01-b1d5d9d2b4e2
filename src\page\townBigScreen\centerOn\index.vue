<!--
 * @Author: chenn26
 * @Date: 2023-05-10 16:23:14
 * @LastEditors: chenn26
 * @LastEditTime: 2023-05-26 17:18:35
 * @Description: 中上
-->

<template>
  <div class="town-center-on-content">
    <div class="header">
      <img src="/img/screen/title-decorate-center.png" class="left-decorate" alt="" />
      <span class="title" :title="villageName">&nbsp;{{ villageName | filter_name }}镇简介</span>
      <img src="/img/screen/title-decorate-center.png" class="right-decorate" alt="" />
    </div>
    <div class="content">
      <div class="content-carousel block">
        <el-carousel height="18vh" interval="5000" indicator-position="none">
          <el-carousel-item v-for="item in descImageList" :key="item">
            <img :src="item.link" style="width: 100%;height: 18vh;" alt="" />
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="content-desc">
        <img src="/img/townScreen/descbox.png" width="100%" height="100%" class="left-decorate" alt="" />
        <p class="desc">{{ villageDesc }}</p>
      </div>
      <span class="content-village-info">
        <img src="/img/townScreen/descbox.png" width="100%" height="95%" class="left-decorate" alt="" />
        <div class="box" style="left:3%;top:1vh;position: absolute;">
          <img src="/img/townScreen/base.png" width="100%" height="130%">
          <p class="infoP">
            <v-countup :start-value="0" :end-value="villageData.villageSize" class="num" :title="villageData.villageSize"/>
            <span class="unit"> 个 </span>
          </p>
          <p class="infoP2">
            行政村
          </p>
        </div>
        <div class="box" style="left:35%;top:1vh;">
          <img src="/img/townScreen/base.png" width="100%" height="130%">
          <p class="infoP">
            <v-countup :start-value="0" :end-value="villageData.villageArea" class="num" :title="villageData.villageArea" decimals="2"/>
            <span class="unit"> 公里² </span>
          </p>
          <p class="infoP2">
            总面积
          </p>
        </div>
        <div class="box" style="left:67%;top:1vh;">
          <img src="/img/townScreen/base.png" width="100%" height="130%">
          <p class="infoP">
            <v-countup :start-value="0" :end-value="villageData.populationNum" class="num" :title="villageData.populationNum"/>
            <span class="unit"> 人 </span>
          </p>
          <p class="infoP2">
            总人口
          </p>
        </div>
        <div class="box" style="left:3%;top:10vh;">
          <img src="/img/townScreen/base.png" width="100%" height="130%">
          <p class="infoP">
            <v-countup :start-value="0" :end-value="villageData.homeSize" class="num" :title="villageData.homeSize"/>
            <span class="unit"> 户 </span>
          </p>
          <p class="infoP2">
            家庭户数
          </p>
        </div>
        <div class="box" style="left:35%;top:10vh;">
          <img src="/img/townScreen/base.png" width="100%" height="130%">
          <p class="infoP">
            <v-countup :start-value="0" :end-value="villageData.partyMemberNum" class="num" :title="villageData.partyMemberNum"/>
            <span class="unit"> 人 </span>
          </p>
          <p class="infoP2">
            党员人数
          </p>
        </div>
        <div class="box" style="left:67%;top:10vh;">
          <img src="/img/townScreen/base.png" width="100%" height="130%">
          <p class="infoP">
            <v-countup :start-value="0" :end-value="villageData.totalOutput" class="num" :title="villageData.totalOutput" decimals="2"/>
            <span class="unit"> 元 </span>
          </p>
          <p class="infoP2">
            国内生产总值
          </p>
        </div>
        <!-- <div class="content-village-data" style="top:4vh;">
          <span :title="'辖区' + villageData.villageSize + '个行政村'">辖区 {{ villageData.villageSize }} 个行政村</span>
          <span :title="'总面积' + villageData.villageArea + '平方公里'">总面积 {{ villageData.villageArea }} 平方公里</span>
        </div>
        <div class="content-village-data" style="top:8vh;">
          <span :title="'总人口：' + villageData.populationNum">总人口：{{ villageData.populationNum }}</span>
          <span :title="'家庭户数：' + villageData.homeSize">家庭户数：{{ villageData.homeSize }}</span>
        </div>
        <div class="content-village-data" style="top:12vh;">
          <span :title="'党员人数：' + villageData.partyMemberNum">党员人数：{{ villageData.partyMemberNum }}</span>
          <span :title="'现国内生产总值' + villageData.totalOutput + '元'">现国内生产总值 {{ villageData.totalOutput }} 元</span>
        </div> -->
      </span>
    </div>
  </div>
</template>
<script>
import "./index.scss";
export default {
  props: {
    villageDesc: String,
    descImageList: Array,
    villageName: String,
    villageData: Object
  },
  data() {
    return {};
  }
};
</script>
<style lang="scss" scoped>
.infoP {
  position: absolute;
  top: -0.5vh;
  width: 100%;
  text-align: center;

  .num {
    height: 2.6vh;
    display: block;
    font-size: 2vh;
    text-align: center;
    width: 40%;
    margin-left:30%;
    overflow: hidden;
    float: left;
    white-space: nowrap;
    text-overflow: ellipsis;
    color:#46ecd2;
    font-weight: bold;
  }

  .unit {
    width: 15%;
    display: block;
    float: left;
    margin-top:1vh;
    color:#46ecd2;
  }
}

.infoP2 {
  position: absolute;
  top: 1.5vh;
  width: 100%;
  text-align: center;
  font-size: 1.8vh;
}</style>
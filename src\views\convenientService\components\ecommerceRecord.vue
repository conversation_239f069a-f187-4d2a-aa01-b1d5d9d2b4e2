<!--
 * @Description: 自定义模态框
 * @Author: chenz76
 * @Date: 2021-07-08 11:00:48
 * @LastEditors: chenn26
 * @LastEditTime: 2023-05-04 16:36:22
-->
<template>
  <!-- 弹窗部分 -->
    <el-dialog
      :visible.sync="dialogVisible"
      append-to-body
      :before-close="beforeClose"
      :show-close="true"
      :close-on-press-escape="true"
      width="70%"
      top="50px"
      :destroy-on-close="false"
      title="学习记录"
    >
      <div>
         <div class="stat-panel">
            <div class="stat-item">
                <div class="stat-label">学习人数</div>
                <div class="stat-value">{{ stats.studyCount||0 }}人</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">完成人数</div>
                <div class="stat-value">{{ stats.finishCount||0 }}人</div>
            </div>
            <!-- <div class="stat-item">
                <div class="stat-label">完成率</div>
                <div class="stat-value">{{ stats.completionRate }}</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">平均进度</div>
                <div class="stat-value">{{ stats.averageProgress }}</div>
            </div> -->
        </div>
        <avue-crud 
          :data="data" 
          :option="tableOption"
          :page.sync="page"
          @size-change="sizeChange"
          @current-change="currentChange"
          @refresh-change="refreshChange" 
          @on-load="onLoad" >
          <!-- 进度自定义显示 -->
          <template slot="progress" slot-scope="scope">
              <div class="progress-container">
                  <el-progress :percentage="scope.row.progress" :show-text="false" :stroke-width="8"></el-progress>
                  <div class="progress-value">{{ scope.row.progress }}%</div>
              </div>
          </template>
        </avue-crud>
      </div>
    </el-dialog>
</template>

<script>
import {studyHistory,studyStatic} from '@/api/infoRelease/ecommerce'

export default {
  props: ["dialogVisible",'recordId'],
  data() {
    return {
       tableOption: {
        addBtn: false,    // 隐藏添加按钮
        editBtn: false,   // 隐藏编辑按钮
        delBtn: false,    // 隐藏删除按钮
        menu: false,      // 隐藏操作栏
        refreshBtn: false, // 隐藏刷新按钮
        border: true,
        stripe: true,
        index: true,     // 显示索引列
        indexLabel: '#',
        column: [{
          label: '账号',
          prop: 'account',
        }, {
          label: '进度',
          prop: 'progress',
          slot: true,
        }, {
          label: '学习时长',
          prop: 'studyTime'
        }, {
          label: '状态',
          prop: 'status',
          dicData: [
              { label: '已完成', value: 1 },
              { label: '进行中', value: 0 }
            ],
          width: 160
        }, {
          label: '最近学习时间',
          prop: 'updateTime',
          width: 160
        }]
      },
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      stats:{}
    };
  },
  watch:{
    recordId:{
      handler(nv){
        if(nv){
            this.refreshStats()
        }
        
      },immediate:true
    }
  },
  methods: {
    refreshStats(){
      console.log('ddd')
      studyStatic({id:this.recordId}).then(res=>{
        this.stats=res.data.data
      })
    },
    beforeClose(done) {
     this.$emit('update:dialogVisible',false)
      done();
    },
      // 当前页切换
    currentChange (currentPage) {
      this.page.currentPage = currentPage;
    },
    // 页面显示条数切换
    sizeChange (pageSize) {
      this.page.pageSize = pageSize;
    },
    // 刷新
    refreshChange () {
      this.page.currentPage=1
      this.onLoad(this.page, this.query);
    },
    // 首次加载
    async onLoad (page, params = {}) {
      this.loading = true;
      let query = {
        ...params,
        current: this.page.currentPage,
        size: this.page.pageSize,
        id:this.recordId
        
      };
      if (query.updateTime) {
        if (Array.isArray(query.updateTime)) {
          query.begin = query.updateTime[0];
          query.end = query.updateTime[1];
        }
        delete query.updateTime;
      }
      // needtochange
      let res = await studyHistory(query);
      if (res && res.data && res.data.success) {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        if (this.page.currentPage > 1 && this.page.currentPage > data.pages) {
          this.page.currentPage = data.pages
          this.onLoad()
        }
      }
    },
  },
};
</script>

<style lang="scss">
.stat-panel {
    display: flex;
    background: #f5f7fa;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}
.stat-item {
    flex: 1;
    text-align: center;
}
.stat-label {
    font-size: 14px;
    color: #909399;
}
.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #409EFF;
    margin-top: 8px;
}
</style>
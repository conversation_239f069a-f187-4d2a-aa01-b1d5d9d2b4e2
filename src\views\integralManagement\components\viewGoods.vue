<!--
 * @Description:积分商城-查看
 * @Author: wangyy553
 * @Date: 2021-12-21 16:45:24
 * @LastEditors: wangyy553
 * @LastEditTime: 2022-01-17 15:42:55
-->
<template>
  <el-dialog
    :fullscreen="isFullscreen"
    :visible.sync="dialogVisible"
    append-to-body="true"
    :close-on-click-modal="false"
    top="100px"
    width="60%"
    @close="close()"
  >
    <div slot="title" class="header">
      <div class="avue-crud__dialog__header">
        <span class="el-dialog__title">{{ title }}</span>
        <div class="avue-crud__dialog__menu">
          <i
            @click="isFullScreen"
            class="el-dialog__close el-icon-full-screen"
          ></i>
        </div>
      </div>
      <button type="button" aria-label="Close" class="el-dialog__headerbtn">
        <i class="el-dialog__close el-icon el-icon-close"></i>
      </button>
    </div>
    <avue-form ref="form" v-model="detail" :option="option">
      <template slot="codeNum">
        <span>{{ detail.codeNum }}</span>
      </template>
      <template slot="name">
        <span>{{ detail.name }}</span>
      </template>
      <template slot="attachLink">
        <span><el-image :src="detail.attachLink"> </el-image></span>
      </template>
      <template slot="price">
        <span class="key-value">{{ detail.price }}</span> 积分
      </template>
      <template slot="marketPrice">
        <span class="key-value">{{ detail.marketPrice }}</span> ￥
      </template>
      <template slot="stock">
        <span class="key-value">{{ detail.stock }}</span>
        {{ detail.unitValue }} /
        <span class="key-value">{{ detail.saleNum }}</span>
        {{ detail.unitValue }}
      </template>
      <template slot="quota">
        <span class="key-value">{{ detail.quota }}</span> {{ detail.unitValue }}
      </template>
      <template slot="labelNames">
        <span
          v-for="(item, key) in detail.labelNames"
          :key="key"
          class="item-box"
          >{{ item }}
        </span>
      </template>
      <template slot="deptNames">
        <span v-for="(item, key) in detail.deptNames" :key="key"
          >{{ item }}
        </span>
      </template>

      <template slot="usageRule">
        <div v-html="detail.usageRule">
          {{ detail.usageRule }}
        </div>
      </template>
      <template slot="reminder">
        <span>{{ detail.reminder }}</span>
      </template>
    </avue-form>
  </el-dialog>
</template>

<script>
export default {
  props: ["visible", "detail"],
  data() {
    return {
      isFullscreen: false,
      title: "查看",
      option: {
        column: [
          { label: "商品编号", prop: "codeNum", span: 24 },
          {
            label: "商品名称",
            prop: "name",
            span: 24,
          },
          {
            label: "商品图片",
            prop: "attachLink",
            span: 24,
          },
          {
            label: "商品单价",
            prop: "price",
            span: 24,
          },
          {
            label: "市场参考价",
            prop: "marketPrice",
            span: 24,
          },
          {
            label: "库存/已兑换",
            prop: "stock",
            span: 24,
          },
          {
            label: "每人限购数",
            prop: "quota",
            span: 24,
          },
          {
            label: "商品属性",
            prop: "labelNames",
            span: 24,
          },
          {
            label: "商品上架组织范围",
            prop: "deptNames",
            span: 24,
          },
          {
            label: "使用规则",
            prop: "usageRule",
            span: 24,
          },
          {
            label: "温馨提示",
            prop: "reminder",
            span: 24,
          },
        ],
        labelPosition: "right",
        labelSuffix: "：",
        labelWidth: 150,
        menuBtn: true,
        submitBtn: false,
        emptyBtn: false,
        menuPosition: "center",
        tabs: false,
        detail: false,
      },
    };
  },
  methods: {
    close() {
      this.dialogVisible = false;
    },
    isFullScreen() {
      this.isFullscreen = !this.isFullscreen;
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
};
</script>

<style scoped lang='scss'>
.key-value {
  font-size: 18px;
  color: #f63832;
}
.item-box {
  margin: 0 5px;
}
</style>

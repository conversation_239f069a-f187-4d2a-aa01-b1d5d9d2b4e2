/**
* @Author: zhengjh43
* @Date: 2024-01-17 16:05:48
* @LastEditors: zhengjh43
* @LastEditTime: 2024-01-31 11:20:38
* @Explain: 积分录入
*/
<template>
  <el-row ref="test">
    <el-col v-if="span == 19" :span="5">
      <div class="box">
        <el-scrollbar>
          <basic-container v-loading="treeLoading">
            <avue-tree :option="treeOption" :data="treeData" :filter-node-method="filterNodeMethod"
              @node-click="nodeClick" />
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :span="span">
      <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :search.sync="query"
          :permission="permissionList" v-model="form" ref="crud" :before-open="beforeOpen" @row-update="rowUpdate"
          @row-save="rowSave" @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset"
          @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange"
          @refresh-change="refreshChange" @on-load="onLoad" class="directSupply-dialog">
          <!-- 表格顶部按钮 -->
          <template slot="menuLeft">
            <el-button v-if="permissionList.addBtn" type="primary" icon="el-icon-plus" size="small"
              @click.stop="$refs.crud.rowAdd()">新 增</el-button>
            <el-button v-if="permissionList.delBtn" type="danger" size="small" icon="el-icon-delete"
              @click="handleDelete">批量删除</el-button>
            <el-button v-if="permissionList.importBtn" type="success" size="small"
              @click="excelVisible = true">批量导入</el-button>
            <el-button v-if="permissionList.sendBtn" :disabled="grantBtnDisabled" type="primary" size="small"
              @click="openGrant">积分发放</el-button>
          </template>

          <!-- 表格列 -->
          <template slot="updateTime" slot-scope="{ row }">
            <span>{{ row.status === 1 ? row.updateTime : "" }}</span>
          </template>
          <template slot="updateUser" slot-scope="{ row }">
            <span>{{ row.status === 1 ? row.updateUser : "" }}</span>
          </template>
          <template slot="createDept" slot-scope="{ row }">
            <span>{{ row.status === 1 ? row.createDept : "" }}</span>
          </template>
          <template slot="realName" slot-scope="{ row }">
            <span>{{ row.status === 1 ? row.realName : "" }}</span>
          </template>

          <!-- 表格每行操作 -->
          <template slot-scope="{ type, size, row, index }" slot="menu">
            <el-button v-if="row.status == 3 && permissionList.viewBtn" :type="type" :size="size"
              @click.stop="$refs.crud.rowView(row, index)">查看</el-button>
            <template v-if="row.status == 0 || row.status == 3">
              <el-button v-if="permissionList.editBtn" :type="type" :size="size"
                @click="$refs.crud.rowEdit(row, index)">编辑</el-button>
              <el-button v-if="permissionList.delBtn" :type="type" :size="size"
                @click="$refs.crud.rowDel(row)">删除</el-button>
            </template>
            <el-button v-if="row.status === 1 && permissionList.revokeBtn" :type="type" :size="size"
              @click.stop="recall(row)">撤回</el-button>
          </template>
          <template slot-scope="{ row }" slot="status">
            <div :style="{ color: colorArr[row.status] }">● {{ row.statusName }}</div>
          </template>
        </avue-crud>
      </basic-container>
    </el-col>
    <!-- 积分批量导入 -->
    <el-dialog title="积分批量导入" append-to-body :visible.sync="excelVisible" width="555px"
      @close="$refs.excelForm.resetForm()">
      <avue-form ref="excelForm" :option="excelOption" v-model="excelForm" :upload-before="uploadBefore"
        :upload-after="uploadAfter" :upload-delete="uploadDelete" :upload-exceed="uploadExceed">
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载
            <i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>

    <!-- 积分发放 -->
    <el-dialog title="积分发放" append-to-body :visible.sync="grantVisible" width="555px">
      <avue-form ref="grantForm" :option="grantOption" v-model="grantForm" @submit="grantIntegral">
        <template slot="grantTip">
          <div style="text-align: center;color: red;">* 每月仅能发放一次，请谨慎操作。</div>
        </template>
      </avue-form>
      <div style="text-align: center;">
        <el-button @click="grantVisible = false">取 消</el-button>
        <el-button type="primary" @click="$refs.grantForm.submit()">发 放</el-button>
      </div>
    </el-dialog>

    <Uview :dialogVisible="uviewVisible" :changeVisible="changeUviewVisible" ref="uview" />
  </el-row>
</template>

<script>
import Uview from "@/components/uview/main.vue"
import { detail, getDeptTree, getDeptTreeFive } from "@/api/infoRelease/partyLead"
import * as api from "@/api/integralManagement/pointsEntry"
import { userKindList } from "@/api/system/user"
import { mapGetters } from "vuex"
import { exportBlob } from "@/api/common";
import { downloadXls } from "@/util/util";

export default {
  components: {
    Uview,
  },
  data () {
    const colorArr = ["gray", "#3FA3FF", "#D2E30E", "red"]
    return {
      span: 19,
      colorArr,
      userKind: userKindList.villager,
      deptId: "",
      treeLoading: false,
      treeOption: {
        nodeKey: "id",
        addBtn: false,
        menu: false,
        size: "small",
        props: {
          label: "title",
          value: "value",
          children: "children",
        },
      },
      treeData: [],
      releaseStatus: "",
      uviewVisible: false,
      bannerUrl: "",
      showBtnImg: true,
      noneBtnImg: false,
      limitCountImg: 1, //上传图片的最大数量
      srcList: [],
      tabLoading: true, //类别loading
      form: {},
      query: {},
      loading: true,
      box: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      type: "",
      selectionList: [],
      // viewDialog: false, //详情弹窗
      option: {
        // viewBtnText: "预览",
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        indexLabel: "序号",
        selection: true,
        selectable: (row, index) => row.status === 0 || row.status === 3,
        menuWidth: 200,
        labelWidth: 120,
        dialogWidth: 700,
        dialogClickModal: false,
        delBtn: false, //默认是有删除、编辑按钮
        editBtn: false,
        addBtn: false,
        dialogCustomClass: "directSupply-dialog",
        column: [
          {
            label: "姓名",
            prop: "name",
            width: 100,
            search: true,
            searchSpan: 6,
            searchOrder: 0,
            span: 24,
            type: "input",
            filterable: true,
            showWordLimit: true,
            minlength: 1,
            maxlength: 10,
            rules: [
              {
                required: true,
                whitespace: true,
                message: "请输入姓名",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "手机号",
            prop: "phone",
            width: 100,
            span: 24,
            type: "input",
            filterable: true,
            showWordLimit: true,
            minlength: 11,
            maxlength: 11,
            rules: [
              {
                required: true,
                whitespace: true,
                message: "请输入手机号",
                trigger: ["blur", "change"],
                pattern: /^1[3456789]\d{9}$/,
              },
            ],
          },
          {
            label: "积分值",
            prop: "integral",
            width: 100,
            span: 24,
            type: "number",
            min: -99999999,
            max: 99999999,
            precision: 0,
            rules: [
              {
                required: true,
                message: "请输入积分值",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "积分途径",
            prop: "approach",
            type: "textarea",
            minRows: 3,
            width: 100,
            span: 24,
            search: true,
            searchSpan: 6,
            searchOrder: 2,
            showWordLimit: true,
            minlength: 1,
            maxlength: 50,
            rules: [
              {
                required: true,
                whitespace: true,
                message: "请输入积分途径",
                trigger: ["blur", "change"],
              },
            ],
            viewDisplay: false,
            addDisplay: true,
            editDisplay: true,
          },
          {
            label: "积分途径",
            prop: "approach",
            span: 24,
            hide: true,
            viewDisplay: true,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "驳回意见",
            prop: "rejectReason",
            span: 24,
            hide: true,
            viewDisplay: true,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "录入时间",
            prop: "createTime",
            display: false,
          },
        ],
      },
      data: [],
      excelVisible: false,
      excelForm: {},
      excelOption: {
        menuBtn: false,
        column: [
          {
            label: "模板上传",
            prop: "excelFile",
            type: "upload",
            drag: true,
            showFileList: false,
            loadText: "模板上传中，请稍等",
            span: 24,
            // propsHttp: {
            //     res: "data",
            // },
            tip: "请上传 .xls,.xlsx 标准格式文件",
            action: "/api/admin/integral-input/import",
          },
          {
            label: "模板下载",
            prop: "excelTemplate",
            formslot: true,
            span: 24,
          },
        ],
      },

      grantVisible: false,
      grantForm: {},
      grantOption: {
        menuBtn: false,
        column: [
          {
            label: "请填写给当前组织下在网用户一键发放的积分值",
            labelPosition: "top",
            placeholder: "请输入积分",
            prop: "integral",
            type: "number",
            value: 50,
            min: 0,
            max: 999,
            precision: 0,
            span: 24,
            offset: 0,
            rules: [
              {
                required: true,
                message: "请输入正确的积分值",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "",
            labelWidth: 0,
            prop: "grantTip",
            span: 24,
          },
        ],
      },
      grantBtnDisabled: false, // 积分发放按钮禁用状态
    }
  },
  computed: {
    ...mapGetters(["permission", "website", "userInfo"]),
    permissionList () {
      return {
        addBtn: this.vaildData(this.permission.pointsEntry_add, false),
        viewBtn: this.vaildData(this.permission.pointsEntry_view, false),
        editBtn: this.vaildData(this.permission.pointsEntry_edit, false),
        delBtn: this.vaildData(this.permission.pointsEntry_del, false),
        importBtn: this.vaildData(this.permission.pointsEntry_import, false),
        sendBtn: this.vaildData(this.permission.pointsEntry_send, false),
        revokeBtn: this.vaildData(this.permission.pointsEntry_revoke, false),
      }
    },
    ids () {
      let ids = []
      this.selectionList.forEach((ele) => {
        ids.push(ele.id)
      })
      return ids.join(",")
    },
  },
  created () {
    // 判断权限，村干部不显示组织树
    // if (this.userInfo.role_name.indexOf('administrator') > -1 || this.userInfo.role_name.indexOf('admin') > -1 || this.userInfo.role_name.indexOf('townCadres') > -1) {
    //   this.span = 19
    this.treeLoading = true
    this.initTreeData(this.userInfo.tenant_id)
    // } else {
    //   this.span = 24
    // }

  },
  methods: {
    //Uview预览 start
    showUview () {
      this.uviewVisible = true
      this.$refs.uview.sendMessage(this.form.title, this.form.content)
    },
    changeUviewVisible () {
      this.uviewVisible = false
    },
    // 详情弹窗
    preview (row) {
      this.form = row
      this.queryDetail()
    },
    // 保存
    rowSave (row, done, loading) {
      console.log("rowsave", row)
      const { name, phone, integral, approach } = row
      const submitData = {
        name,
        phone,
        integral,
        approach,
      }
      api.add(submitData).then(
        async () => {
          this.$message.success(`积分录入新增成功`)
          await this.onLoad(this.page, this.query)
          done()
        },
        () => {
          loading()
        }
      )
    },
    // 修改
    rowUpdate (row, index, done, loading) {
      console.log("rowsave", row)
      const { name, phone, integral, approach, id } = row
      const submitData = {
        id,
        name,
        phone,
        integral,
        approach,
      }
      api.update(submitData).then(
        async () => {
          this.$message.success(`积分录入编辑成功`)
          await this.onLoad(this.page, this.query)
          done()
        },
        () => {
          loading()
        }
      )
    },
    // 删除
    rowDel (row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return api.remove({
            id: row.id,
          })
        })
        .then(() => {
          this.onLoad(this.page, this.query)
          this.$message({
            type: "success",
            message: "操作成功!",
          })
        })
    },
    // 重置
    searchReset () {
      this.query = {}
      this.releaseStatus = ""
      this.query.deptId = this.deptId
      this.onLoad(this.page)
    },
    // 搜索
    searchChange (params, done) {
      this.page.currentPage = 1
      const deptId = this.query.deptId
      this.query = params
      this.query.deptId = deptId
      this.onLoad(this.page, params)
      done()
    },
    // 勾选
    selectionChange (list) {
      this.selectionList = list
    },
    // 重置勾选
    selectionClear () {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    // 多选删除
    handleDelete () {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据")
        return
      }
      // if (this.selectionList.findIndex((item) => item.status === 1) !== -1) {
      //     this.$message.warning("存在已发布的数据,已发布的数据无法删除")
      //     return
      // }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return api.remove({
            id: this.ids,
          })
        })
        .then(() => {
          this.onLoad(this.page, this.query)
          this.$message({
            type: "success",
            message: "操作成功!",
          })
          this.$refs.crud.toggleSelection()
        })
    },

    // 发送审核
    handleSendAudit (row) {
      const id = row.id
      console.log("row.id", id)
      if (!id && this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据")
        return
      }
      this.$confirm(`确定${id ? "" : "一键"}发送审核?`, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return api.sendAudit({
            id: id || this.ids,
          })
        })
        .then(() => {
          this.onLoad(this.page, this.query)
          this.$message({
            type: "success",
            message: "操作成功!",
          })

          if (!id) {
            this.$refs.crud.toggleSelection()
          }
        })
    },

    // 查看详情
    queryDetail () {
      detail({
        id: this.form.id,
      }).then((res) => {
        this.form = res.data.data
        if (this.form.picture.length !== 0) {
          this.bannerUrl = this.form.picture[0].link
          this.srcList = [this.form.picture[0].link]
        }
        let fileList = []
        let idList = []

        this.form.attachVos &&
          Array.isArray(this.form.attachVos) &&
          this.form.attachVos.forEach((ele) => {
            fileList.push({
              label: ele.originalName,
              value: ele.link,
            })
            idList.push(ele.id)
          })
        this.form.fileList = fileList
        this.form.attachList = idList
        this.showUview()
      })
    },
    // 打开前回调
    async beforeOpen (done, type) {
      if (type === "add") {
        this.form = {}
        done()
      }
      if (["edit", "view"].includes(type)) {
        api.detail({
          id: this.form.id,
        }).then((res) => {
          this.form = res.data.data
          done()
        })
      }
    },

    // 当前页切换
    currentChange (currentPage) {
      this.page.currentPage = currentPage
    },
    // 页面显示条数切换
    sizeChange (pageSize) {
      this.page.pageSize = pageSize
    },
    // 刷新
    refreshChange () {
      this.onLoad(this.page, this.query)
    },
    // 首次加载
    async onLoad (page, params = {}) {
      this.loading = true
      let query = Object.assign(params, {
        ...this.query,
        current: this.page.currentPage,
        size: this.page.pageSize,
      });

      let res = await api.getList(query)
      if (res && res.data && res.data.success) {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      }
    },

    filterNodeMethod (value, data) {
      if (!value) return true
      const title = this.userKind === userKindList.villager ? data.deptName : data.deptName
      return title.indexOf(value.trim()) !== -1
    },
    nodeClick (data) {
      this.query.deptId = data.id
      this.deptId = data.id
      this.page.currentPage = 1
      this.onLoad(this.page, this.query)
      this.getIsGrant(data.id)
    },
    // 数村cv代码，祖传代码看不懂，注释了两行代码
    // getDeptTree和getDeptTreeFive接口一模一样
    // this.userKind === userKindList.villager必定为true
    async initTreeData (tenantId) {
      if (this.userKind === userKindList.villager) {
        // this.$set(this.treeOption.props, "label", "deptName")
        // this.$set(this.treeOption.props, "value", "key")
        this.treeData = (await getDeptTree(tenantId)).data.data
      } else this.treeData = (await getDeptTreeFive(tenantId)).data.data
      if (this.userKind !== userKindList.visitor) this.$emit("treeData", this.treeData)
      this.treeLoading = false
    },

    handleTemplate () {
      exportBlob(`/api/admin/integral-input/template`).then(res => {
        downloadXls(res.data, "积分导入数据模板.xlsx");
      })
    },

    // 上传前回调
    uploadBefore (file, done, loading) {
      const fileDir = file.name
      const suffix = fileDir.substr(fileDir.lastIndexOf("."))
      if (".xls" != suffix && ".xlsx" != suffix) {
        this.$message.error("文件上传格式错误")
        loading()
        return false
      }
      const size = 10
      const isSize = file.size / 1024 / 1024 < size
      if (!isSize) {
        this.$message.error(`文件上传大小不能超过 ${size}MB!`)
        loading()
        return false
      }
      done()
    },
    // 上传后执行操作
    uploadAfter (res, done, loading, column) {
      if (res && res.code == 200) {
        this.$message.success(res.msg)
        this.excelForm = {}
        this.excelVisible = false
        this.onLoad(this.page)
        done()
      } else {
        // this.$message.error(res.msg)
        setTimeout(() => {
          done()
        }, 500);
      }
    },

    // 删除已上传文件
    uploadDelete (file) {
      this.excelForm.excelFile.splice(file.uid, 1)
      this.noneBtnImg = this.excelForm.excelFile.length >= this.limitCountImg
    },
    // 上传限制
    uploadExceed (limit) {
      this.$message.error(`最多只能上传${limit}个文件`)
    },

    // 积分发放对话框
    openGrant () {
      if (this.deptId) {
        this.grantForm = {
          integral: 50,
        }
        this.grantVisible = true
      } else {
        this.$message.warning("请点击左侧选择积分发放应用范围")
      }
    },
    // 积分发放
    grantIntegral (row, done, loading) {
      const submitData = {
        deptId: this.deptId,
        integral: row.integral,
      }
      api.grantIntegral(submitData).then(
        async () => {
          this.$message.success(`积分发放成功`)
          this.grantVisible = false
          this.getIsGrant(this.deptId)
          done()
        },
        () => {
          // loading()
          done()
        }
      )
    },

    // 是否发放 积分发放每月仅能发放一次
    getIsGrant (id) {
      api.isGrant({ deptId: id }).then((res) => {
        this.grantBtnDisabled = res.data.data
      })
    },

    // 撤回
    recall (row) {
      this.$confirm("确定撤回审核?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return api.recall({
            id: row.id,
          })
        })
        .then(() => {
          this.onLoad(this.page, this.query)
          this.$message({
            type: "success",
            message: "操作成功!",
          })
        })
    },
  },
}
</script>
<style scoped lang='scss'>
.img-tiny {
  height: 100px;
  width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(245, 247, 250);
}

.btn-group-container {
  display: flex;
}

.add-award-row-btn {
  text-align: center;
  padding: 10px 0 5px 0;
}

::v-deep .dialogwidth {
  width: 700px;
}

.dialog-footer {
  text-align: center;
}
</style>
<!--
 * @Date: 2025-01-15 09:39:07
 * @LastEditors: linqh21
 * @LastEditTime: 2025-03-10 14:39:31
 * @Description:
 * @FilePath: \src\components\amap\MapContainer.vue
-->
<template>
  <div>
    <el-row :gutter="10">
      <el-col v-if="!readOnly" :span="10">
        <div class="tips">
          注：通过左键点击来进行绘制，右键点击结束，点击确认保存地图数据。
        </div>
      </el-col>
      <el-col v-if="!readOnly" :span="12" style="text-align: right; margin-bottom: 10px">
        <el-button type="warning" :disabled="editFlag" @click="editLayout">编辑</el-button>
        <el-button type="primary" :disabled="editEndFlag" @click="compileClose">结束编辑</el-button>
        <el-button type="success" @click="preservation">确认</el-button>
        <el-button type="danger" @click="clear">清除</el-button>
      </el-col>
    </el-row>
    <div id="container"></div>
  </div>
</template>
<script>
import AMapLoader from "@amap/amap-jsapi-loader";
import { isPolygonSelfIntersecting } from "./utils";
export default {
  name: "map-view",
  mounted () {
    console.log(this.mapData, "mapData");
    this.initAMap();
  },
  beforeDestroy () {
    console.log("destroy");
    if (this.map.destroy()) {
      this.map.destroy();
    }

  },
  props: {
    coordinateString: {
      type: String,
      default: "",
    },
    readOnly: {
      type: Boolean,
      default: false,
    },
    mapData: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data () {
    return {
      AMap: null,
      map: null,
      mouseTool: null,
      editFlag: true,
      polygonEditor: null,
      overlays: [],
      isCompile: false,
      editEndFlag: true,
    };
  },
  methods: {
    initAMap () {
      window._AMapSecurityConfig = {
        securityJsCode: "d747acb84a39e989d49e8d9aca53b573",
      };
      AMapLoader.load({
        key: "b3aa6f7ba8d5a0fc1a51a0ab420ddf7b", // 申请好的Web端开发者Key，首次调用 load 时必填
        version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: [
          "AMap.Scale",
          "AMap.MouseTool",
          "AMap.PolygonEditor",
          "AMap.ToolBar",
          "AMap.MapType",
        ], //需要使用的的插件列表，如比例尺'AMap.Scale'，支持添加多个如：['...','...']
      })
        .then((AMap) => {
          this.AMap = AMap;
          this.map = new AMap.Map("container", {
            // 设置地图容器id
            viewMode: "2D", // 是否为3D地图模式
            zoom: 16, // 初始化地图级别
            center: [117.402167, 26.33612978], // 初始化地图中心点位置
          });
          this.map.addControl(new AMap.ToolBar());
          this.map.addControl(new AMap.Scale());
          this.map.addControl(
            new AMap.MapType({
              defaultType: 1,
              showRoad: true,
            })
          );
          // this.polygonEditor = new AMap.PolygonEditor(this.map);
          // this.polygonEditor.on('add', (e) => {
          //   console.log(e.target, "add");
          //   this.tempPolygon = e.target;
          //   this.startFlag = true;
          //   this.deleteFlag = false;
          //   this.saveFlag = false;
          //   this.$message({
          //     message: '绘制完成',
          //     type: 'success'
          //   });
          // });
          this.mouseTool = new AMap.MouseTool(this.map);
          this.mouseTool.polygon({
            strokeColor: "#FF33FF",
            strokeWeight: 6,
            strokeOpacity: 0.6,
            fillColor: "#1791fc",
            fillOpacity: 0.4,
            // 线样式还支持 'dashed'
            strokeStyle: "solid",
            // strokeStyle是dashed时有效
            // strokeDasharray: [30,10],
          });
          this.mouseTool.on("draw", (e) => {
            console.log(e);
            this.editFlag = false;
            this.overlays = [];
            this.overlays.push(e.obj);
            this.mouseTool.close();
            this.$message({
              message: "绘制完成",
              type: "success",
            });
          });
          if (this.mapData && this.mapData.length > 0) {
            this.mouseTool.close();
            let path = this.mapData;
            // 创建多边形对象
            this.polygon = new this.AMap.Polygon({
              path: path, // 设置多边形路径
              strokeColor: "#FF33FF",
              strokeWeight: 6,
              strokeOpacity: 0.6,
              fillColor: "#1791fc",
              fillOpacity: 0.4,
              // 线样式还支持 'dashed'
              strokeStyle: "solid",
            });

            // 将多边形添加到地图上
            this.map.add(this.polygon);
            // 调整地图视野以适应多边形
            this.map.setFitView();
            // 初始化多边形编辑器（如果需要）
            this.PolygonEditor = new this.AMap.PolygonEditor(
              this.map,
              this.polygon
            );
            this.editFlag = false;
            // if(this.readOnly){
            //   this.mouseTool.close();
            // }
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    editLayout () {
      if (this.isCompile) {
        return;
      }
      this.isCompile = true;
      if (this.coordinateString === "") {
        console.log(
          "coordinateString",
          this.coordinateString,
          this.map.getAllOverlays("polygon")[0]
        );
        this.PolygonEditor = new this.AMap.PolygonEditor(
          this.map,
          this.map.getAllOverlays("polygon")[0]
        );
      } else {
        // var path = [
        //   [117.400525, 26.335718],
        //   [117.401813, 26.333602],
        //   [117.404538, 26.335025],
        //   [117.402006, 26.337564]
        // ];

        // // 创建多边形对象
        // this.polygon = new this.AMap.Polygon({
        //   path: path,  // 设置多边形路径
        //   strokeColor: "#FF33FF",
        //   strokeWeight: 6,
        //   strokeOpacity: 0.6,
        //   fillColor: '#1791fc',
        //   fillOpacity: 0.4,
        //   // 线样式还支持 'dashed'
        //   strokeStyle: "solid",
        // });

        // 将多边形添加到地图上
        this.map.add(this.polygon);
        // 调整地图视野以适应多边形
        this.map.setFitView();
        // 初始化多边形编辑器（如果需要）
        this.PolygonEditor = new this.AMap.PolygonEditor(
          this.map,
          this.polygon
        );
      }
      this.editEndFlag = false;
      this.editFlag = true;
      this.PolygonEditor.open();
      this.PolygonEditor.on("end", (event) => {
        this.isCompile = false;
        this.editEndFlag = true;
        this.editFlag = false;
        this.overlays = [];
        this.overlays.push(event.target);
      });
    },
    // 结束编辑
    compileClose () {
      if (this.PolygonEditor) {
        this.PolygonEditor.close();
      }
    },

    // 清除
    clear () {
      if (this.isCompile) {
        this.PolygonEditor.close();
      }
      if (this.PolygonEditor) {
        this.map.remove(this.PolygonEditor);
      }
      if (this.mouseTool) {
        this.map.remove(this.mouseTool);
      }
      this.map.remove(this.overlays);
      if (this.polygon) {
        this.map.remove(this.polygon);
      }
      this.newLayout();
      this.overlays = [];
      this.compileArr = [];
      this.editFlag = true;
    },

    // 重新绘制
    newLayout () {
      this.mouseTool.polygon({
        strokeColor: "#FF33FF",
        strokeWeight: 6,
        strokeOpacity: 0.6,
        fillColor: "#1791fc",
        fillOpacity: 0.4,
        // 线样式还支持 'dashed'
        strokeStyle: "solid",
        // strokeStyle是dashed时有效
        // strokeDasharray: [30,10],
      });
    },

    preservation () {
      const coordinate = [];
      this.overlays.forEach((item) => {
        item._opts.path.forEach((items) => {
          coordinate.push([items[0], items[1]]);
        });
      });
      if (this.overlays.length > 0) {
        if (coordinate.length <= 2) {
          this.$message({
            message: "请至少绘制三个点！",
            type: "warning",
          });
        } else {
          // console.log()
          if (isPolygonSelfIntersecting(coordinate)) {
            this.$message({
              message: "请不要绘制自相交的图形！",
              type: "warning",
            });
          } else {
            this.$message({
              message: "保存成功",
              type: "success",
            });
            this.$emit("coordinate", coordinate);
          }
        }
      } else {
        this.$emit('coordinate', this.compileArr)
        this.$message({
          message: "保存成功",
          type: "success",
        });
        // this.$message({
        //   message: "请先绘制图形！",
        //   type: "warning",
        // });
      }
    },
  },
};
</script>
<style scoped>
#container {
  width: 100%;
  height: 800px;
}
</style>

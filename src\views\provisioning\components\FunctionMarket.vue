<template>
  <div class="step-container">
    <div class="form-title">
      <div class="title-bar"></div>
      <span>快捷配置</span>
    </div>
    <div class="title-divider"></div>

    <div class="function-market">
      <div class="quick-config">
        <div class="quick-buttons">
          <el-button
            v-for="(item, index) in itemList"
            :key="index"
            :disabled="isViewMode"
            @click="selectItem(item.id)">
            {{ item.name }}
            <!-- <i v-if="selectedItem === item.value" class="el-icon-check check-icon"></i> -->
          </el-button>
        </div>
      </div>
    </div>

    <div class="form-title">
      <div class="title-bar"></div>
      <span>手动选择</span>
    </div>
    <div class="title-divider"></div>
    <div class="config-content">
      <div class="platform-container">
        <div class="platform-section">
          <div class="platform-header">
            <span class="dot"></span>
            <span>PC端</span>
          </div>
          <div class="menu-tree-container">
            <div class="tree-header">功能列表</div>
            <div class="search-box">
              <el-input v-model="filterWebText" :disabled="isViewMode" placeholder="请输入菜单名称" size="small" clearable />
            </div>
            <div style="height:290px;overflow-y:auto;border-radius:4px;padding:8px;">
              <el-tree
                v-loading="menuWebIdTreeLoading"
                :data="menuWebIdTree"
                node-key="id"
                show-checkbox
                :default-checked-keys="defaultMenuWebId"
                :props="{ label: 'title', children: 'children', value: 'id' }"
                ref="webMenuTree"
                @check="menuWebIdTreeChange"
                :filter-node-method="filterWebMenuNode"
              />
            </div>
          </div>
        </div>

        <div class="platform-section">
          <div class="platform-header">
            <span class="dot"></span>
            <span>小程序端</span>
          </div>
          <div class="menu-tree-container">
            <div class="tree-header">功能列表</div>
            <div class="search-box">
              <el-input v-model="filterAppText" :disabled="isViewMode" placeholder="请输入应用名称" size="small" clearable />
            </div>
            <div style="height:290px;overflow-y:auto;border-radius:4px;padding:8px;">
              <el-tree
                v-loading="menuMiniIdTreeLoading"
                :data="menuMiniIdTree"
                node-key="id"
                :disabled=isViewMode
                show-checkbox
                :default-checked-keys="defaultMenuMiniId"
                :props="{ label: 'title', children: 'children', value: 'id',disabled:'disabled' }"
                ref="miniMenuTree"
                @check="menuMiniIdTreeChange"
                :filter-node-method="filterMiniMenuNode"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="info-panel">
        <div class="info-header">
          <i class="el-icon-info"></i>
          <span>说明</span>
        </div>
        <div class="info-content">
          <!-- <p>1、<el-checkbox disabled></el-checkbox>表示该功能为镇村联动功能，需要镇级开通该功能后，村级才可使用。</p> -->
          <p>PC端与小程序端部分功能为绑定关系，勾选或取消勾选其中一端时，另一端同步更新。</p>
        </div>
      </div>
    </div>

    <div class="actions-divider"></div>

    <div class="action-buttons">
      <div></div>
      <div class="step-buttons">
        <el-button @click="$emit('cancel')">{{ isViewMode ? '关闭' : '取消' }}</el-button>
        <el-button v-if="!isViewMode" @click="save">保存</el-button>
        <el-button @click="prev">上一步</el-button>
        <el-button type="primary" @click="next">下一步</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getMenuTree } from "@/api/system/menu";
import { getListAll,getDetail,getDisabledLinkageMenuId } from '@/api/system/new_tenantpackage'
const getAllAssociatedMenuIds = (treeData) => {
  const result = new Set(); // 使用 Set 自动去重
  function traverse(node) {
    if (node.associatedMenuIds && node.associatedMenuIds.length > 0) {
      node.associatedMenuIds.forEach(id => result.add(id));
    }
    if (node.hasChildren && node.children.length > 0) {
      node.children.forEach(child => traverse(child));
    }
  }

  // 处理单节点或数组形式的输入
  if (Array.isArray(treeData)) {
    treeData.forEach(node => traverse(node));
  } else {
    traverse(treeData);
  }

  return Array.from(result); // 转为数组返回
}
export default {
  props: {
     isViewMode: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      webMenuId: [],
      appMenuId: [],
      menuWebIdTree: [],
      defaultMenuWebId:[],
      menuWebIdTreeLoading: false,
      menuMiniIdTree: [],
      defaultMenuMiniId:[],
      menuMiniIdTreeLoading: false,
      filterWebText:"",
      filterAppText:"",
      itemList: [],
      selectedItem: 'town_standard',
      tenantForm:{},
      disabledIds:[],
    };
  },
  created() {
    console.log(this.formData,"functionMarket")
    if (this.formData) {
      this.tenantForm = { ...this.formData };
      if(this.formData.webMenuId){
        this.defaultMenuWebId = this.formData.webMenuId.split(',')
        this.webMenuId = this.formData.webMenuId.split(',')
      }
      if(this.formData.appMenuId) {
        this.defaultMenuMiniId = this.formData.appMenuId.split(',')
        this.appMenuId = this.formData.appMenuId.split(',')
      }
    }
    this.initData()
  },
  watch: {
    filterWebText(val) {
     this.$refs.webMenuTree && this.$refs.webMenuTree.filter(val);
    },
    filterAppText(val) {
      this.$refs.miniMenuTree && this.$refs.miniMenuTree.filter(val);
    }

  },
  methods: {
    initData() {
      const setDisabled = (nodes) => {
            return nodes.map(item => {
              const newItem = { ...item, disabled: true };
              if (newItem.children && newItem.children.length > 0) {
                newItem.children = setDisabled(newItem.children);
              }
              return newItem;
            });
          };
      this.menuWebIdTreeLoading = true;
      this.menuMiniIdTreeLoading = true;
      getMenuTree(1).then(res => {
        // const column = this.findObject(this.option.column, "menuWebId");
        this.menuWebIdTree = res.data.data;
        this.menuWebIdTreeLoading = false;
        // column.dicData = res.data.data;
        if(this.isViewMode){
          this.menuWebIdTree = setDisabled(this.menuWebIdTree);
        }
      }).finally(() => {
        this.menuWebIdTreeLoading = false;
      });
      getMenuTree(2).then(res => {
        // const column = this.findObject(this.option.column, "menuMiniId");
        // column.dicData = res.data.data;
        this.menuMiniIdTree = res.data.data;
        // 递归设置disabled
        if(this.isViewMode){
          this.menuMiniIdTree = setDisabled(this.menuMiniIdTree);
        }
        this.menuMiniIdTreeLoading = false;
      }).finally(() => {
        this.menuMiniIdTreeLoading = false;
      });
      getListAll().then(res=>{
        // console.log(res)
        this.itemList = res.data.data
      })
      //  this.$nextTick(() => {
      //       this.webMenuId = this.$refs.webMenuTree.getCheckedKeys()
      //       this.appMenuId = this.$refs.miniMenuTree.getCheckedKeys()
      //     })
      // if(this.formData.id){
      //   getDisabledLinkageMenuId(this.formData.id).then(res=>{
      //     console.log(res)
      //   })
      // }

    },
    save() {
      // this.$emit('save', {
      //   webMenuId: this.webMenuId,
      //   appMenuId: this.appMenuId,
      // });
      // console.log(this.webMenuId,this.appMenuId)
      // if(this.webMenuId.length==0){
      //   this.$message.warning("请选择PC功能");
      //   return;
      // }
      // if(this.appMenuId.length==0){
      //   this.$message.warning("请选择小程序功能");
      //   return;
      // }
      this.tenantForm.webMenuId = this.webMenuId.join(",")
      this.tenantForm.appMenuId = this.appMenuId.join(",")
      this.$emit('save', this.tenantForm,"functionMarket");
      // this.$message.success('功能配置保存成功');
    },
    prev(){
      // console.log("prev", this.webMenuId, this.appMenuId)
      this.tenantForm.webMenuId = this.webMenuId.join(",")
      this.tenantForm.appMenuId = this.appMenuId.join(",")
      this.$emit('prev', this.tenantForm,"functionMarket");
    },
    next(){
      this.tenantForm.webMenuId = this.webMenuId.join(",")
      this.tenantForm.appMenuId = this.appMenuId.join(",")
      this.$emit('next', this.tenantForm,"functionMarket");
    },
    filterWebMenuNode(value, data) {
      if (!value) return true;
      return data.title.indexOf(value.trim()) !== -1;
    },
    filterMiniMenuNode(value, data) {
      if (!value) return true;
      return data.title.indexOf(value.trim()) !== -1;
    },

    selectItem(value) {
      this.selectedItem = value;
      // 这里可以添加选择不同配置后的逻辑
      // 例如加载对应的预设功能列表
      this.$refs.webMenuTree.setCheckedKeys([])
      this.$refs.miniMenuTree.setCheckedKeys([])
      this.defaultMenuMiniId = []
      getDetail(value).then(res => {
          this.defaultMenuWebId = res.data.data.webMenuIds
          this.defaultMenuMiniId = res.data.data.appMenuIds
          this.$nextTick(() => {
            this.webMenuId = this.$refs.webMenuTree.getCheckedKeys()
            this.appMenuId = this.$refs.miniMenuTree.getCheckedKeys()
          })
        });

    },
    menuWebIdTreeChange(data) {
      // console.log(data, node);
      let check = !this.webMenuId.includes(data.id)
      let associatedMenuIds = []
      associatedMenuIds = getAllAssociatedMenuIds(data)
      // console.log(associatedMenuIds)
      if (check) {
        // this.$refs.menuWebIdTree.setChecked(data.associatedMenuIds, true)

        for (let i = 0; i < associatedMenuIds.length; i++) {
          this.$refs.miniMenuTree.setChecked(associatedMenuIds[i], true)
        }
      } else {
        // this.$refs.menuWebIdTree.setChecked(data.associatedMenuIds, false)
        for (let i = 0; i < associatedMenuIds.length; i++) {
          this.$refs.miniMenuTree.setChecked(associatedMenuIds[i], false)
        }
      }

      this.$nextTick(() => {
        this.webMenuId = this.$refs.webMenuTree.getCheckedKeys()
        this.appMenuId = this.$refs.miniMenuTree.getCheckedKeys()
      })

    },
    menuMiniIdTreeChange(data) {
      let check = !this.appMenuId.includes(data.id)
      let associatedMenuIds = []
      associatedMenuIds = getAllAssociatedMenuIds(data)
      // console.log(associatedMenuIds)
      if (check) {
        // this.$refs.menuWebIdTree.setChecked(data.associatedMenuIds, true)
        for (let i = 0; i < associatedMenuIds.length; i++) {
          this.$refs.webMenuTree.setChecked(associatedMenuIds[i], true)
        }
      } else {
        // this.$refs.menuWebIdTree.setChecked(data.associatedMenuIds, false)
        for (let i = 0; i < associatedMenuIds.length; i++) {
          this.$refs.webMenuTree.setChecked(associatedMenuIds[i], false)
        }
      }

      this.$nextTick(() => {
        this.webMenuId = this.$refs.webMenuTree.getCheckedKeys()
        this.appMenuId = this.$refs.miniMenuTree.getCheckedKeys()
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.step-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.step-content-area {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 20px;
}
.form-title {
  display: flex;
  align-items: center;

  .title-bar {
    width: 4px;
    height: 20px;
    background-color: #409eff;
    margin-right: 8px;
  }

  span {
    font-size: 16px;
    font-weight: 500;
  }
}

.title-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 15px 0 20px 0;
}

.actions-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 20px -20px 20px -20px;
}

.action-buttons {
  display: flex;
  justify-content: end;
  align-items: center;
  width: 100%;

  .step-buttons {
    display: flex;
    gap: 10px;

    .el-button {
      min-width: 80px;
    }
  }
}
.function-market {
  .quick-config {
    margin-bottom: 20px;
    min-height: 35px;

    .quick-buttons {
      display: flex;
      gap: 12px;

      .quick-btn {
        // flex: 1;
        padding: 8px 12px;
        border: 1px solid #d9d9d9;
        background: #fff;
        color: #000;
        font-size: 13px;
        cursor: pointer;
        border-radius: 6px;
        position: relative;
        transition: all 0.3s;
        min-height: 36px;
        width: 110px;

        &.active {
          background: #e6f7ff;
          color: #1890ff;
          border-color: #91d5ff;
        }

        &:hover:not(.active) {
          border-color: #b3d8ff;
        }

        .check-icon {
          position: absolute;
          right: 4px;
          bottom: 4px;
          color: #1890ff;
          background: #fff;
          border-radius: 50%;
          font-size: 12px;
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}

.config-content {
  display: flex;
  gap: 20px;

  .platform-container {
    display: flex;
    flex: 1;
    gap: 20px;

    .platform-section {
      flex: 1;

      .platform-header {
        display: flex;
        align-items: center;
        font-size: 14px;
        color: #333;
        margin-bottom: 8px;

       .dot {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #409eff;
          margin-right: 4px;
        }
      }

      .menu-tree-container {
        border: 1px solid #ededed;
        border-radius: 4px;
        background: white;
        box-shadow: 0 0 10px 0 #2c74af1f;


        .tree-header {
          padding: 8px 12px;
          background: #fff;
          border-top-left-radius: 15px;
          border-top-right-radius: 15px;
          border-bottom: 1px solid #e4e7ed;
          font-size: 14px;
          color: #000;
        }

        .search-box {
          padding: 8px 12px;
          // border-bottom: 1px solid #e4e7ed;

          .el-input {
            .el-input__inner {
              font-size: 12px;
              height: 28px;
              line-height: 28px;
            }
          }
        }

        .tree-content {
          padding: 8px 0;
          max-height: 300px;
          overflow-y: auto;

          .menu-group {
            .group-header {
              display: flex;
              align-items: center;
              padding: 4px 12px;
              font-size: 13px;
              color: #333;
              cursor: pointer;

              i {
                margin-right: 6px;
                font-size: 12px;
                color: #909399;
              }

              &:hover {
                background: #f5f7fa;
              }
            }

            .group-items {
              .menu-item {
                padding: 2px 32px;
                font-size: 12px;

                .item-text {
                  color: #909399;
                }

                .el-checkbox {
                  font-size: 12px;

                  .el-checkbox__label {
                    color: #409EFF;
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .info-panel {
    width: 50%;
    background: #f0f9ff;
    border: 1px solid #b3d8ff;
    border-radius: 4px;
    padding: 12px;
    height: 85px;

    .info-header {
      display: flex;
      align-items: center;
      font-size: 13px;
      font-weight: 500;
      color: #409EFF;
      margin-bottom: 8px;

      i {
        margin-right: 4px;
        font-size: 14px;
      }
    }

    .info-content {
      font-size: 12px;
      color: #666;
      line-height: 1.5;

      p {
        margin: 0 0 6px 0;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

</style>





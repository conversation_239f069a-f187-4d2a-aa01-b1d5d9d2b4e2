<!--
 * @Date: 2025-07-31 21:50:00
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-31 21:50:00
 * @Description: 便民服务组件
 * @FilePath: \src\views\system\appConfigComponents\ConvenientService.vue
-->
<template>
  <div class="convenient-service-container">
    <div class="convenient-service-title">便民服务</div>
    <div class="convenient-service-cards">
      <div class="service-card work-instructions">
        <div class="service-card-content">
          <div class="service-card-title">办事指南</div>
        </div>
      </div>
      <div class="service-card discount-policy">
        <div class="service-card-content">
          <div class="service-card-title">涉农政策</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConvenientService'
}
</script>

<style scoped>
/* ================== 便民服务样式 ================== */
.convenient-service-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  padding: 12px;
  box-sizing: border-box;
}

.convenient-service-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  text-align: left;
  padding-left: 2px;
}

.convenient-service-cards {
  display: flex;
  gap: 6px;
  flex: 1;
}

.service-card {
  flex: 1;
  border-radius: 5px;
  position: relative;
  overflow: hidden;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: flex-end;
  min-height: 75px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.service-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.service-card.work-instructions {
  background-image: url("~@/assets/appConfig/work-instructions-bg.png");
  background-blend-mode: overlay;
}

.service-card.discount-policy {
  background-image: url("~@/assets/appConfig/discount-policy-bg.png");
  background-blend-mode: overlay;
}

.service-card-content {
  width: 100%;
  padding: 8px 12px;
}

.service-card-title {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  line-height: 3;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .convenient-service-container {
    padding: 8px;
  }

  .convenient-service-title {
    font-size: 14px;
    margin-bottom: 6px;
  }

  .service-card {
    min-height: 60px;
    border-radius: 12px;
  }

  .service-card-content {
    padding: 6px 8px;
  }

  .service-card-title {
    font-size: 12px;
    line-height: 1.2;
  }
}
</style>

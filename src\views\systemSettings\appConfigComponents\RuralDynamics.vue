<!--
 * @Date: 2025-07-31 21:50:00
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-31 21:50:00
 * @Description: 村镇动态组件
 * @FilePath: \src\views\system\appConfigComponents\RuralDynamics.vue
-->
<template>
  <div class="rural-dynamics-container">
    <div class="rural-dynamics-header">
      <div class="rural-dynamics-title">村镇动态</div>
    </div>
    <div class="rural-dynamics-content">
      <div class="news-item">
        <div class="news-image">
          <img src="@/assets/appConfig/swiper-def.png" alt="新闻图片" />
        </div>
        <div class="news-info">
          <div class="news-title">中国气象局新闻发布会：2月...</div>
          <div class="news-meta">
            <span class="news-source">四季洋房</span>
            <span class="news-time">2023-02-07 15:48:19</span>
          </div>
        </div>
      </div>
      <div class="news-item">
        <div class="news-image">
          <img src="@/assets/appConfig/swiper-def.png" alt="新闻图片" />
        </div>
        <div class="news-info">
          <div class="news-title">中国气象局新闻发布会：2月...</div>
          <div class="news-meta">
            <span class="news-source">四季洋房</span>
            <span class="news-time">2023-02-07 15:48:19</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'RuralDynamics'
}
</script>

<style scoped>
/* ================== 村镇动态样式 ================== */
.rural-dynamics-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  padding: 12px;
  box-sizing: border-box;
}

.rural-dynamics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.rural-dynamics-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.rural-dynamics-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.news-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  border-radius: 6px;
  background: #fafafa;
  transition: background 0.2s ease;
}

.news-item:hover {
  background: #f0f8ff;
}

.news-image {
  width: 60px;
  height: 45px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.news-title {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #999;
}

.news-source {
  background: #ff6b6b;
  color: #fff;
  padding: 1px 6px;
  border-radius: 3px;
  font-size: 10px;
}

.news-time {
  font-size: 10px;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .rural-dynamics-container {
    padding: 8px;
  }

  .rural-dynamics-title {
    font-size: 14px;
  }

  .news-item {
    padding: 6px;
  }

  .news-image {
    width: 50px;
    height: 38px;
  }

  .news-title {
    font-size: 12px;
  }

  .news-meta {
    font-size: 10px;
  }
}
</style>

/*
 * @Description:
 * @Author: wangyy553
 * @Date: 2021-10-26 17:03:09
 * @LastEditors: wangyy553
 * @LastEditTime: 2021-12-29 17:00:37 */
import request from '@/router/axios'

/**
 * @description: 获得列表
 * @param {int}deptId
 * @param {int}current
 * @param {int}size
 * @param {object}account
 */
export const getDataList = (deptId, current, size, descs, ascs, account) => {
  return request({
    url: '/api/integral/admin/page',
    method: 'get',
    params: {
      deptId,
      current,
      size,
      ...account,
      descs,
      ascs
    }
  })
}

/**
 * @description: 获得积分详情
 * @param {int}current
 * @param {int}size
 * @param {int}userId
 */
export const getRecord = (current, size, userId) => {
  return request({
    url: '/api/integral/admin/detail-record',
    method: 'get',
    params: {
      userId,
      current,
      size
    }
  })
}


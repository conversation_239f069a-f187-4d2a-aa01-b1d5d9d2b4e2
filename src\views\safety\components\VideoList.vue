<template>
  <basic-container>
  <div class="video-list-container">
    <avue-crud
      :option="option"
      :data="deviceList"
      :table-loading="loadingDevices"
      :page.sync="page"
      :search.sync="search"
      ref="crud"
      v-model="form"
      @search-change="searchChange"
      @search-reset="searchReset"
      @refresh-change="refreshChange"
      @on-load="onLoad"
      :before-open="beforeOpen"
    >
      <!-- 设备状态插槽 -->
      <template slot-scope="{ row }" slot="state">
        <el-tag :type="getStatusType(row.state)">
          {{ getStatusText(row.state) }}
        </el-tag>
      </template>

      <!-- 操作按钮插槽 -->
      <template slot="menu" slot-scope="{row}">
        <el-button
          type="text"
          size="mini"
          @click="previewDevice(row)"
          icon="el-icon-video-play"
          :disabled="row.state !== 2"
        >
          预览
        </el-button>
      </template>
    </avue-crud>

    <!-- 视频预览弹窗 -->

  </div>
  <el-dialog
      :visible.sync="previewDialogVisible"
      :title="`视频预览 - ${currentPreviewDevice.deviceName || '未命名设备'}`"
      width="60%"
      :close-on-click-modal="false"
      append-to-body
      @close="handlePreviewClose"
    >
      <div class="preview-content" >
        <!-- 视频播放区域 -->
        <div class="video-container">
          <MultiSourcePlayer
            v-if="previewDialogVisible && currentPreviewDevice.id"
            :key="currentPreviewDevice.id + '-' + (currentPreviewDevice.refreshKey || '')"
            :device-name="currentPreviewDevice.deviceName || '未命名设备'"
            :sources="currentPreviewDevice.srcList || {}"
            :initial-source-type="getDefaultSourceType(currentPreviewDevice)"
            :device-status="currentPreviewDevice.state || ''"
            @request-source="() => fetchDeviceSource(currentPreviewDevice)"
            @error="handlePlayerError"
            :is-expanded="false"
            style="width: 100%; height: 100%;"
          />
        </div>

        <!-- 方向控制台 -->
        <div class="control-panel">
          <div class="control-title">方向控制</div>
          <div class="direction-control">
            <!-- 上方向 -->
            <div class="direction-btn direction-up" @click="handleDirection('up')">
              <i class="el-icon-caret-top"></i>
            </div>

            <!-- 左右方向 -->
            <div class="direction-row">
              <div class="direction-btn direction-left" @click="handleDirection('left')">
                <i class="el-icon-caret-left"></i>
              </div>
              <div class="direction-center">
                <div class="center-circle"></div>
              </div>
              <div class="direction-btn direction-right" @click="handleDirection('right')">
                <i class="el-icon-caret-right"></i>
              </div>
            </div>

            <!-- 下方向 -->
            <div class="direction-btn direction-down" @click="handleDirection('down')">
              <i class="el-icon-caret-bottom"></i>
            </div>
          </div>
        </div>
        <div>
          语音对接区域
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="previewDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import { getList, getVideoDetail,videoControl } from "@/api/system/video";
import MultiSourcePlayer from "@/components/video-player/MultiSourcePlayer.vue";

export default {
  name: "VideoList",
  components: {
    MultiSourcePlayer
  },
  props: {
    deptId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      form: {},
      search: {},
      loadingDevices: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      deviceList: [],
      query: {},
      // 预览弹窗相关
      previewDialogVisible: false,
      currentPreviewDevice: {},
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 4,
        border: true,
        index: true,
        selection: false,
        addBtn: false,
        viewBtn: true,
        editBtn: false,
        delBtn: false,
        dialogClickModal: false,
        labelWidth: 120,
        column: [
          {
            label: '设备名称',
            prop: 'deviceName',
            search: true,
            searchSpan: 6,
            minWidth: 150,
          },
          {
            label: '设备状态',
            prop: 'state',
            slot: true,
            type: 'select',
            searchSpan: 4,
            width: 100,
            dicData: [
              { label: '未注册', value: 1 },
              { label: '在线', value: 2 },
              { label: '离线', value: 3 },
            ],
            search: true
          },
          {
            label: '设备类型',
            prop: 'deviceType',
            type: 'select',
            width: 120,
            dicData: [
              { label: '国标摄像头', value: 1 },
              { label: 'RTMP 摄像头', value: 2 },
              { label: '国标平台', value: 3 },
              { label: 'NVR 设备', value: 4 },
            ],
          },
          {
            label: '绑定部门',
            prop: 'deptName',
            minWidth: 120,
          },
          {
            label: '安装地址',
            prop: 'gbAddress',
            minWidth: 150,
            overHidden: true,
          },
          {
            label: '设备厂商',
            prop: 'manufacturer',
            hide: true,
          },
          {
            label: '设备描述',
            prop: 'deviceDescription',
            hide: true,
          },
          {
            label: '更新时间',
            prop: 'updateTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            width: 160,
          },
        ]
      }
    };
  },
  watch: {
    deptId: {
      handler(newDeptId) {
        if (newDeptId) {
          this.page.currentPage = 1;
          this.search = {};
          this.query = {};
          this.onLoad(this.page);
        } else {
          this.deviceList = [];
          this.page.total = 0;
        }
      },
      immediate: true
    }
  },
  methods: {
    // 数据加载
    async onLoad(page, params = {}) {
      if (!this.deptId) {
        this.deviceList = [];
        this.page.total = 0;
        return;
      }

      this.loadingDevices = true;
      try {
        const res = await getList(page.currentPage, page.pageSize, {
          deptId: this.deptId,
          ...params
        });

        if (res && res.data.success) {
          this.deviceList = res.data.data.records.map(device => ({
            ...device,
            srcList: {},
            refreshKey: Date.now() + '-' + Math.random()
          }));
          this.page.total = res.data.data.total || 0;
        }
      } catch (error) {
        this.handleLoadError(error);
      } finally {
        this.loadingDevices = false;
      }
    },

    // 搜索变化
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params).then(() => done());
    },

    // 搜索重置
    searchReset() {
      this.search = {};
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },

    // 刷新
    refreshChange() {
      this.onLoad(this.page, this.query);
    },

    // 处理加载错误
    handleLoadError(error) {
      console.error('Failed to load video devices:', error);

      let errorMessage = '获取设备列表失败';
      if (error && error.response && error.response.status === 404) {
        errorMessage = '该部门下暂无设备';
      } else if (error && error.response && error.response.status === 403) {
        errorMessage = '没有权限访问该部门的设备';
      } else if (error && error.code === 'NETWORK_ERROR') {
        errorMessage = '网络连接失败，请检查网络状态';
      }

      this.$message({
        message: errorMessage,
        type: 'error',
        duration: 4000,
        showClose: true
      });

      this.deviceList = [];
      this.page.total = 0;
    },

    // 获取默认视频源类型
    getDefaultSourceType(device) {
      if (!device.srcList) return 'flv';
      const priority = ['flv', 'ws_flv', 'fmp4', 'ws_fmp4', 'hls'];
      return priority.find(type => device.srcList[type]) || Object.keys(device.srcList)[0];
    },

    // 获取设备视频源
    async fetchDeviceSource(device, forceRefresh = false) {
      if (device.state !== 2) {
        this.$message.warning('设备已离线，无法获取视频源');
        return;
      }

      if (forceRefresh) {
        this.$set(device, 'srcList', {});
      } else if (Object.keys(device.srcList).length > 0) {
        return;
      }

      this.$set(device, 'loadingSource', true);
      try {
        const res = await getVideoDetail(device.id);
        this.$set(device, 'srcList', res.data.data.playOnRes);
      } catch (error) {
        console.error('Failed to fetch device source:', error);

        let errorMessage = '获取视频源失败';
        if (error && error.response && error.response.status === 404) {
          errorMessage = '设备视频源不存在';
        } else if (error && error.response && error.response.status === 500) {
          errorMessage = '服务器内部错误，请稍后重试';
        } else if (error && error.code === 'TIMEOUT') {
          errorMessage = '获取视频源超时，请重试';
        }

        this.$message({
          message: `${device.deviceName}: ${errorMessage}`,
          type: 'warning',
          duration: 4000
        });

        throw error;
      } finally {
        this.$set(device, 'loadingSource', false);
      }
    },

    // 处理播放器错误
    handlePlayerError(error) {
      console.error('Player error:', error);

      const friendlyMessage = (error && error.friendlyMessage) || '视频播放出错';
      this.$message({
        message: friendlyMessage,
        type: 'error',
        duration: 3000,
        showClose: true
      });

      if (error && error.message && (error.message.includes('Network') || error.message.includes('Timeout'))) {
        this.$message({
          message: '建议检查网络连接后重试',
          type: 'info',
          duration: 5000
        });
      }
    },

    // 状态类型
    getStatusType(state) {
      const typeMap = {
        1: 'warning',  // 未注册
        2: 'success',  // 在线
        3: 'danger'    // 离线
      };
      return typeMap[state] || 'info';
    },

    // 状态文本
    getStatusText(state) {
      const textMap = {
        1: '未注册',
        2: '在线',
        3: '离线'
      };
      return textMap[state] || '未知';
    },

    // 预览设备
    async previewDevice(row) {
      if (row.state !== 2) {
        this.$message.warning('设备已离线，无法预览');
        return;
      }

      this.currentPreviewDevice = { ...row };
      this.previewDialogVisible = true;

      // 如果没有视频源，先获取
      if (!row.srcList || Object.keys(row.srcList).length === 0) {
        try {
          await this.fetchDeviceSource(this.currentPreviewDevice);
        } catch (error) {
          console.error('获取视频源失败:', error);
        }
      }
    },

    // 关闭预览弹窗
    handlePreviewClose() {
      this.currentPreviewDevice = {};
    },

    // 方向控制
    handleDirection(direction) {
      console.log('方向控制:', direction, '设备:', this.currentPreviewDevice);
      // return
      // 这里可以调用设备的方向控制API
      // 示例：调用云台控制接口
      // this.$message.info(`控制设备 ${this.currentPreviewDevice.deviceName} 向${this.getDirectionText(direction)}移动`);
      let data = {
        deviceId: this.currentPreviewDevice.deviceId,
        channelCodeId: this.currentPreviewDevice.channelCodeId,
        horizontal: direction == 'right' ? 2 : direction == 'left' ? 1 : 0,
        vertical: direction == 'down' ? 2 : direction == 'up' ? 1 : 0,
      };
      videoControl(data).then(res=>{
        console.log(res);
      })
    },

    // 获取方向文本
    // getDirectionText(direction) {
    //   const directionMap = {
    //     'up': '上',
    //     'down': '下',
    //     'left': '左',
    //     'right': '右'
    //   };
    //   return directionMap[direction] || '未知';
    // },

    // 详情查看前的处理
    beforeOpen(done, type) {
      if (["view"].includes(type)) {
        // 可以在这里获取设备详细信息
        done();
      } else {
        done();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.video-list-container {
  padding: 16px;
  height: 100%;

  // 预览弹窗样式


  // 状态标签样式
  .el-tag {
    font-weight: 500;
  }

  // 操作按钮样式
  .el-button--text {
    margin-right: 8px;

    &:disabled {
      color: #c0c4cc;
      cursor: not-allowed;
    }

    &:last-child {
      margin-right: 0;
    }
  }
}
.preview-content {
    display: flex;
    flex-direction: row;
    gap: 15px;
    align-items: flex-start;
    min-height: 500px;
    justify-content: flex-start;

    .video-container {
      width: 800px;
      height: 500px;
      background: #000;
      border-radius: 8px;
      overflow: hidden;
      flex-shrink: 0;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .control-panel {
      width: 180px;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      flex-shrink: 0;

      .control-title {
        font-size: 15px;
        font-weight: 600;
        color: #495057;
        margin-bottom: 15px;
        text-align: center;
        width: 100%;
        padding-bottom: 8px;
        border-bottom: 1px solid #dee2e6;
      }

      .direction-control {
        width: 140px;
        height: 140px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        background: #fff;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        .direction-btn {
          width: 40px;
          height: 40px;
          background: #f8f9fa;
          border: 2px solid #dee2e6;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;

          i {
            font-size: 18px;
            color: #6c757d;
            font-weight: bold;
          }

          &:hover {
            background: #409eff;
            border-color: #409eff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);

            i {
              color: #fff;
            }
          }

          &:active {
            transform: translateY(0);
          }
        }

        .direction-up {
          align-self: center;
        }

        .direction-row {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;

          .direction-center {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;

            .center-circle {
              width: 20px;
              height: 20px;
              background: #6c757d;
              border-radius: 50%;
              border: 2px solid #dee2e6;
            }
          }
        }

        .direction-down {
          align-self: center;
        }
      }
    }
  }
</style>

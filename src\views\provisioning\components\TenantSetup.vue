<template>
  <div class="step-container" v-loading="loading">
    <div class="form-title">
      <div class="title-bar"></div>
      <span>{{ isViewMode ? '查看租户' : '新增租户' }}</span>
    </div>
    <div class="title-divider"></div>

    <div class="form-content-center">
      <el-form :model="tenantForm" :rules="tenantFormRules" label-width="100px" class="tenant-form-content">
        <el-form-item label="行政区划:" prop="allRegionCodes">
          <el-cascader v-model="tenantForm.allRegionCodes" :options="regionOptions" :props="cascaderProps" :disabled="isViewMode" placeholder="请选择行政区划" ref="cascader" style="width: 100%" @change="handleRegionChange" clearable></el-cascader>
        </el-form-item>

        <el-form-item label="租户名称:" prop="regionName">
          <el-input v-model="tenantForm.regionName" :disabled="true" placeholder="请输入租户名称"></el-input>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="租户管理员:" prop="adminName">
              <el-input v-model="tenantForm.adminName" :disabled="isViewMode" :maxlength="20" placeholder="请输入租户管理员姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话:" prop="contactNumber">
              <div class="phone-input-group">
                <el-input v-model="tenantForm.contactNumber" :maxlength="11" :disabled="isViewMode" placeholder="请输入联系电话" class="phone-input"></el-input>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="联系地址:" prop="address">
          <el-input type="textarea" maxlength="100" :rows="4" show-word-limit v-model="tenantForm.address" :disabled="isViewMode" placeholder="请输入联系地址"></el-input>
        </el-form-item>

        <div class="info-tip">
          <i class="el-icon-info"></i>
          <span>说明：为了能快速完成服务开通配置流程，请您提前准备如下数据，后续需进行上传。</span>
        </div>

        <div class="document-section">
          <div class="document-title">数据名称</div>
          <div class="document-title">说明</div>

          <div class="document-row">
            <div class="document-cell">组织架构、系统用户信息</div>
            <div class="document-cell">
              <span class="download-link" @click="downloadTemplate('org')">模板下载</span>
              <i class="el-icon-download" style="color:#409EFF;font-weight:bold"></i>
            </div>
          </div>

          <div class="document-row">
            <div class="document-cell">镇、村介绍</div>
            <div class="document-cell">
              <span class="download-link" @click="downloadTemplate('town')">模板下载</span>
              <i class="el-icon-download" style="color:#409EFF;font-weight:bold"></i>
            </div>
          </div>
        </div>
      </el-form>
    </div>

    <div class="actions-divider"></div>

    <div class="action-buttons">
      <div></div>
      <div class="step-buttons">
        <el-button @click="$emit('cancel')">{{ isViewMode ? '关闭' : '取消' }}</el-button>
        <el-button v-if="!isViewMode" @click="save">保存</el-button>
        <el-button type="primary" @click="next">下一步</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getLazyTreeList } from "@/api/base/region";
import { getToken } from '@/util/auth'
import { handleDownloadFile } from '@/util/download';

export default {
  props: {
    isViewMode: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading:true,
      tenantForm: {
        allRegionCodes:'',
        regionCode: '',
        regionName: '',
        adminName: '',
        contactNumber: '',
        verifyCode: '',
        address: ''
      },
      tenantFormRules: {
        allRegionCodes: [
          { required: true, message: '请选择行政区划', trigger: ['blur', 'change'] }
        ],
        regionName: [
          { required: true, message: '请输入租户名称', trigger: ['blur', 'change'] }
        ],
        adminName: [
          { required: true, message: '请输入租户管理员姓名', trigger: ['blur', 'change'] },
          { min: 2, max: 20, message: '请输入2-20个字符', trigger: ['blur', 'change'] }
        ],
        contactNumber: [
          { required: true, message: '请输入联系电话', trigger: ['blur', 'change'] },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: ['blur', 'change'] },
          { max: 11, message: '请输入2-20个字符', trigger: ['blur', 'change'] }
        ],
        address: [
          { min: 2, max: 100, message: '请输入2-100个字符', trigger: ['blur', 'change'] }
        ]
      },
      regionOptions: [],
      cascaderProps: {
        lazy: true,
        lazyLoad: this.loadRegionData,
        value: 'id',
        label: 'name',
        leaf: 'leaf',
        checkStrictly: true,
        emitPath: true,
      }
    };
  },
  created() {
    console.log(this.formData,"tenantSetup")
    if (this.formData) {
      this.tenantForm = { ...this.formData };
      // this.tenantForm.regionCode = this.formData.allRegionCodes
    }
  },
  methods: {
    async loadRegionData(node, resolve) {
      const { level, data } = node;
      const parentCode = level === 0 ? '35' : data.id;

      try {
        const res = await getLazyTreeList(parentCode);
        const nodes = res.data.data.map(item => ({
          ...item,
          disabled: level < 2,
          leaf: !item.hasChildren
        }));
        resolve(nodes);
      } catch (error) {
        console.error('加载行政区划数据失败:', error);
        resolve([]);
      } finally {
        this.loading = false;
      }
    },

    handleRegionChange(value) {
      console.log('选中的行政区划:', value);
      if (value.length === 0) {
        this.tenantForm.regionName = "";
        this.tenantForm.regionCode = "";
      } else {
        let temp = this.$refs.cascader.getCheckedNodes();
        console.log('选中的行政区划:', temp);
        this.tenantForm.regionCode= temp[0].value;
        this.tenantForm.regionName = temp[0].label;
      }
    },

    

    downloadTemplate(type) {
      const templates = {
        'org': '/api/serviceActive/template/one',
        'town': '/api/serviceActive/template/two'
      };
      const url = `${templates[type]}?${this.website.tokenHeader}=${getToken()}`;
      handleDownloadFile(url);
    },

    save() {
      // this.$refs.tenantForm.validate(valid => {
      //   if (valid) {
      if(!this.tenantForm.regionCode){
        this.$message.warning("请选择行政区划")
      }else {
        this.$emit('save', this.tenantForm,"tenantSetup");
      }
    },

    next(){
      this.$emit('next', this.tenantForm,"tenantSetup");
    }
  }
};
</script>

<style lang="scss" scoped>
.step-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.step-content-area {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 20px;
}
.form-title {
  display: flex;
  align-items: center;

  .title-bar {
    width: 4px;
    height: 20px;
    background-color: #409eff;
    margin-right: 8px;
  }

  span {
    font-size: 16px;
    font-weight: 500;
  }
}

.title-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 15px 0 20px 0;
}

.actions-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 20px -20px 20px -20px;
}

.action-buttons {
  display: flex;
  justify-content: end;
  align-items: center;
  width: 100%;

  .step-buttons {
    display: flex;
    gap: 10px;

    .el-button {
      min-width: 80px;
    }
  }
}
.form-content-center {
  display: flex;
  justify-content: center;

  .tenant-form-content {
    max-width: 800px;
    width: 100%;
  }
}

.info-tip {
  background-color: #f0f9ff;
  border: 1px solid #419eff;
  padding: 10px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  margin: 20px 0;

  i {
    color: #409eff;
    margin-right: 8px;
  }

  span {
    color: #666;
    font-size: 13px;
  }
}

.document-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin: 20px 0;
}

.document-title {
  padding: 10px;
  background-color: #f5f7fa;
  font-weight: 500;
  border-bottom: 1px solid #ebeef5;
}

.document-row {
  display: contents;
}

.document-cell {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-left: 1px solid #ebeef5;
  }
}

.download-link {
  color: #409eff;
  cursor: pointer;
  margin-right: 5px;
}

.phone-input-group {
  display: flex;
  align-items: center;
  gap: 10px;

  .phone-input {
    flex: 1;
  }

  .verify-btn {
    flex-shrink: 0;
    white-space: nowrap;
  }
}
</style>

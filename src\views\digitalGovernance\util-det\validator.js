/** @module system/user
 * @Description:
 * @Author: lins14
 * @Date: 2021-12-10 09:15:53
 * @FilePath: /szxc-saber/src/views/system/user/util/validator.js
 * @LastEditors: lins14
 * @LastEditTime: 2022-01-06 14:35:56
 */
import { noSpace, validatename, checkChinese, isKeyBoardContinuousChar, passWordLimit, isEmail, isMobile, cardId } from '@/util/validate'

/**
 * @func
 * @param {*} rule
 * @param {*} value
 * @param {*} callback
 * @param {*} _this
 * @returns void
 */
export const validatePassword = (rule, value, callback, _this) => {
  if (!value) {
    callback(new Error('请输入密码'))
  } else {
    const username = _this.userInfo.user_name
    const result = passWordLimit(username, value)
    const keyBoard = isKeyBoardContinuousChar(value)
    const chinese = checkChinese(value)
    const isNoSpace = noSpace(value)
    if (keyBoard === true) {
      callback(new Error('密码不能含有键盘排序'))
    } else if (isNoSpace === false) {
      callback(new Error('密码不能含有空格'))
    } else if (chinese === true) {
      callback(new Error('密码不能含有中文'))
    } else if (result !== true) {
      callback(new Error(result))
    } else {
      callback()
    }
  }
}
export const validatePassword2 = (rule, value, callback, _this) => {
  if (value === '') {
    callback(new Error('请输入确认密码'))
  } else if (value !== _this.form.password) {
    callback(new Error('确认密码与密码不一致!'))
  } else {
    callback()
  }
}
export const checkAccountName = (rule, value, callback) => {
  const length = value.length
  if (value === '') {
    callback()
  } else if (!noSpace(value)) {
    return callback(new Error('用户名不能含有空格'))
  } else if (!(length > 1 && length < 21)) {
    return callback(new Error('用户名长度2-20'))
  } else {
    callback()
  }
}
export const checkName = (rule, value, callback) => {
  if (!noSpace(value)) {
    return callback(new Error('用户昵称不能含有空格'))
  } else {
    callback()
  }
}
export const checkAccount = (rule, value, callback) => {
  const length = value.length
  if (!value) {
    return callback(new Error('请输入账号'))
  } else if (!noSpace(value)) {
    return callback(new Error('账号不能含有空格'))
  } else if (!(length > 1 && length < 21)) {
    return callback(new Error('账号长度2-20'))
  } else {
    callback()
  }
}
export const checkRealName = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('请输入真实姓名'))
  } else if (!noSpace(value)) {
    return callback(new Error('真实姓名不能含有空格'))
  } else if (!validatename(value)) {
    return callback(new Error('真实姓名必须是中文,长度2-20'))
  } else {
    callback()
  }
}
export const checkAdminName = (rule, value, callback) => {
  if (!value) {
    return callback(new Error('请输入用户名'))
  } else if (!noSpace(value)) {
    return callback(new Error('用户名不能含有空格'))
  } else if (!validatename(value)) {
    return callback(new Error('用户名必须是中文,长度2-20'))
  } else {
    callback()
  }
}
export const checkMobile = (rule, value, callback, required) => {
  if (!value) {
    return required ? callback(new Error('请输入手机号码')) : callback()
  } else if (!isMobile(value)) {
    return callback(new Error('手机号码格式错误'))
  } else {
    return callback()
  }
}
export const checkEmail = (rule, value, callback) => {
  if (!value) {
    return callback()
  } else if (!isEmail(value)) {
    return callback(new Error('电子邮箱格式错误'))
  } else {
    return callback()
  }
}
export const checkIdNo = (rule, value, callback) => {
  if (!value) {
    return callback()
  } else if (cardId(value)[0]) {
    return callback(new Error('身份证号格式错误'))
  } else {
    return callback()
  }
}

// export default {
//   validatePass,
//   validatePass2,
//   checkName,
//   checkAccount,
//   checkRealName,
//   checkMobile,
//   checkEmail,
//   checkIdNo
// }

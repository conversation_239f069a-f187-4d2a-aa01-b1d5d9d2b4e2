<!-- 
/**
  * @Author: 林中奇
  * @Date: 2025/08/04
  * @lastAuthor:
  * @lastChangeDate:
  * @Explain: 党组织管理- 党员信息
  */
 
-->

<template>
  <div>
    <avue-crud ref="crud" v-model="form" :option="option" :table-loading="loading" :data="data" :page.sync="page"
      :permission="permissionList" :before-open="beforeOpen" @row-update="rowUpdate" @row-save="rowSave"
      @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
      @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <template slot="menuLeft">
        <el-button v-if="permission.census_import" size="small" icon="el-icon-upload2" @click="handleImport">导
          入</el-button>
        <el-button v-if="permission.census_delete" type="danger" size="small" icon="el-icon-delete" plain
          @click="handleDelete">批量删除 </el-button>
      </template>
      <template slot="menu" slot-scope="scope">
        <el-button type="text" size="mini" icon="el-icon-delete" @click="$refs.crud.rowDel(scope.row)">删
          除</el-button>
      </template>
    </avue-crud>
    <!-- 家庭成员编辑 -->
    <!-- <el-dialog :title="dialogTitle" append-to-body :visible.sync="memberBox" width="1200px" @before-close="handleClose">
      <member type="detail" :familyId="familyId" />
    </el-dialog> -->

    <el-dialog title="数据导入" append-to-body :visible.sync="excelBox" width="555px">
      <avue-form v-if="excelBox" v-model="excelForm" :option="excelOption" :upload-before="uploadBefore"
        :upload-error="uploadError" :upload-after="uploadAfter">
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate"> 点击下载<i class="el-icon-download el-icon--right" />
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </div>
</template>

<script>
import { getOrganList, getOrganDetail, orgaAddOrUpDate, organRemove } from '@/api/organManage'
import { mapGetters } from 'vuex'
import { isMobile } from '@/util/validate'
import axios from 'axios'
import website from '@/config/website'
import { handleDownload } from '@/util/download';
import { exportBlob } from "@/api/common";
import { downloadXls } from "@/util/util";

export default {
  components: {},
  props: {
    dept: {
      default: '',
      type: String
    }
  },
  data () {
    var checkMobile = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入联系方式'))
      } else if (value.length > 0 && !isMobile(value)) {
        callback(new Error('手机号码格式错误'))
      } else {
        callback()
      }
    }
    const checkName = (rule, value, callback) => {
      if (value !== '' && value.length > 1 && value.length < 21) {
        if (!value.trim()) {
          callback(new Error('户主姓名不能为纯空格'))
        }
      } else {
        callback(new Error('户主姓名长度在2到20个字符'))
      }
      callback()
    };
    return {
      icon: "el-icon-download el-icon--right", // del
      disableButton: false,
      dialogTitle: '成员',
      memberBox: false,
      familyId: '',
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        border: true,
        index: true,
        labelPosition: 'right',
        viewBtn: true,
        delBtn: false,
        selection: true,
        menuWidth: 200,
        searchMenuSpan: 4,
        labelWidth: 120,
        dialogWidth: 600,
        column: [
          {
            label: '姓名',
            prop: 'householderName',
            type: 'input',
            maxlength: 20,
            showWordLimit: true,
            span: 24,
            search: true,
            searchSpan: 6,
            rules: [
              {
                required: true,
                validator: checkName,
                trigger: "blur",
              }
            ],
          },
          {
            label: "手机号",
            prop: "contract",
            width: 100,
            span: 24,
            maxlength: 11,
            showWordLimit: true,
            rules: [{ required: true, validator: checkMobile, trigger: 'change' }],
          },
          {
            label: "性别",
            prop: "sex",
            type: "radio",
            dicData: [
              { label: "男", value: 1 },
              { label: "女", value: 2 },
            ],
            rules: [{ required: true, message: "请选择性别", trigger: 'change' }],
          },
          { label: "年龄", prop: "age", display: false },
          {
            label: '出生年月',
            prop: 'birthDate',
            type: 'date',
            rules: [{ required: true, message: '请选择出生年月', trigger: 'change' }],
            span: 12,
            type: "date",
            hide: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            hide: true
          },
          {
            label: "学历",
            prop: "sex",
            type: "radio",
            dicData: [
              { label: "小学", value: 1 },
              { label: "初中", value: 2 },
              { label: "高中", value: 3 },
              { label: "中专", value: 4 },
              { label: "大专", value: 5 },
              { label: "本科", value: 6 },
              { label: "硕士", value: 7 },
              { label: "博士", value: 8 },
            ],
            rules: [{ required: true, message: "请选择学历", trigger: 'change' }],
          },
          {
            label: '头像',
            prop: 'imgUrl',
            type: 'upload',
            accept: '.png, .jpg, .jpeg',
            span: 24,
            loadText: '图片上传中，请稍等',
            tip: '只能上传png/jpg/jpeg文件，且不超过10MB',
            propsHttp: {
              res: 'data',
              name: 'originalName',
              url: 'attachId'
            },
            action: '/api/blade-resource/oss/endpoint/put-file-attach',
            required: true,
          },
          {
            label: '入党日期',
            prop: 'birthDate',
            type: 'date',
            rules: [{ required: true, message: '请选择入党日期', trigger: 'change' }],
            span: 12,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: '转正日期',
            prop: 'birthDate',
            type: 'date',
            rules: [{ required: true, message: '请选择转正日期', trigger: 'change' }],
            span: 12,
            type: "date",
            display: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: "党员结构",
            prop: "sex",
            type: "radio",
            dicData: [
              { label: "正式党员", value: 1 },
              { label: "预备党员", value: 2 },
            ],
            rules: [{ required: true, message: "请选择党员结构", trigger: 'change' }],
          },
          { label: "党龄", prop: "age", display: false },
          {
            label: "流动党员",
            prop: "sex",
            type: "radio",
            dicData: [
              { label: "是", value: 1 },
              { label: "否", value: 2 },
            ],
            rules: [{ required: true, message: "请选择流动党员", trigger: 'change' }],
          },
          {
            label: '流入/流出时间',
            prop: 'birthDate',
            type: 'date',
            rules: [{ required: true, message: '请选择流入/流出时间', trigger: 'change' }],
            span: 12,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          { label: "流入/流出地点", prop: "age", display: false },

        ]
      },
      data: [],
      excelBox: false,
      excelForm: {},
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: '模板上传',
            prop: 'excelFile',
            type: 'upload',
            drag: true,
            loadText: '模板上传中，请稍等',
            span: 24,
            propsHttp: {
              res: 'data'
            },
            tip: '请上传 .xls,.xlsx 标准格式文件',
            action: '/api/census/import'
          },
          {
            label: '模板下载',
            prop: 'excelTemplate',
            formslot: true,
            span: 24
          }
        ]
      },
      deptId: ''
    }
  },
  computed: {
    ...mapGetters(['permission', 'userInfo']),
    permissionList () {
      return {
        addBtn: this.vaildData(this.permission.census_add, false),
        viewBtn: this.vaildData(this.permission.census_view, false),
        delBtn: this.vaildData(this.permission.census_delete, false),
        editBtn: this.vaildData(this.permission.census_edit, false)
      }
    },
    ids () {
      let ids = []
      this.selectionList.forEach((ele) => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  watch: {
    dept: function (val) {
      this.deptId = val
      this.page.currentPage = 1
      this.onLoad(this.page)
    },
  },
  created () {
  },
  methods: {
    rowSave (row, done, loading) {
      orgaAddOrUpDate(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          done()
        },
        (error) => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate (row, index, done, loading) {
      orgaAddOrUpDate(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          done()
        },
        (error) => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowDel (row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return organRemove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        })
    },
    handleDelete () {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return organRemove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.$refs.crud.toggleSelection()
        })
    },
    handleExport () {
      this.$confirm('是否导出户籍数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let downloadUrl = `/api/census/export?${this.website.tokenHeader}=${getToken()}`
        if (this.selectionList.length > 0) {
          downloadUrl += `&ids=${this.ids}`
        }
        const householderName = this.query.householderName || ''
        if (householderName.length > 0) {
          downloadUrl += `&householderName=${householderName}`
        }
        this.icon = "el-icon-loading el-icon--right";
        this.disableButton = true;
        const result = await handleDownload(downloadUrl);
        if (result != null) {
          this.icon = "el-icon-download el-icon--right";
          this.disableButton = false;
        }
      })
    },
    async beforeOpen (done, type) {
      if (['edit', 'view'].includes(type)) {
        await new Promise((resolve) => {
          getOrganDetail(this.form.id).then((res) => {
            const data = res.data.data || {}
            this.form = data
            resolve()
          })
        })
      }
      done()
    },
    searchReset () {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange (params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange (list) {
      this.selectionList = list
    },
    selectionClear () {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange (currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange (pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange () {
      this.onLoad(this.page, this.query)
    },
    onLoad (page, params = {}) {
      this.loading = true
      params.deptId = this.deptId
      getOrganList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then((res) => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
    handleImport () {
      this.excelBox = true
    },
    uploadAfter (res, done) {
      if (!res) {
        this.$message.success('导入成功')
      }
      axios.defaults.timeout = website.timeout
      this.excelForm = {}
      this.excelBox = false
      this.refreshChange()
      done()
    },
    uploadBefore (file, done) {
      axios.defaults.timeout = 600000 //上传前设置超时时间为10分钟
      done()
      return
    },
    uploadError () {
      axios.defaults.timeout = website.timeout
    },
    handleTemplate () {
      exportBlob(`/api/census/module`).then(res => {
        downloadXls(res.data, "户籍导入数据模板.xlsx");
      })
    },
    reflashTable () {
      this.$refs.crud.refreshTable();
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .census-user-option {
  display: flex;
  align-items: center;
  padding: 5px 10px;

  .content {
    margin-left: 10px;
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>

<style lang="scss">
.census-dialog .el-textarea .el-input__count {
  height: 25px;
  line-height: 25px;
  bottom: -25px !important;
}

.census-dialog .el-input .el-input__count {
  margin-top: 25px;
}
</style>

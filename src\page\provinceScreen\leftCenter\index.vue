<!--
 * @Description: 左中-人员激活
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2022-03-11 10:15:12
-->
<template>
  <div class="province-left-center-content">
    <div class="header">
      <span class="title">人员激活</span>
      <img
        src="/img/privinceScreen/titleDirection.png"
        class="title-direction"
        alt=""
      />
      <img src="/img/privinceScreen/line.png" class="left-on-line" alt="" />
    </div>
    <div class="content" style="margin-left: -5%">
      <dv-loading v-if="loading" style="margin-top: 80px"
        >Loading...</dv-loading
      >
      <div
        id="echart-user-active"
        :style="{ width: '100%', height: '26vh' }"
      ></div>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts/lib/echarts";
import "echarts/lib/chart/bar";
import "echarts/lib/chart/pie";
import "echarts/lib/chart/line";
import "echarts/lib/component/tooltip";
import "echarts/lib/component/title";
import "echarts/lib/component/legend";
import { debounce } from "lodash";
import { getUserActiveForm } from "@/api/privinceScreen/privinceScreen";
import { changeDateMonth } from '../commonJs/common';
import "./index.scss";
export default {
  data() {
    return {
      loading: true,
      myChart: null,
    };
  },
  mounted() {
    this.getUserActiveForm();
  },
  created() {
    this.setResize();
  },
  destroyed() {
    let self = this;
    window.removeEventListener("resize", function () {
      if (self.myChart) {
        self.myChart = null;
      }
    });
  },
  methods: {
    async getUserActiveForm() {
      const result = await new Promise((resolve) => {
        this.loading = true;
        getUserActiveForm().then((res) => {
          this.loading = false;
          if (res && res.data.code === 200) {
            resolve(res.data.data);
          }
        });
      });
      this.$nextTick(() => {
        this.renderEcharts(result);
      });
    },
    renderEcharts(userActiceData) {
      let keyList = userActiceData.map((item) => {
        return item.key;
      });
      const afterKeyList = changeDateMonth(keyList);
      //激活数据
      const activeValueList = userActiceData.map((item) => {
        return item.active;
      });
      //激活率数据
      const activeRateValueList = userActiceData.map((item) => {
        return item.activeRate;
      });
      //活跃数据
      const enlivenValueList = userActiceData.map((item) => {
        return item.enliven;
      });
      //活跃率数据
      const enlivenRateValueList = userActiceData.map((item) => {
        return item.enlivenRate;
      });
      this.myChart = echarts.init(
        document.getElementById("echart-user-active")
      );

      var echartOpt = {
        fz: "12",
        color: "#fff",
      };

      var option = {
        grid: {
          top: "65",
          right: "2%",
          width: "90%",
          height: "65%",
          containLabel: true,
        },
        dataZoom: [
          {
            show: true,
            height: 15,
            xAxisIndex: [0],
            left: "50",
            right: "50",
            bottom: 0,
            backgroundColor: "#1f2d70",
            dataBackground: {
              areaStyle: {
                color: "rgba(79, 140, 210, 0.4)",
              },
              lineStyle: {
                opacity: 0.8,
                color: "#8392A5",
              },
            },
            fillerColor: "rgba(4, 175, 246, 0.6)",
            start: 0,
            end: 10,
            handleIcon:
              "path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z",
            handleSize: "80%",
            handleStyle: {
              color: "#00ADFA",
              shadowBlur: 0,
              shadowColor: "rgba(255, 0, 0, 1)",
              shadowOffsetX: 0,
              shadowOffsetY: 0,
            },
            textStyle: {
              color: "#11caff",
              fontSize: "12",
            },
            borderColor: "#3458B4",
          },
          {
            type: "inside",
            show: true,
            height: 15,
            start: 1,
            end: 35,
          },
        ],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(0,46, 115, 0.3)",
            },
          },
          formatter: function (params) {
            var str = params[0].name + "<br>";
            params.forEach(function (v) {
              if (v.seriesType === "bar") {
                str += v.seriesName + ": " + v.value + "人" + "<br>";
              } else {
                str += v.seriesName + ": " + v.value + "%" + "<br>";
              }
            });
            return str;
          },
          textStyle: {
              align: "left",
              color: "#f2f3ff",
              fontSize: "16",
          },
          backgroundColor: "rgba(15, 52, 135, 0.7)",
          borderWidth: "1",
          borderColor: "#5cc1ff",
          extraCssText: "box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);",
        },
        legend: {
          data: ["激活", "激活率", "活跃", "活跃率"],
          right: "0",
          top: "0",
          textStyle: {
            color: echartOpt.color,
            fontSize: echartOpt.fz,
          },
          itemGap: 10,
          itemHeight: 15,
          itemWidth: 26,
        },
        calculable: true,
        xAxis: {
          type: "category",
          axisLine: {
            lineStyle: {
              color: echartOpt.color,
            },
          },
          axisTick: {
            show: false,
            interval: 0,
            alignWithLabel: true,
          },
          axisLabel: {
            interval: 0,
            rotate: "0",
            textStyle: {
              fontSize: echartOpt.fz,
              color: echartOpt.color,
            },
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: ["#2f46a1"],
            },
          },
          data: afterKeyList,
        },
        yAxis: [
          {
            type: "value",
            name: "人数（人）",
            min: "dataMin",
            splitLine: {
              show: false,
              lineStyle: {
                color: ["#2f46a1"],
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: echartOpt.fz,
                color: echartOpt.color,
              },
            },
            axisLine: {
              lineStyle: {
                color: echartOpt.color,
                fontSize: echartOpt.fz,
              },
            },
          },
          {
            type: "value",
            name: "比例（%）",
            min: "dataMin",
            splitLine: {
              show: false,
              lineStyle: {
                color: ["#2f46a1"],
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: true,
              textStyle: {
                fontSize: echartOpt.fz,
                color: echartOpt.color,
              },
            },
            axisLine: {
              lineStyle: {
                color: echartOpt.color,
                fontSize: echartOpt.fz,
              },
            },
          },
        ],
        color: ["#0570E9", "#FEC006", "#F10F0F", "#14EE10"],
        series: [
          {
            name: "激活",
            type: "bar",
            barMaxWidth: 20,
            itemStyle: {
              normal: {
                barBorderRadius: 10,
                borderColor: "rgba(63,167,220,.9)",
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 1,
                    color: "#0570E9",
                  },
                  {
                    offset: 0,
                    color: "rgba(63,167,220,.4)",
                  },
                ]),
              },
            },
            data: activeValueList,
            zlevel: 9,
          },
          {
            name: "激活率",
            type: "line",
            yAxisIndex: 1,
            symbolSize: 5,
            smooth: true,
            data: activeRateValueList,
            zlevel: 9,
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "rgba(5,112,233, 1)", // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: "#061936", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          {
            name: "活跃",
            type: "bar",
            barMaxWidth: 20,
            itemStyle: {
              normal: {
                barBorderRadius: 10,
                borderColor: "rgba(254,192,6, 1)",
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 1,
                    color: "rgba(254,192,6, 0.8)",
                  },
                  {
                    offset: 0,
                    color: "rgba(254,192,6, 0.5)",
                  },
                ]),
              },
            },
            data: enlivenValueList,
            zlevel: 9,
          },
          {
            name: "活跃率",
            type: "line",
            yAxisIndex: 1,
            symbolSize: 5,
            smooth: true,
            data: enlivenRateValueList,
            zlevel: 9,
            areaStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: "#d0a34b", // 0% 处的颜色
                  },
                  {
                    offset: 0.6,
                    color: "#061936", // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
        ],
      };

      // 使用刚指定的配置项和数据显示图表。
      this.myChart.setOption(option);
    },
    setResize() {
      let self = this;
      window.addEventListener(
        "resize",
        debounce(function () {
          if (self.myChart) self.myChart.resize();
        }, 200)
      );
    },
  },
};
</script>
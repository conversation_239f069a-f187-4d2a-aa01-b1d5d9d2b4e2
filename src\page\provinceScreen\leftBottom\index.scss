.province-left-bottom-content {
    position: absolute;
    width: 100%;
    height: 100%;
    .header {
        position: absolute;
        width: 100%;
        white-space: nowrap;
        top: 2vh;
        left: 50%;
        transform: translate(-50%, 0);
        .title-nation {
            position: absolute;
            letter-spacing: 3px;
            color: #fff;
            font-size: 1.7vh;
            cursor: pointer;
            left: 0;
        }
        .title-pilot {
            position: absolute;
            letter-spacing: 3px;
            color: #fff;
            font-size: 1.7vh;
            cursor: pointer;
            left: 180px !important;
        }
        .title-direction {
            position: absolute;
            width: 35px;
            top: 0.8vh;
            right: 0;
            height: auto;
        }
        .left-on-line {
            position: absolute;
            width: 100%;
            top: 3.6vh;
        }
    }
    .content {
        position: absolute;
        float: left;
        width: 100%;
        margin-top: 7vh;
    }
}

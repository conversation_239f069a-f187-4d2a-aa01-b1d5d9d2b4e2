<!-- 人员选择 -->
<template>
  <div style="margin: -24px 12px 0;">
    <el-tabs v-if="!type" v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="分组" name="first" />
      <el-tab-pane label="人员" name="second" />
      <!-- 手搓穿梭框 -->
      <!-- <div class="flex">
        <el-transfer filterable :filter-method="filterMethod" filter-placeholder="请输入搜索内容" v-model="director"
          :data="transferData" :titles="['选择', '已选']">
        </el-transfer>
      </div> -->
    </el-tabs>
    <!-- 手搓穿梭框 -->
    <div>
      <div class="page">
        <div class="card">
          <div class="search-input">
            <el-input v-model="searchInput" placeholder="请输入关键词进行搜索" clearable size="small"
              @keyup.enter.native="handleQuery" @clear="handleClear" />
          </div>
          <div v-show="searchValue" v-loading="loading">
            <el-tree ref="search" :data="searchList" :load="loadNode" :props="props" node-key="id" lazy
              :check-strictly="true" @node-click="nodeClick" />
          </div>
          <div v-show="searchValue === ''" v-loading="loading">
            <el-tree ref="tree" :data="showList" :load="loadNode" :props="props" node-key="id" lazy
              :check-strictly="true" @node-click="nodeClick" />
            <!-- <div style="margin-top: 20px;">
              <el-checkbox v-model="isAll">全选</el-checkbox>
              <el-tooltip class="item" effect="dark" content="可一键选择在你的通讯录管理权限内，且属于此应用可见范围的所有成员和部门" placement="top">
                <i class="el-icon-warning" />
              </el-tooltip>
            </div> -->
          </div>
        </div>
        <!-- 左标 -->
        <div style="display: flex; align-items: center;">
          <i class="el-icon-caret-right" style="font-size: 30px;" />
        </div>
        <!-- 右侧已选 -->
        <div class="card">
          <div class="title">已选成员</div>
          <div>
            <div v-if="isAll" class="line-item" @click="isAll = false">
              <div class="name">全选</div>
              <div><i class="el-icon-close" /></div>
            </div>
            <div v-for="(item, index) in checkedNode" :key="index" class="line-item" @click="cancelSelect(item)">
              <div class="name">{{ item.label }}</div>
              <div v-if="item.isSee"><i class="el-icon-close" /></div>
            </div>
          </div>
          <div>
            <el-button type="primary" style="width:100%;margin-top: 20px;" @click="confirm()">确认</el-button>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import { getGroupList, getDeptUserTree, getGroupDet } from "@/api/digitalGovernance/work.js";
import { mapGetters } from "vuex";

export default {
  name: 'PersonSelect',
  components: {},
  props: {
    type: {
      type: Boolean,
      default: true
    },
    // 是否单选
    isSingle: {
      type: Boolean,
      default: false
    },
    existList: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      activeName: 'first',
      searchInput: '',
      searchValue: '',
      searchList: [], // 搜索后数据
      showList: [], // 正常数据
      checkedKey: [],
      checkedNode: [],
      loading: false,
      props: {
        isLeaf: 'leaf',
      },
      isAll: false,
      id: ''
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  watch: {},
  computed: {
    ...mapGetters(["userInfo"]),
  },
  created () {
    if (this.type) {
      this.activeName = 'second';
    }
    // 判断是否有回显数据
    if (this.existList.length > 0) {
      this.checkedNode = this.existList
    }
  },
  mounted () {
  },
  // 方法集合
  methods: {
    handleClick (tab, event) {
      this.handleClear()
      this.activeName = tab.paneName;
      this.getList()
    },

    // 数据格式统一
    formatSet (list) {
      let label = this.activeName == 'first' ? 'name' : 'title'
      const returnList = []
      for (let i = 0; i < list.length; i++) {
        const element = list[i];
        const obj = {
          id: element.id,
          label: element.phone ? `${element[label]}-${element.phone}` : element[label],
          leaf: element.leaf
        }

        returnList.push(obj)
      }
      this.loading = false;
      return returnList;
    },

    getList () {
      if (this.activeName == 'first') {
        getGroupList({ current: 1, size: 999 }).then((res) => {
          this.showList = this.formatSet(res.data.data.records)
        });
      } else {
        getDeptUserTree({ parentId: 0 }).then((res) => {
          const deptList = res.data.data.deptList
          const userList = res.data.data.userList
          for (let i = 0; i < userList.length; i++) {
            userList[i].leaf = true
          }
          const list = userList.concat(deptList)
          this.showList = this.formatSet(list)
        });
      }
    },

    async loadNode (node, resolve) {
      if (this.activeName == 'first') {
        if (node.level == 0) {
          const res = await getGroupList({ current: 1, size: 999 })
          resolve(this.formatSet(res.data.data.records))
        } else {
          // 分组成员
          getGroupDet({ id: node.data.id })
            .then((res) => {
              const data = res.data.data
              const list = []
              for (let i = 0; i < data.userList.length; i++) {
                const element = data.userList[i];
                list[i] = {
                  id: element.id,
                  label: element.phone ? `${element.realName}-${element.phone}` : element.realName,
                  leaf: true
                }
              }
              this.loading = false;

              resolve(list)
            })
        }
      } else {
        const id = node.level === 0 ? 0 : node.data.id
        const res = await getDeptUserTree({ parentId: id })
        const deptList = res.data.data.deptList
        const userList = res.data.data.userList
        for (let i = 0; i < userList.length; i++) {
          userList[i].leaf = true
        }
        const list = userList.concat(deptList)
        resolve(this.formatSet(list))
      }

    },
    handleClear () {
      this.loading = true
      this.searchInput = this.searchValue = ''
    },
    handleQuery () {
      this.loading = true
      this.searchValue = this.searchInput
      this.searchDept()
    },
    nodeClick (data, node, comp) {
      // console.log(data, node, comp)
      // 跳过省市县节点
      if (!data.leaf) return

      if (this.isSingle && this.checkedNode.length == 1) {
        this.checkedNode = [data]
        return
      }

      const has = this.checkedNode.find(item => item.id == data.id)
      if (!has) {
        this.checkedNode.push(data)
      }
    },
    cancelSelect (delItem) {
      if (delItem.isSee == 0) {
        return
      }
      this.checkedNode = this.checkedNode.filter(item => item.id !== delItem.id)
      // console.log(this.checkedNode, 'che', this.checkedKey)
    },
    async searchDept () {
      if (this.activeName == 'first') {
        getGroupList({ current: 1, size: 999, name: this.searchValue }).then((res) => {
          this.searchList = this.formatSet(res.data.data.records)
        });
      } else {
        getDeptUserTree({ parentId: 0, name: this.searchValue }).then((res) => {
          const deptList = res.data.data.deptList
          const userList = res.data.data.userList
          for (let i = 0; i < userList.length; i++) {
            userList[i].leaf = true
          }
          const list = userList.concat(deptList)
          this.searchList = this.formatSet(list)
        });
      }
    },
    confirm () {
      const selectDepts = []
      const selectUsers = []
      const selectUsersName = []
      const param = {
        isAll: 0,
        id: this.id
      }

      this.checkedNode.forEach(item => {
        if (item.leaf) {
          selectUsers.push(item.id)
          selectUsersName.push(item.label)
        } else {
          selectDepts.push(item.id)
        }
      })
      param.selectDepts = selectDepts.join(',')
      param.selectUsers = selectUsers.join(',')
      param.selectUsersName = selectUsersName.join(',')
      this.$emit('confirm', param)
    },
    reset () {
      this.searchInput = '';
      this.searchValue = '';
      this.searchList = [];
      this.checkedKey = [];
      this.checkedNode = [];
    }
  }
}
</script>
<style lang='scss' scoped>
.flex {
  display: flex;
  justify-content: center;
  margin-top: 12px;
}

.search-input {
  height: 40px;
}

.page {
  display: flex;
  justify-content: space-between;
  // align-items: center;
}

.card {
  width: 40%;
  padding: 15px;
  border-radius: 15px;
}

.line-item {
  display: flex;
  justify-content: space-between;
  align-content: center;
  line-height: 28px;
  height: 26px;
  font-size: 14px;
  padding: 0 5px;

  &:hover {
    background: #ecf5ff;
    cursor: pointer;
  }

  .name {
    white-space: nowrap;
    word-break: keep-all;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.title {
  font-size: 17px;
  border: 1px solid #b2b2b2;
  padding: 3px 15px;
  text-align: center;
  border-radius: 8px;
  margin-bottom: 10px;
}
</style>

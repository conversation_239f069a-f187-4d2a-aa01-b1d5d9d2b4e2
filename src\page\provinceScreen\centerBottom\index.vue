<!--
 * @Description: 中下
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2022-01-06 10:47:20
-->
<template>
  <div class="province-center-bottom-content">
    <div class="center-bottom-header">
      <span class="title">平台渗透率排行榜</span>
      <img src="/img/privinceScreen/titleDirection.png" class="title-direction" alt="" />
      <img src="/img/privinceScreen/line.png" class="center-bottom-line" alt="" />
    </div>
    <div class="content">
      <div class="content-value">
        <dv-loading v-if="loading">Loading...</dv-loading>
        <dv-scroll-board v-else :config="this.config" ref="scrollBoard" />
      </div>
    </div>
  </div>
</template>
<script>
import "./index.scss";
import { getUserPenetrationSort } from "@/api/privinceScreen/privinceScreen";
export default {
  data() {
    return {
      loading: true,
      config: {
        header: [
          "<span>排名</span>",
          "<span>地市</span>",
          "<span>平台任务数</span>",
          "<span>已部署数</span>",
          "<span>导入用户数</span>",
          "<span>平台渗透率</span>",
        ],
        rowNum: 5,
        headerBGC: "",
        headerHeight: 30,
        columnWidth: [100, 120, 155, 155, 155, 155],
        evenRowBGC: "",
        oddRowBGC: "",
        data: [],
      },
    };
  },
  created() {
    this.getUserPenetrationSort();
  },
  methods: {
    async getUserPenetrationSort(params) {
      let _this = this;
      const data = await new Promise((resolve) => {
        this.loading = true;
        getUserPenetrationSort(params).then((res) => {
          this.loading = false;
          if (res && res.data.code === 200) {
            resolve(res.data.data);
          }
        });
      });
      let countfinancialList = [];
      for (let i = 0; i < data.length; i++) {
        let editData = [];
        const color = _this.getTextColor(i);
        editData[0] =
          "<span style='color:" +
          color +
          "' title='" +
          data[i].sort +
          "'>" +
          data[i].sort +
          "</span>";
        editData[1] =
          "<span style='color:" +
          color +
          "' title='" +
          data[i].name +
          "'>" +
          data[i].name +
          "</span>";
        editData[2] =
          "<span style='color:" +
          color +
          "' title='" +
          data[i].taskNum +
          "'>" +
          data[i].taskNum +
          "</span>";
        editData[3] =
          "<span style='color:" +
          color +
          "' title='" +
          data[i].deployed +
          "'>" +
          data[i].deployed +
          "</span>";
        editData[4] =
          "<span style='color:" +
          color +
          "' title='" +
          data[i].userImport +
          "'>" +
          data[i].userImport +
          "</span>";
        editData[5] =
          "<span style='color:" +
          color +
          "' title='" +
          data[i].penetration +
          "%'>" +
          data[i].penetration +
          "%</span>";
        countfinancialList[i] = editData;
      }
      this.config = {
        header: [
          "<span>排名</span>",
          "<span>地市</span>",
          "<span>平台任务数</span>",
          "<span>已部署数</span>",
          "<span>导入用户数</span>",
          "<span>平台渗透率</span>",
        ],
        rowNum: 5,
        headerBGC: "",
        headerHeight: 30,
        columnWidth: [100, 120, 155, 155, 155, 155],
        evenRowBGC: "",
        oddRowBGC: "",
        data: countfinancialList,
      };
    },
    getTextColor(index) {
      let color = "#fff";
      switch (index) {
        case 0:
          color = "#F9A035";
          break;
        case 1:
          color = "#01D4DB";
          break;
        case 2:
          color = "#45D1AA";
          break;
        default:
          break;
      }
      return color;
    },
  },
};
</script>

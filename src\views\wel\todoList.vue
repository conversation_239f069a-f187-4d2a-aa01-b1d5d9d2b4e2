<!--
 * @Date: 2025-08-04 18:38:24
 * @LastEditors: linqh21
 * @LastEditTime: 2025-08-04 19:17:24
 * @Description: Tab切换页面
 * @FilePath: \src\views\wel\todoList.vue
-->
<template>
  <div class="tab-container" >
    <el-tabs v-model="activeTab" type="border-card" >
      <el-tab-pane label="工作待办" name="tab1">
        <TabComponent1 />
      </el-tab-pane>
      <el-tab-pane label="预警待办" name="tab2">
        <TabComponent2 />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import TabComponent1 from '@/views/digitalGovernance/mywork.vue'
import TabComponent2 from '@/components/TabComponent2.vue'

export default {
  name: 'TodoList',
  components: {
    TabComponent1,
    TabComponent2
  },
  data() {
    return {
      activeTab: 'tab1' // 默认激活第一个tab
    }
  }
}
</script>

<style scoped>
.tab-container {
  margin: 0 auto;
  padding: 20px;
}


/* 响应式设计 */
@media (max-width: 768px) {
  .tab-container {
    padding: 15px;
  }

  .custom-tabs ::v-deep .el-tabs__item {
    padding: 6px 12px;
    font-size: 13px;
  }
}
</style>
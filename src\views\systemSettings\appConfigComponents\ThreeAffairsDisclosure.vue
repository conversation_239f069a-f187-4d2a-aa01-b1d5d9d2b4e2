<!--
 * @Date: 2025-07-31 21:50:00
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-31 21:50:00
 * @Description: 三务公开组件
 * @FilePath: \src\views\system\appConfigComponents\ThreeAffairsDisclosure.vue
-->
<template>
  <div class="three-affairs-container">
    <div class="three-affairs-header">
      <div class="three-affairs-title">三务公开</div>
    </div>
    <div class="three-affairs-content">
      <div class="news-item">
        <div class="news-image">
          <img src="@/assets/appConfig/swiper-def.png" alt="新闻图片" />
        </div>
        <div class="news-info">
          <div class="news-title">中国气象局新闻发布会：2月...</div>
          <div class="news-meta">
            <span class="news-source">四季洋房</span>
            <span class="news-time">2023-02-07 15:48:19</span>
          </div>
        </div>
      </div>
      <div class="news-item">
        <div class="news-image">
          <img src="@/assets/appConfig/swiper-def.png" alt="新闻图片" />
        </div>
        <div class="news-info">
          <div class="news-title">中国气象局新闻发布会：2月...</div>
          <div class="news-meta">
            <span class="news-source">四季洋房</span>
            <span class="news-time">2023-02-07 15:48:19</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ThreeAffairsDisclosure'
}
</script>

<style scoped>
/* ================== 三务公开样式 ================== */
.three-affairs-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  padding: 12px;
  box-sizing: border-box;
}

.three-affairs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.three-affairs-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.three-affairs-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.news-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  border-radius: 6px;
  background: #fafafa;
  transition: background 0.2s ease;
}

.news-item:hover {
  background: #f0f8ff;
}

.news-image {
  width: 60px;
  height: 45px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.news-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.news-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.news-title {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.news-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #999;
}

.news-source {
  background: #ff6b6b;
  color: #fff;
  padding: 1px 6px;
  border-radius: 3px;
  font-size: 10px;
}

.news-time {
  font-size: 10px;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .three-affairs-container {
    padding: 8px;
  }

  .three-affairs-title {
    font-size: 14px;
  }

  .news-item {
    padding: 6px;
  }

  .news-image {
    width: 50px;
    height: 38px;
  }

  .news-title {
    font-size: 12px;
  }

  .news-meta {
    font-size: 10px;
  }
}
</style>

/*
 * @Description: 提取「管理员管理」与「村民管理」的共用代码
 * @Author: lins14
 * @Date: 2021-12-10 09:19:01
 * @FilePath: \src\views\system\user\mixins\action.js
 * @LastEditors: linqh21
 * @LastEditTime: 2024-04-28 09:31:14
 */
import { mapGetters } from 'vuex'
import { getRoleDropdown } from '@/api/system/role'
import {
  userKindList,
  getUser,
  getUserAdmin,
  getUserEdit,
  getUserEditAdmin,
  remove,
  removeAdmin,
  add,
  update,
  addAdmin,
  updateAdmin,
  getDefaultPassword,
  resetPassword,
  ImportAdminApi
} from '@/api/system/user'
import axios from 'axios'
import { Loading } from 'element-ui'

export default {
  data() {
    return {
      defaultPsw: '',
      excelBox: false,
      init: {
        roleTree: [],
        deptTree: []
      },
      excelForm: {},
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: '用户上传',
            prop: 'excelFile',
            type: 'upload',
            drag: true,
            loadText: '用户上传中，请稍等',
            span: 24,
            propsHttp: {
              res: 'data'
            },
            tip: '请上传 .xls 标准格式文件',
            action: this.userKind === userKindList.villager ? '/api/blade-user/import-user' : ImportAdminApi
          },
          {
            label: '数据覆盖',
            prop: 'isCovered',
            type: 'switch',
            align: 'center',
            width: 80,
            span: 24,
            tip: '数据覆盖以手机号为基准',
            dicData: [
              {
                label: '否',
                value: 0
              },
              {
                label: '是',
                value: 1
              }
            ],
            value: 0,
            slot: true,
            rules: [
              {
                required: true,
                message: '请选择是否覆盖',
                trigger: 'blur'
              }
            ]
          },
          {
            label: '模板下载',
            prop: 'excelTemplate',
            formslot: true,
            span: 24
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    'excelForm.isCovered'() {
      if (this.excelForm.isCovered !== '') {
        const column = this.findObject(this.excelOption.column, 'excelFile')
        const importUrl = this.userKind === userKindList.villager ? '/api/blade-user/import-user' : ImportAdminApi
        column.action = `${importUrl}?isCovered=${this.excelForm.isCovered}`
      }
    }
  },
  mounted() {
    this.initData(this.website.tenantId)
    this.getDefaultPassword()
  },
  methods: {
    async getDefaultPassword() {
      const result = await new Promise(resolve => {
        getDefaultPassword().then(res => {
          if (res.data.code === 200 && res.data.data != null) {
            if (typeof res.data.data === 'string') resolve(res.data.data)
          }
        })
      })
      this.$nextTick(() => {
        this.defaultPsw = result
      })
    },
    getTreeData(treeData) {
      this.findObject(this.option.group, 'realDeptId').dicData = treeData
    },
    async initData(tenantId) {
      this.findObject(this.option.group, 'roleId').dicData = (await getRoleDropdown(tenantId)).data.data.adminRoles
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          if(this.userKind === userKindList.villager){
            return remove(row.id)
         }else {
            return removeAdmin(row.id)
         }
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        })
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {

          if(this.userKind === userKindList.villager){
            return remove(this.ids)
         }else {
            return removeAdmin(this.ids)
         }
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.$refs.crud.toggleSelection()
        })
    },
    async beforeOpen(done, type) {
      done()
      let loadingInstance
      this.$nextTick(() => {
        loadingInstance = Loading.service({
          fullscreen: false,
          target: '.el-dialog__body'
        })
      })
      let res
      if(this.userKind === userKindList.villager){
         res = type === 'edit' ? await getUserEdit(this.form.id, this.userKind) : type === 'view' ? await getUser(this.form.id, this.userKind) : {}
      }else {
         res = type === 'edit' ? await getUserEditAdmin(this.form.id, this.userKind) : type === 'view' ? await getUserAdmin(this.form.id, this.userKind) : {}
      }

      // 加200ms的延迟避免屏闪
      await new Promise(resolve => {
        setTimeout(resolve, 200)
      })
      if (res.data) this.form = res.data.data
      if (this.form.roleId && this.form.roleId.includes(',')) this.form.roleId = this.form.roleId.split(',')
      if (this.form.postId && this.form.postId.includes(',')) this.form.postId = this.form.postId.split(',')
      this.initFlag = true
      this.$nextTick(() => {
        loadingInstance.close()
      })
    },
    rowSave(row, done, loading) {
      if (Array.isArray(row.roleId)) row.roleId = row.roleId.join(',')
      if (Array.isArray(row.postId)) row.postId = row.postId.join(',')
      if (row.labelId && Array.isArray(row.labelId)) {
        row.labelId = row.labelId.join(',')
      } else row.labelId = row.labelId || ''
      row.tenantId = this.website.tenantId
      const $_add = this.userKind === userKindList.villager ? add : addAdmin
      $_add(row).then(
        () => {
          this.initFlag = false
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          done()
        },
        error => {
          window.console.log(error)
          loading()
        }
      )
    },
    rowUpdate(row, index, done, loading) {
      if (Array.isArray(row.roleId)) row.roleId = row.roleId.join(',')
      if (Array.isArray(row.postId)) row.postId = row.postId.join(',')
      if (typeof row.labelId === 'object') {
        row.labelId = row.labelId.join(',')
      } else if (typeof row.labelId === 'string') {
        row.labelId = row.labelId
      } else {
        row.labelId = ''
      }
      row.tenantId = this.website.tenantId
      const $_update = this.userKind === userKindList.villager ? update : updateAdmin
      $_update(row).then(
        () => {
          this.initFlag = false
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          done()
        },
        error => {
          window.console.log(error)
          loading()
        }
      )
    },
    handleReset() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      if (this.defaultPsw) {
        this.$confirm(`确定将选择账号密码重置为${this.defaultPsw}?`, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            return resetPassword(this.ids)
          })
          .then(() => {
            this.$message({
              type: 'success',
              message: '操作成功!'
            })
            this.$refs.crud.toggleSelection()
          })
          .catch(() => {})
      } else {
        this.$message.warning('请先设置默认密码!')
      }
    },
    // --------------------------------------------/*  */--------------------------------------------
    uploadAfter(res, done) {
      if (!res) {
        this.$message.success('导入成功')
      } else {
        const res1 = res.toString().replace('Error: ', '')
        this.$confirm(<div style='white-space:pre-wrap;word-wrap: break-word;max-height: 800px;overflow: scroll;'>{res1}</div>, '错误提示', {
          showConfirmButton: false,
          cancelButtonText: '关闭',
          type: 'error'
        })
          .then(() => {})
          .catch(() => {})
      }

      axios.defaults.timeout = this.website.timeout
      this.excelForm = {}
      this.excelBox = false
      this.refreshChange()
      done()
    },
    uploadBefore(file, done, loading) {
      if (file.name.match(/^.*\.(?:xls)$/)) {
        axios.defaults.timeout = 600000 // 上传前设置超时时间为10分钟
        done()
      } else {
        this.$message.error('格式错误，请上传.xls文件')
        loading()
      }
      return
    },
    uploadError() {
      axios.defaults.timeout = this.website.timeout
    }
  }
}

/*
 * @Description:订单管理
 * @Author: wangyy553
 * @Date: 2022-01-12 10:35:26
 * @LastEditors: wangyy553
 * @LastEditTime: 2022-01-17 10:37:06
 */
import request from '@/router/axios'

/**
 * @description: 获得订单分页
 * @param {int}current
 * @param {int}size
 * @param {object}params
 */
export const getList = (current, size, params) => {
  return request({
    url: '/api/admin/integral-order/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

/**
 * @description: 订单详情
 * @param {int} id
 */
export const getDetail = (id) => {
  return request({
    url: '/api/admin/integral-order/' + id,
    method: 'get',
  })
}


/**
 * @description: 订单发货记录分页
 * @param {int}current
 * @param {int}size
 * @param {int}orderId
 */
export const getDeliverData = (current, size, orderId) => {
  return request({
    url: '/api/admin/integral-order/send/page',
    method: 'get',
    params: {
      orderId,
      current,
      size,
    }
  })
}

/**
 * @description: 订单发货
 * @param {object} params
 */
export const submitDeliver = (params) => {
  return request({
    url: '/api/admin/integral-order/send',
    method: 'post',
    params: {
      ...params
    }
  })
}


/**
 * @description:订单退回
 * @param {int} id
 */
export const closeDeliver = (id) => {
  return request({
    url: '/api/admin/integral-order/close',
    method: 'post',
    params: {
      id
    }
  })
}

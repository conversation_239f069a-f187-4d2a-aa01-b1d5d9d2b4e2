/*
 * @Author: chenz76
 * @Date: 2021-06-23 20:03:48
 * @LastEditTime: 2025-03-04 09:00:15
 * @LastEditors: linqh21
 * @Description: 便民通讯录
 * @FilePath: \src\api\carouselMangement\carouselMangement.js
 ** @module carouselMangement
 */
import request from '@/router/axios'

/**
 * @func
 * @desc 分页
 * @param {number} current 当前页码
 * @param {number} size 每页数据条数
 * @param {{}} params 查询数据
 * @returns {Promise<ResponseData>}
 */
export const startList = (current, size, params) => {
  return request({
    url: '/api/blade-carousel/carousel/list',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  })
}

/**
 * 新增
 * @function
 * @param {{}} data 轮播图信息
 * @returns {Promise<ResponseData>}
 */
export const saveCar = data => {
  return request({
    url: '/api/blade-carousel/carousel/save',
    method: 'post',
    data
  })
}

/**
 * 编辑
 * @function
 * @param {{}} data 轮播图信息
 * @returns {Promise<ResponseData>}
 */
export const editCar = data => {
  return request({
    url: '/api/blade-carousel/carousel/update',
    method: 'put',
    data
  })
}

/**
 * 删除
 * @function
 * @param {{}} params
 * @param {number[]} params.ids - 轮播图id
 * @returns {Promise<ResponseData>}
 */
export const delCar = params => {
  return request({
    url: '/api/blade-carousel/carousel/remove',
    method: 'post',
    params
  })
}

/**
 * 获取本站信息
 * @function
 * @param {{}} params
 * @param {number} params.size
 * @returns {Promise<ResponseData>}
 */
export const getInformation = params => {
  return request({
    url: '/api/admin/informationOther/carouselList',
    method: 'get',
    params
  })
}

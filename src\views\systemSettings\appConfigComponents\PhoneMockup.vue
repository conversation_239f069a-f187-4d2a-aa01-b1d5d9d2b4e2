<!--
 * @Date: 2025-07-31 22:05:00
 * @LastEditors: linqh21
 * @LastEditTime: 2025-08-01 16:41:19
 * @Description: 手机模拟器组件
 * @FilePath: \src\views\system\appConfigComponents\PhoneMockup.vue
-->
<template>
  <div class="iphone-mockup">
    <div class="iphone-top-bar"></div>
    <div class="iphone-AppList" @click="onAppListClick">
      <div class="app-grid6">
        <div v-for="app in appList.slice(0,5)" :key="app.menuId" class="app-grid6-item">
          <div class="app-grid6-icon" :style="{background: app.bgColor || '#e3f1fd'}">
            <img :src="app.menuSourceLink" alt="icon" />
          </div>
          <div class="app-grid6-name">{{ app.menuName }}</div>
        </div>
        <div v-if="appList.length >= 5" class="app-grid6-item more-item">
          <div class="app-grid6-icon more-icon">
            <img src="@/assets/appConfig/menu-more.png" alt="icon" />
          </div>
          <div class="app-grid6-name">更多</div>
        </div>
      </div>
    </div>
    <div class="iphone-content">
      <div class="content-blocks-wrapper">
        <draggable v-model="localContentList" :animation="200" class="content-draggable" :ghost-class="'content-dragging'" :chosen-class="'content-chosen'">
          <div class="color-block" v-for="(item, idx) in localContentList" :key="item.id" :class="item.id" :style="getBlockStyle(item.id)">
            <span class="content-remove-btn" @click.stop="removeContentBlock(idx)">×</span>
            <template v-if="item.id === 'ConvenientService'">
              <ConvenientService />
            </template>
            <template v-else-if="item.id === 'ThreeAffairsDisclosure'">
              <ThreeAffairsDisclosure />
            </template>
            <template v-else-if="item.id === 'RuralDynamics'">
              <RuralDynamics />
            </template>
            <template v-else-if="item.id === 'Monitoring'">
              <Monitoring />
            </template>
            <template v-else-if="item.img">
              <img :src="item.img" alt="" style="width:100%;height:100%;object-fit:cover;" />
            </template>
            <template v-else>
              {{ idx+1 }}
            </template>
          </div>
        </draggable>
      </div>
      <div class="empty-content-card" @click="onEmptyCardClick">
        点击选择组件
      </div>
    </div>
    <div class="iphone-bottom-bar">
      <div class="nav-item active">
        <img src="@/assets/appConfig/home_on.png" alt="首页" class="nav-icon">
        <span class="nav-text">首页</span>
      </div>
      <div class="nav-item">
        <img src="@/assets/appConfig/mine.png" alt="我的" class="nav-icon">
        <span class="nav-text">我的</span>
      </div>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable';
import ConvenientService from './ConvenientService.vue';
import ThreeAffairsDisclosure from './ThreeAffairsDisclosure.vue';
import RuralDynamics from './RuralDynamics.vue';
import Monitoring from './Monitoring.vue';

export default {
  name: 'PhoneMockup',
  components: {
    draggable,
    ConvenientService,
    ThreeAffairsDisclosure,
    RuralDynamics,
    Monitoring
  },
  props: {
    appList: {
      type: Array,
      default: () => []
    },
    contentList: {
      type: Array,
      default: () => []
    },
    showContentListBox: {
      type: Boolean,
      default: false
    }
  },
  emits: ['app-list-click', 'empty-card-click', 'remove-content-block', 'update-content-list'],
  computed: {
    localContentList: {
      get() {
        return this.contentList;
      },
      set(value) {
        this.$emit('update-content-list', value);
      }
    }
  },
  methods: {
    onAppListClick() {
      this.$emit('app-list-click');
    },
    onEmptyCardClick() {
      this.$emit('empty-card-click');
    },
    removeContentBlock(idx) {
      this.$emit('remove-content-block', idx);
    },
    getBlockStyle(id) {
      if (id === 'ConvenientService') {
        return {
          background: 'transparent',
          width: '350px',
          height: '140px',
          padding: '0',
          display: 'flex',
          gap: '8px',
        };
      } else if (id === 'ThreeAffairsDisclosure') {
        return {
          background: 'transparent',
          width: '350px',
          height: '200px',
          padding: '0',
          display: 'flex',
        };
      } else if (id === 'RuralDynamics') {
        return {
          background: 'transparent',
          width: '350px',
          height: '200px',
          padding: '0',
          display: 'flex',
        };
      } else if (id === 'Monitoring') {
        return {
          background: 'transparent',
          width: '350px',
          height: '230px',
          padding: '0',
          display: 'flex',
        };
      }
      return {};
    }
  }
}
</script>

<style scoped>
.iphone-mockup {
  position: relative;
  width: 100%;
  max-width: 390px;
  height: 844px;
  margin: 0 auto;
  background: #f7f7f7;
  border-radius: 32px;
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1.5px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .iphone-mockup {
    max-width: 350px;
    height: 760px;
  }
}

@media (max-width: 992px) {
  .iphone-mockup {
    max-width: 320px;
    height: 680px;
  }
}

@media (max-width: 768px) {
  .iphone-mockup {
    max-width: 280px;
    height: 600px;
  }
}

.iphone-top-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 200px;
  background: #6ec1e4;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  z-index: 10;
  background-image: url("~@/assets/appConfig/swiper-def.png");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}

.iphone-bottom-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 55px;
  background: #fff;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-around;
  font-size: 16px;
  border-top: 1px solid #e0e0e0;
  z-index: 10;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  /* cursor: pointer; */
  transition: all 0.3s ease;
}

.nav-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.nav-text {
  font-size: 12px;
  color: #999;
}

.nav-item.active .nav-text {
  color: #4caf50;
}

.iphone-content {
  position: absolute;
  left: 0;
  right: 0;
  top: 370px;
  bottom: 80px;
  overflow-y: auto;
  padding: 0 12px;
  z-index: 5;
  margin-top: 25px;
}

/* 响应式调整内容区域 */
@media (max-width: 1200px) {
  .iphone-content {
    top: 340px;
    bottom: 70px;
  }
}

@media (max-width: 992px) {
  .iphone-content {
    top: 310px;
    bottom: 65px;
  }
}

@media (max-width: 768px) {
  .iphone-content {
    top: 280px;
    bottom: 60px;
  }
}

.iphone-AppList {
  position: absolute;
  left: 50%;
  top: 170px;
  transform: translateX(-50%);
  width: 90%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  padding: 20px 12px 16px 12px;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
}

.app-grid6 {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 16px 8px;
  padding: 0 8px;
}

.app-grid6-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  user-select: none;
  transition: transform 0.2s ease;
}

.app-grid6-item:hover {
  transform: translateY(-2px);
}

.app-grid6-icon {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  background: #e3f1fd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.2s ease;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.app-grid6-icon:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(1.05);
}

.app-grid6-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.app-grid6-name {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70px;
  line-height: 1.2;
}

.empty-content-card {
  width: 95%;
  height: 100px;
  margin: 0 auto 0 auto;
  background: #fff;
  border-radius: 16px;
  border: 2px solid #b6e6fd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 22px;
  cursor: pointer;
  transition: box-shadow 0.2s, border-color 0.2s;
}

.empty-content-card:hover {
  box-shadow: 0 4px 16px rgba(58, 142, 230, 0.1);
  border-color: #6ec1e4;
  color: #6ec1e4;
}

.content-blocks-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 8px;
}

.iphone-content .color-block {
  margin: 0;
}

.color-block {
  position: relative;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: #888;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin: 0 8px;
  transition: box-shadow 0.2s;
}

.content-remove-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 22px;
  height: 22px;
  background: #fff;
  border-radius: 50%;
  color: #f56c6c;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 1px 4px rgba(245, 108, 108, 0.08);
  border: 1px solid #ffeaea;
  transition: all 0.15s;
  z-index: 10;
}

.content-remove-btn:hover {
  background: #f56c6c;
  color: #fff;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.18);
}

/* 内容块拖拽相关样式 */
.content-draggable {
  display: contents;
}

.content-dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.content-chosen {
  box-shadow: 0 0 0 2px #409eff;
  cursor: grabbing;
}

.color-block {
  cursor: grab;
  transition: all 0.2s ease;
}

.color-block:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.more-item {
  opacity: 0.8;
}

.more-icon {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.more-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
</style>

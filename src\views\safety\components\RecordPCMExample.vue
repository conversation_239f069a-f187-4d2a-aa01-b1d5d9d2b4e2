<template>
  <div class="record-pcm-example">
    <el-card class="example-card">
      <div slot="header" class="card-header">
        <span>麦克风音频采集示例</span>
      </div>
      
      <div class="example-content">
        <div class="device-selector">
          <el-form :inline="true">
            <el-form-item label="设备ID:">
              <el-input 
                v-model="deviceId" 
                placeholder="请输入设备ID"
                style="width: 200px;"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        
        <div class="record-section">
          <RecordPCM 
            v-if="deviceId" 
            :device-id="deviceId"
            @recording-started="onRecordingStarted"
            @recording-stopped="onRecordingStopped"
            @error="onRecordingError"
          />
          <div v-else class="no-device-tip">
            <el-alert
              title="请先输入设备ID"
              type="warning"
              :closable="false"
              show-icon
            ></el-alert>
          </div>
        </div>
        
        <div class="info-section">
          <el-collapse v-model="activeCollapse">
            <el-collapse-item title="使用说明" name="usage">
              <div class="usage-info">
                <h4>功能说明：</h4>
                <ul>
                  <li>点击麦克风图标开始/停止录音</li>
                  <li>音频格式：PCM，单声道，48kHz采样率，16位采样深度</li>
                  <li>通过WebSocket实时传输音频数据</li>
                  <li>支持浏览器麦克风权限检测和错误处理</li>
                </ul>
                
                <h4>状态说明：</h4>
                <ul>
                  <li><span class="status-demo idle">灰色</span>：空闲状态，点击开始录音</li>
                  <li><span class="status-demo requesting">橙色</span>：正在连接WebSocket</li>
                  <li><span class="status-demo connected">蓝色</span>：已连接，准备录音</li>
                  <li><span class="status-demo recording">红色</span>：录音中，带动画效果</li>
                  <li><span class="status-demo error">红色</span>：错误状态</li>
                </ul>
                
                <h4>注意事项：</h4>
                <ul>
                  <li>首次使用需要授权麦克风权限</li>
                  <li>确保设备ID正确且设备在线</li>
                  <li>建议在HTTPS环境下使用</li>
                  <li>录音过程中请保持网络连接稳定</li>
                </ul>
              </div>
            </el-collapse-item>
            
            <el-collapse-item title="技术参数" name="technical">
              <div class="technical-info">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="音频格式">PCM</el-descriptions-item>
                  <el-descriptions-item label="声道数">单声道 (1)</el-descriptions-item>
                  <el-descriptions-item label="采样率">48000 Hz</el-descriptions-item>
                  <el-descriptions-item label="采样位深">16 位</el-descriptions-item>
                  <el-descriptions-item label="缓冲区大小">4096 samples</el-descriptions-item>
                  <el-descriptions-item label="传输协议">WebSocket</el-descriptions-item>
                </el-descriptions>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import RecordPCM from './recordPCM.vue'

export default {
  name: 'RecordPCMExample',
  components: {
    RecordPCM
  },
  data() {
    return {
      deviceId: '12345', // 示例设备ID
      activeCollapse: ['usage']
    }
  },
  methods: {
    onRecordingStarted() {
      console.log('录音已开始')
      this.$message.success('录音已开始')
    },
    
    onRecordingStopped() {
      console.log('录音已停止')
      this.$message.info('录音已停止')
    },
    
    onRecordingError(error) {
      console.error('录音错误:', error)
      this.$message.error('录音出现错误: ' + error)
    }
  }
}
</script>

<style scoped>
.record-pcm-example {
  padding: 20px;
}

.example-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.example-content {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.device-selector {
  text-align: center;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.record-section {
  text-align: center;
  padding: 40px 20px;
  background-color: #fafafa;
  border-radius: 8px;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-device-tip {
  width: 100%;
  max-width: 300px;
}

.info-section {
  margin-top: 20px;
}

.usage-info h4,
.technical-info h4 {
  color: #409eff;
  margin-bottom: 10px;
}

.usage-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.usage-info li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.status-demo {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.status-demo.idle {
  background-color: #f5f7fa;
  color: #909399;
  border: 1px solid #dcdfe6;
}

.status-demo.requesting {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #e6a23c;
}

.status-demo.connected {
  background-color: #ecf5ff;
  color: #409eff;
  border: 1px solid #409eff;
}

.status-demo.recording {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #f56c6c;
}

.status-demo.error {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #f56c6c;
}

.technical-info {
  margin-top: 10px;
}
</style>

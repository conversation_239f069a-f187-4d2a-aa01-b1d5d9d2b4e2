import request from '@/router/axios'

export const getList = (current, size,deptId, params) => {
  return request({
    url: '/api/integral-audit/declare/page',
    method: 'get',
    params: {
      ...params,
      deptId,
      current,
      size,
    }
  })
}


export const getDetail = (id) => {
  return request({
    url: '/api/integral-audit/declare/detail',
    method: 'get',
    params: {
      id,
    }
  })
}

export const declare = (data) => {
  return request({
    url: '/api/integral-audit/declare/handler',
    method: 'post',
    data
  })
}


export const getInputList = (current, size,deptId, params) => {
  return request({
    url: '/api/integral-audit/input/page',
    method: 'get',
    params: {
      ...params,
      deptId,
      current,
      size,
    }
  })
}


export const getInputDetail = (id) => {
  return request({
    url: '/api/integral-audit/input/detail',
    method: 'get',
    params: {
      id,
    }
  })
}

export const declareInput = (data) => {
  return request({
    url: '/api/integral-audit/input/handler',
    method: 'post',
    data
  })
}

<!--
 * @Description: 福建联通八闽数村运营指挥中心
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2022-07-09 14:41:28
-->
<template>
  <div class="province-screen">
    <!-- 背景图片 -->
    <div>
      <!--标题 -->
      <div class="title-header">
        <img src="/img/screen/header.png" class="title-back" alt="" />
      </div>
      <div class="title-back-desc">福建联通八闽数村运营指挥中心</div>
    </div>
    <!--左侧栏 -->
    <div class="screen-left pointer-events-auto">
      <div class="screen-left-all">
        <!-- <dv-border-box-8 dur="0"> -->
        <!--左侧栏 1-->
        <div class="left-on">
          <LeftOn />
        </div>
        <!--左侧栏 2-->
        <div class="left-center">
          <LeftCenter :branchDesc="branchDesc" />
        </div>
        <!--左侧栏 3-->
        <div class="left-bottom">
          <LeftBottom :honorList="honorList" />
        </div>
        <!-- </dv-border-box-8> -->
      </div>
    </div>
    <!--中间栏 -->
    <div class="screen-center pointer-events-auto">
      <!--右侧栏 1-->
      <div class="center-on">
        <CenterOn />
      </div>
      <!--右侧栏 3-->
      <div class="center-bottom">
        <CenterBottom />
      </div>
    </div>
    <!--右侧栏 -->
    <div class="screen-right pointer-events-auto">
      <!--右侧栏 1-->
      <div class="right-on">
        <RightOn />
      </div>
      <div class="right-center">
        <RightCenter />
      </div>
      <!--右侧栏 3-->
      <div class="right-bottom">
        <RightBottom />
      </div>
    </div>
  </div>
</template>
<script>
import LeftOn from "./leftOn";
import LeftCenter from "./leftCenter";
import LeftBottom from "./leftBottom";
import CenterOn from "./centerOn";
import CenterBottom from "./centerBottom";
import RightOn from "./rightOn";
import RightBottom from "./rightBottom";
import RightCenter from "./rightCenter";

import "./index.scss";
export default {
  components: {
    LeftOn,
    LeftCenter,
    LeftBottom,
    CenterOn,
    CenterBottom,
    RightOn,
    RightBottom,
    RightCenter,
  },
  data() {
    return {};
  },
  mounted() {
    //修改浏览器头部信息
    this.$router.$avueRouter.setTitle("省级运营大屏");
  },
};
</script>
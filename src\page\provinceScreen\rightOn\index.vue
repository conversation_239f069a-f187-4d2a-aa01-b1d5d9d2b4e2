<!--
 * @Description: 右上-平台用户
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2022-01-11 17:20:03
-->
<template>
  <div class="province-right-on-content">
    <div class="header">
      <span class="title">平台用户</span>
      <img
        src="/img/privinceScreen/titleDirection.png"
        class="title-direction"
        alt=""
      />
      <img src="/img/privinceScreen/line.png" class="left-on-line" alt="" />
    </div>
    <div class="content">
      <dv-loading v-if="loading" style="margin-top: 80px"
        >Loading...</dv-loading
      >
      <div id="echart-user" :style="{ width: '100%', height: '26vh' }"></div>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts/lib/echarts";
import "echarts/lib/chart/bar";
import "echarts/lib/chart/pie";
import "echarts/lib/component/tooltip";
import "echarts/lib/component/title";
import "echarts/lib/component/legend";
import { debounce } from "lodash";
import { getUserImportForm } from "@/api/privinceScreen/privinceScreen";
import "./index.scss";
export default {
  data() {
    return {
      loading: true,
      myChart: null,
    };
  },
  mounted() {
    this.getUserImportForm();
  },
  created() {
    this.setResize();
  },
  destroyed() {
    let self = this;
    window.removeEventListener("resize", function () {
      if (self.myChart) {
        self.myChart = null;
      }
    });
  },
  methods: {
    async getUserImportForm() {
      let result = await new Promise((resolve) => {
        this.loading = true;
        getUserImportForm().then((res) => {
          this.loading = false;
          if (res && res.data.code === 200) {
            resolve(res.data.data);
          }
        });
      });
      this.$nextTick(() => {
        this.renderEcharts(result);
      });
    },
    renderEcharts(userData) {
      const keyList = userData.map((item) => {
        return item.key;
      });
      const valueList = userData.map((item) => {
        return item.value;
      });
      const maxNum6 = Math.max(...valueList);
      const maxData = valueList.map(() => {
        return parseInt(maxNum6 * 1.2);
      });
      this.myChart = echarts.init(document.getElementById("echart-user"));
      var option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(0,46, 115, 0.3)",
            },
          },
          formatter: function (params) {
            let str = `<div style="text-align:left">${params[0].name}<br/>平台用户数: ${params[0].value} 人</div>`;
            return str;
          },
          textStyle: {
            align: "left",
            color: "#f2f3ff",
            fontSize: "16",
          },
          backgroundColor: "rgba(15, 52, 135, 0.7)",
          borderWidth: "1",
          borderColor: "#5cc1ff",
          extraCssText: "box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);",
        },
        //背景颜色，使用时请注释
        // backgroundColor:'#07253a',
        legend: {
          y: "top",
          x: "left",
          show: false,
          icon: "rect",
          textStyle: {
            color: "#fff",
          },
        },
        // color: ["#4effe8", "#0573E9"],
        grid: {
          top: "15",
          right: "1%",
          width: "93%",
          height: "82%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          show: true,

          axisLine: {
            show: false,
          },
          axisTick: { show: false },
          axisLabel: {
            textStyle: { color: "#fff" },
            rotate: -30,
            padding: [20, -20, -10, -20],
          },
          nameTextStyle: { color: "#fff" },
          data: keyList,
        },
        yAxis: [
          {
            type: "value",
            name: "",
            axisLine: {
              show: false,
            },
            min: 0,
            max: maxData[0],
            splitNumber: 2,
            axisTick: { show: false },
            axisLabel: { textStyle: { color: "#fff" } },
            nameTextStyle: { color: "#fff" },
            splitLine: {
              show: false,
              lineStyle: {
                type: "dashed",
                color: "#6a7c89",
              },
            },
          },
        ],
        series: [
          {
            name: "数值",
            type: "bar",
            barMaxWidth: 20,
            label: {
              show: true,
              color: "white",
              position: "top",
            },
            itemStyle: {
              normal: {
                color: function (d) {
                  if (d.data > 0) {
                    return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "#902ADE",
                      },
                      {
                        offset: 0.5,
                        color: "#6E31C1",
                      },
                      {
                        offset: 1,
                        color: "#343D8F",
                      },
                    ]);
                  } else {
                    return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "#902ADE",
                      },
                      {
                        offset: 0.5,
                        color: "#6E31C1",
                      },
                      {
                        offset: 1,
                        color: "#343D8F",
                      },
                    ]);
                  }
                },
                // opacity: 0.6,
                barBorderRadius: 15,
              },
              emphasis: {
                opacity: 0.6,
              },
            },
            data: valueList,
            zlevel: 9,
          },
          {
            name: "最大值",
            type: "bar",
            barMaxWidth: 20,

            itemStyle: {
              normal: { color: "#303E8C", barBorderRadius: 15 },
            },
            barGap: "-100%",
            barCategoryGap: "60%",
            data: maxData,
            animation: true,
          },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      this.myChart.setOption(option);
    },
    setResize() {
      let self = this;
      window.addEventListener(
        "resize",
        debounce(function () {
          if (self.myChart) self.myChart.resize();
        }, 200)
      );
    },
  },
};
</script>
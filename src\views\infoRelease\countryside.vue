<!--
 * @Date: 2025-01-21 14:05:39
 * @LastEditors: linqh21
 * @LastEditTime: 2025-02-07 16:50:50
 * @Description:
 * @FilePath: \src\views\infoRelease\countryside.vue
-->
<template>
  <div>
    <CommonTypeOne :moduleName="moduleName" :moduleKey="moduleKey" :moduleDic="moduleDic" :funObj="funObj" ></CommonTypeOne>
  </div>
</template>

<script>
import CommonTypeOne from '@/views/components/CommonTypeOne'
import * as funList from "@/api/infoRelease/info";
export default {
  components: {
    CommonTypeOne
  },
  data() {
    return {
      moduleName: '美丽乡村',
      moduleKey: 'countryside',
      moduleDic: 'beautifulCountryType',
      funObj:funList
    }
  },
  created() {
    // this.$nextTick(() => {
    //   this.$refs.commonTypeOne.initData()
    // })
  }
}
</script>

<style>

</style>

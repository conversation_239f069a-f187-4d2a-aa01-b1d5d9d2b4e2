<!--
 * @Description: 左中-支部概况
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2022-01-06 10:13:02
-->
<template>
  <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column; padding: 1vh 1vw; padding-right: 0.5vw; box-sizing: border-box;">
    <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column;">
      <img class="img-common" src="/img/bigScreen/modulebg.png" mode="scaleToFill" />

      <div style="display: flex; flex-direction: row; padding-right: 1vw;">
        <div class="head-idx" style="position: relative; width: 3vh; height: 3vh; font-weight: bold;">
          <img class="img-common" src="/img/bigScreen/idx.png" mode="scaleToFill" />
          02
        </div>
        <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center;">
          <img class="img-common" src="/img/bigScreen/subTitlebg.png" mode="scaleToFill" />
          <div class="head-title" style="margin-left: 1vw;">乡情风貌</div>
        </div>
      </div>
      <div style="display: flex; flex-direction: row; justify-content: space-between; padding: 0 1vw; margin-top: 2vh; margin-bottom: 1vh;">
        <div class="btn" @click="onTap(1)">
          <img class="img-common" :src="selectedType==1?'/img/bigScreen/btnSelected.png':'/img/bigScreen/btnNormal.png'" mode="scaleToFill" />
          <div class="head-idx" style="font-size: 1.2vh; font-weight: Medium;">乡村数据</div>
        </div>
        <div class="btn" @click="onTap(2)">
          <img class="img-common" :src="selectedType==2?'/img/bigScreen/btnSelected.png':'/img/bigScreen/btnNormal.png'" mode="scaleToFill" />
          <div class="head-idx" style="font-size: 1.2vh; font-weight: Medium;">一村一品</div>
        </div>
        <div class="btn" @click="onTap(3)">
          <img class="img-common" :src="selectedType==3?'/img/bigScreen/btnSelected.png':'/img/bigScreen/btnNormal.png'" mode="scaleToFill" />
          <div class="head-idx" style="font-size: 1.2vh; font-weight: Medium;">特色产业</div>
        </div>
      </div>
      <div class="content-grid">
        <div class="grid-item" v-for="(item, index) in gridData" :key="index">
          <img class="img-common" src="/img/bigScreen/grid.png" mode="scaleToFill" />
          <div class="grid-title">{{ item.label }}</div>
          <div style="display: flex; margin-top: 0.56vh;">
            <div class="grid-value">{{ item.value }}
            <span class="grid-unit">{{ item.unit }}</span></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: { branchDesc: String },
  data() {
    return {
      selectedType: 1,
      gridData: [
        {
          label: '总面积',
          value: '35.2',
          valueKey: '',
          unit: 'km2',
        },
        {
          label: '总面积',
          value: '35.2',
          valueKey: '',
          unit: 'km2',
        },
        {
          label: '总面积',
          value: '35.2',
          valueKey: '',
          unit: 'km2',
        },
        {
          label: '总面积',
          value: '35.2',
          valueKey: '',
          unit: 'km2',
        },
        {
          label: '总面积',
          value: '35.2',
          valueKey: '',
          unit: 'km2',
        },
        {
          label: '总面积',
          value: '35.2',
          valueKey: '',
          unit: 'km2',
        },
        {
          label: '总面积',
          value: '35.2',
          valueKey: '',
          unit: 'km2',
        },
        {
          label: '总面积',
          value: '35.2',
          valueKey: '',
          unit: 'km2',
        }
      ],
    };
  },
  methods: {
    onTap(type) {
      this.selectedType = type;
    },
  },
};
</script>

<style lang="scss" scoped>

.img-common {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.head-idx {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 1.7vh;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
}

.head-title {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 1.5vh;
  font-style: normal;
  font-weight: Bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
}

.btn {
  position: relative;
  width: 6vw;
  height: 2.2vh;
  cursor: pointer;
  // display: flex;
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr); // 4列
  grid-template-rows: repeat(2, 1fr);    // 2行
  gap: 1.1vh 0.7vw;                          // 行间距和列间距
  padding: 0 1vw;
}

.grid-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  // width: 5.4vw;
  height: 8.5vh;
  position: relative;
}

.grid-title {
  color: #ffffff;
  text-align: center;
  font-size: 1.1vh;
  font-weight: normal;
}

.grid-value {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 2.6vh;
  font-style: normal;
  font-weight: bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.grid-unit {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 0.74vh;
  font-style: normal;
  font-weight: bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
</style>

import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/servicePackage/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getListAll = () => {
  return request({
    url: '/api/servicePackage/list',
    method: 'get',
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/servicePackage/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/servicePackage/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/servicePackage/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/servicePackage/submit',
    method: 'post',
    data: row
  })
}

export const getDisabledLinkageMenuId = (id) => {
  return request({
    url: '/api/serviceActive/getDisabledLinkageMenuId',
    method: 'get',
    params: {
      id
    }
  })
}

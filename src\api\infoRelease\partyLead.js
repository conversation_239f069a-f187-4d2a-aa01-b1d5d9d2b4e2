/*
 * @Descrip: 美丽乡村
 * @Author: 林玥
 * @Date: 2021-06-23 14:28:15
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-24 19:17:48
 */
import request from '@/router/axios';

/**
 * @description: 请求类型
 * @param {object} params
 * @author: weixw14
 */
export const getTypeList = (params) => {
  return request({
    url: '/api/information_release/typeList',
    method: 'get',
    params,
  })
}

/**
 * @description: 请求列表
 * @param {object} params
 * @author: weixw14
 */
export const getList = (params) => {
  return request({
    url: '/api/information_release/list',
    method: 'get',
    params,
  })
}

/**
 * @description: 新增
 * @param {object} data
 * @author: weixw14
 */
export const save = (data) => {
  return request({
    url: '/api/information_release/save',
    method: 'post',
    data,
  })
}

/**
 * @description: 查看详情
 * @param {object} params
 * @author: weixw14
 */
export const detail = (params) => {
  return request({
    url: '/api/information_release/detail',
    method: 'get',
    params,
  })
}

/**
 * @description: 修改
 * @param {object} data
 * @author: weixw14
 */
export const update = (data) => {
  return request({
    url: '/api/information_release/update',
    method: 'post',
    data,
  })
}

/**
 * @description: 新增或修改
 * @param {object} data
 * @author: weixw14
 */
export const submit = (data) => {
  return request({
    url: '/api/information_release/submit',
    method: 'post',
    data,
  })
}

/**
 * @description: 删除
 * @param {object} params
 * @author: weixw14
 */
export const remove = (params) => {
  return request({
    url: '/api/information_release/remove',
    method: 'delete',
    params,
  })
}

/**
 * @description: 上传图片
 * @param {object} data
 * @author: weixw14
 */
export const upload = (data) => {
  return request({
    url: '/api/blade-resource/oss/endpoint/put-file-attach',
    method: 'post',
    data
  })
}

/**
 * @description: 发布 取消发布
 * @param {object} params
 * @author: weixw14
 */
 export const updateList = (params) => {
  return request({
    url: '/api/information_release/release',
    method: 'post',
    params,
  })
}
/**
 * @description: 获取机构树形列表
 * @param {string} tenantId
 */
export const getDeptTree = () => {
  return request({
    url: '/api/blade-system/dept/tree',
    method: 'get',
    params: {
      excludeSix:true
    }
  })
}
/**
 * @description: 获取5级树形结构
 * @param {String} tenantId
 */
export const getDeptTreeFive = (tenantId) => {
  return request({
    url: '/api/blade-system/dept/tree',
    method: 'get',
    params: {
      tenantId
    }
  })
}
<template>
  <div class="record-pcm-container">
    <div class="microphone-wrapper">
      <!-- 麦克风图标 -->
      <div
        class="microphone-icon"
        :class="microphoneClass"
        @click="toggleRecording"
        :disabled="isProcessing"
      >
        <i class="el-icon-microphone" v-if="!isRecording"></i>
        <i class="el-icon-microphone recording" v-else></i>

        <!-- 录音动画波纹 -->
        <div class="recording-waves" v-if="isRecording">
          <div class="wave wave-1"></div>
          <div class="wave wave-2"></div>
          <div class="wave wave-3"></div>
        </div>
      </div>

      <!-- 状态文字 -->
      <div class="status-text">
        <span :class="statusTextClass">{{ statusText }}</span>
      </div>

      <!-- 错误提示 -->
      <div class="error-message" v-if="errorMessage">
        <el-alert
          :title="errorMessage"
          type="error"
          :closable="false"
          show-icon
        ></el-alert>
      </div>
    </div>
  </div>
</template>

<script>
import { broadcastOn, broadcastOff } from '@/api/system/video'

export default {
  name: 'RecordPCM',
  props: {
    deviceId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      // 录音状态
      isRecording: false,
      isProcessing: false,

      // WebSocket相关
      websocket: null,
      wsUrl: '',
      taskId: null,

      // 音频相关
      audioContext: null,
      mediaStream: null,
      scriptProcessor: null,

      // 状态管理
      currentState: 'IDLE', // IDLE, REQUESTING, CONNECTED, RECORDING, ERROR
      errorMessage: '',

      // 音频配置
      audioConfig: {
        sampleRate: 48000,
        channelCount: 1,
        sampleSize: 16
      }
    }
  },
  computed: {
    microphoneClass() {
      return {
        'microphone-idle': this.currentState === 'IDLE',
        'microphone-requesting': this.currentState === 'REQUESTING',
        'microphone-connected': this.currentState === 'CONNECTED',
        'microphone-recording': this.currentState === 'RECORDING',
        'microphone-error': this.currentState === 'ERROR',
        'processing': this.isProcessing
      }
    },
    statusText() {
      switch (this.currentState) {
        case 'IDLE':
          return '点击开始录音'
        case 'REQUESTING':
          return '正在连接...'
        case 'CONNECTED':
          return '已连接，准备录音'
        case 'RECORDING':
          return '录音中...'
        case 'ERROR':
          return '连接失败'
        default:
          return '未知状态'
      }
    },
    statusTextClass() {
      return {
        'status-idle': this.currentState === 'IDLE',
        'status-requesting': this.currentState === 'REQUESTING',
        'status-connected': this.currentState === 'CONNECTED',
        'status-recording': this.currentState === 'RECORDING',
        'status-error': this.currentState === 'ERROR'
      }
    }
  },
  methods: {
    // 切换录音状态
    async toggleRecording() {
      if (this.isProcessing) return

      try {
        if (!this.isRecording) {
          await this.startRecording()
        } else {
          await this.stopRecording()
        }
      } catch (error) {
        console.error('Toggle recording error:', error)
        this.handleError('操作失败: ' + error.message)
      }
    },

    // 开始录音
    async startRecording() {
      this.isProcessing = true
      this.errorMessage = ''
      this.currentState = 'REQUESTING'

      try {
        // 1. 检查浏览器支持
        if (!this.checkBrowserSupport()) {
          throw new Error('浏览器不支持音频录制功能')
        }

        // 2. 调用接口获取WebSocket地址
        const response = await broadcastOn(this.deviceId)
        if (!response.data || !response.data.success) {
          throw new Error((response.data && response.data.msg) || '获取WebSocket地址失败')
        }

        this.wsUrl = response.data.data.wsUrl
        this.taskId = response.data.data.taskId

        // 3. 建立WebSocket连接
        await this.connectWebSocket()

        // 4. 获取麦克风权限和音频流
        await this.initAudioCapture()

        // 5. 开始录音
        this.startAudioProcessing()

        this.isRecording = true
        this.currentState = 'RECORDING'
        this.$message.success('开始录音')

      } catch (error) {
        console.error('Start recording error:', error)
        this.handleError(error.message)
      } finally {
        this.isProcessing = false
      }
    },

    // 停止录音
    async stopRecording() {
      this.isProcessing = true

      try {
        // 1. 停止音频处理
        this.stopAudioProcessing()

        // 2. 关闭WebSocket连接
        this.closeWebSocket()

        // 3. 调用接口结束广播
        if (this.taskId) {
          await broadcastOff(this.deviceId, this.taskId)
        }

        this.isRecording = false
        this.currentState = 'IDLE'
        this.taskId = null
        this.$message.success('录音已停止')

      } catch (error) {
        console.error('Stop recording error:', error)
        this.handleError(error.message)
      } finally {
        this.isProcessing = false
      }
    },

    // 检查浏览器支持
    checkBrowserSupport() {
      return !!(
        navigator.mediaDevices &&
        navigator.mediaDevices.getUserMedia &&
        (window.AudioContext || window['webkitAudioContext'])
      )
    },

    // 建立WebSocket连接
    connectWebSocket() {
      return new Promise((resolve, reject) => {
        try {
          this.websocket = new WebSocket(this.wsUrl)

          this.websocket.onopen = () => {
            console.log('WebSocket连接已建立')
            this.currentState = 'CONNECTED'
            resolve()
          }

          this.websocket.onerror = (error) => {
            console.error('WebSocket连接错误:', error)
            reject(new Error('WebSocket连接失败'))
          }

          this.websocket.onclose = (event) => {
            console.log('WebSocket连接已关闭:', event.code, event.reason)
            if (this.isRecording) {
              this.handleError('WebSocket连接意外断开')
            }
          }

          // 设置连接超时
          setTimeout(() => {
            if (this.websocket.readyState !== WebSocket.OPEN) {
              this.websocket.close()
              reject(new Error('WebSocket连接超时'))
            }
          }, 10000)

        } catch (error) {
          reject(error)
        }
      })
    },

    // 关闭WebSocket连接
    closeWebSocket() {
      if (this.websocket) {
        this.websocket.close()
        this.websocket = null
      }
    },

    // 初始化音频采集
    async initAudioCapture() {
      try {
        // 获取麦克风权限
        const constraints = {
          audio: {
            sampleRate: this.audioConfig.sampleRate,
            channelCount: this.audioConfig.channelCount,
            echoCancellation: false,
            noiseSuppression: false,
            autoGainControl: false
          },
          video: false
        }

        this.mediaStream = await navigator.mediaDevices.getUserMedia(constraints)

        // 创建音频上下文
        const AudioContext = window.AudioContext || window['webkitAudioContext']
        this.audioContext = new AudioContext({
          sampleRate: this.audioConfig.sampleRate
        })

        // 创建音频源节点
        const source = this.audioContext.createMediaStreamSource(this.mediaStream)

        // 创建脚本处理器节点 (已废弃但兼容性好)
        const bufferSize = 4096
        this.scriptProcessor = this.audioContext.createScriptProcessor(
          bufferSize,
          this.audioConfig.channelCount,
          this.audioConfig.channelCount
        )

        // 连接音频节点
        source.connect(this.scriptProcessor)
        this.scriptProcessor.connect(this.audioContext.destination)

      } catch (error) {
        if (error.name === 'NotAllowedError') {
          throw new Error('麦克风权限被拒绝，请允许访问麦克风')
        } else if (error.name === 'NotFoundError') {
          throw new Error('未找到麦克风设备')
        } else if (error.name === 'NotReadableError') {
          throw new Error('麦克风设备被占用')
        } else {
          throw new Error('获取麦克风失败: ' + error.message)
        }
      }
    },

    // 开始音频处理
    startAudioProcessing() {
      if (!this.scriptProcessor) return

      this.scriptProcessor.onaudioprocess = (event) => {
        if (!this.isRecording || !this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
          return
        }

        const inputBuffer = event.inputBuffer
        const inputData = inputBuffer.getChannelData(0) // 获取单声道数据

        // 转换为16位PCM
        const pcmData = this.convertToPCM16(inputData)

        // 通过WebSocket发送
        try {
          this.websocket.send(pcmData)
        } catch (error) {
          console.error('发送音频数据失败:', error)
        }
      }
    },

    // 停止音频处理
    stopAudioProcessing() {
      // 停止音频处理
      if (this.scriptProcessor) {
        this.scriptProcessor.onaudioprocess = null
        this.scriptProcessor.disconnect()
        this.scriptProcessor = null
      }

      // 关闭音频上下文
      if (this.audioContext) {
        this.audioContext.close()
        this.audioContext = null
      }

      // 停止媒体流
      if (this.mediaStream) {
        this.mediaStream.getTracks().forEach(track => track.stop())
        this.mediaStream = null
      }
    },

    // 将Float32Array转换为16位PCM
    convertToPCM16(float32Array) {
      const buffer = new ArrayBuffer(float32Array.length * 2)
      const view = new DataView(buffer)

      for (let i = 0; i < float32Array.length; i++) {
        // 限制在-1到1之间，然后转换为16位整数
        const sample = Math.max(-1, Math.min(1, float32Array[i]))
        view.setInt16(i * 2, sample * 0x7FFF, true) // little-endian
      }

      return buffer
    },

    // 错误处理
    handleError(message) {
      this.currentState = 'ERROR'
      this.errorMessage = message
      this.isRecording = false
      this.isProcessing = false

      // 清理资源
      this.stopAudioProcessing()
      this.closeWebSocket()

      this.$message.error(message)
    }
  },

  // 组件销毁时清理资源
  beforeDestroy() {
    this.stopAudioProcessing()
    this.closeWebSocket()
  }
}
</script>

<style scoped>
.record-pcm-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.microphone-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.microphone-icon {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 3px solid;
  user-select: none;
}

.microphone-icon:hover:not([disabled]) {
  transform: scale(1.05);
}

.microphone-icon[disabled] {
  cursor: not-allowed;
  opacity: 0.6;
}

.microphone-icon i {
  font-size: 32px;
  transition: all 0.3s ease;
}

/* 不同状态的样式 */
.microphone-idle {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
  color: #909399;
}

.microphone-idle:hover:not([disabled]) {
  background-color: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
}

.microphone-requesting {
  background-color: #fdf6ec;
  border-color: #e6a23c;
  color: #e6a23c;
  animation: pulse 1.5s infinite;
}

.microphone-connected {
  background-color: #f0f9ff;
  border-color: #409eff;
  color: #409eff;
}

.microphone-recording {
  background-color: #fef0f0;
  border-color: #f56c6c;
  color: #f56c6c;
  animation: recording-pulse 1s infinite;
}

.microphone-error {
  background-color: #fef0f0;
  border-color: #f56c6c;
  color: #f56c6c;
}

.processing {
  animation: spin 1s linear infinite;
}

/* 录音动画波纹 */
.recording-waves {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.wave {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 2px solid #f56c6c;
  border-radius: 50%;
  opacity: 0;
  animation: wave-animation 2s infinite;
}

.wave-1 {
  animation-delay: 0s;
}

.wave-2 {
  animation-delay: 0.7s;
}

.wave-3 {
  animation-delay: 1.4s;
}

/* 状态文字样式 */
.status-text {
  text-align: center;
  font-size: 14px;
  font-weight: 500;
}

.status-idle {
  color: #909399;
}

.status-requesting {
  color: #e6a23c;
}

.status-connected {
  color: #409eff;
}

.status-recording {
  color: #f56c6c;
  animation: text-blink 1s infinite;
}

.status-error {
  color: #f56c6c;
}

/* 错误消息样式 */
.error-message {
  width: 100%;
  max-width: 400px;
  margin-top: 10px;
}

/* 动画定义 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes recording-pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.4);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 0 10px rgba(245, 108, 108, 0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes wave-animation {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

@keyframes text-blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.5;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .microphone-icon {
    width: 60px;
    height: 60px;
  }

  .microphone-icon i {
    font-size: 24px;
  }

  .status-text {
    font-size: 12px;
  }
}
</style>
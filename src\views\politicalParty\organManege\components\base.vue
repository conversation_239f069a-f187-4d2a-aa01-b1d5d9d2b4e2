<template>
  <div class='' style="margin: 24px 25%;">
    <avue-form :option="option" v-model="form" ref="form" @submit="submit">
    </avue-form>
  </div>
</template>

<script>
export default {
  name: '',
  components: {},
  props: {
    dept: {
      default: '',
      type: String
    }
  },
  data () {
    return {
      form: {},
      option: {
        ubmitText: '保存',
        emptyBtn: false,
        labelWidth: 120,
        column: [
          {
            label: '党组织名称',
            prop: 'householderName',
            type: 'input',
            maxlength: 100,
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入党组织名称",
                trigger: "blur",
              }
            ],
          }, {
            label: "党组织类别",
            prop: "status",
            type: "tree",
            filter: false,
            span: 24,
            dicData: [
              { label: "党总支", value: 1, },
              { label: "党支部", value: 2, },
              { label: "党委", value: 3, },
            ],
            rules: [
              {
                required: true,
                message: "请选择出入库",
                trigger: "[blur,change]",
              },
            ],
          }, {
            label: '成立时间',
            prop: 'birthDate',
            type: 'date',
            rules: [{ required: true, message: '请选择成立时间', trigger: 'change' }],
            span: 24,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          }, {
            label: "支部书记",
            prop: "distribute",
            viewDisplay: false,
            formslot: true,
            span: 24,
            rules: [{ required: true }],
            hide: true,
          }, {
            label: '党组织介绍',
            prop: 'householderName',
            type: 'textarea',
            maxlength: 500,
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入党组织介绍",
                trigger: "blur",
              }
            ],
          }, 
        ],
      },
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  watch: {},
  created () { },
  mounted () { },
  // 方法集合
  methods: {
    submit () {
      this.$refs.form.validate((valid, done) => {
        if (valid) {
          var obj = Object.assign({}, this.form);
          obj.id = this.id;
          delete obj.$status;
          api
            .submitStock(obj)
            .then((res) => {
              if (res.data.success) {
                this.$message({
                  type: "success",
                  message: res.data.msg,
                });
              } else {
                this.$message({
                  type: "warning",
                  message: res.data.msg,
                });
              }
              done();
              this.emptyForm();
              this.initData();
            })
            .catch(() => {
              done();
            });
        }
      });
    },
  }
}
</script>
<style lang='scss' scoped></style>
import request from '@/router/axios';



/**
 * @description: 请求列表
 * @param {object} params
 * @author:
 */
export const getList = (params) => {
  return request({
    url: '/api/admin/gridStaff/page',
    method: 'get',
    params
  })
}

export const remove = (params) => {
  return request({
    url: '/api/admin/gridStaff/remove',
    method: 'post',
    params
  })
}

export const save = (data) => {
  return request({
    url: '/api/admin/gridStaff/save',
    method: 'post',
    data
  })
}

export const update = (data) => {
  return request({
    url: '/api/admin/gridStaff/update',
    method: 'post',
    data
  })
}
/**
 * @description: 发送审核
 * @param {object} params
 * @author:
 */
export const sendAudit = (params) => {
  return request({
    url: '/api/admin/gridStaff/sendAudit',
    method: 'post',
    params
  })
}

/**
 * @description: 审核
 * @param {object} params
 * @author:
 */
export const doAudit = (data) => {
  return request({
    url: '/api/admin/gridStaff/doAudit',
    method: 'post',
    data
  })
}
/**
 * @description: 审定
 * @param {object} params
 * @author:
 */
export const doAuthorize = (data) => {
  return request({
    url: '/api/admin/gridStaff/doAuthorize',
    method: 'post',
    data,
  })
}
//获取详情
export const detail = (params) => {
  return request({
    url: '/api/admin/gridStaff/detail',
    method: 'get',
    params
  })
}


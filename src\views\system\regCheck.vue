<!--
 * @Author: chenn123
 * @Date: 2021-01-06
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-08 10:38:24
 * @Description: 注册审核
-->
<template>
  <div>
    <el-row>
      <el-col :span="5">
        <div class="box">
          <el-scrollbar>
            <basic-container>
              <avue-tree ref="tree" :option="treeOption" :data="treeData" @node-click="nodeClick" node-key="id" :current-node-key="treeGridId" />
            </basic-container>
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="19">
        <basic-container>
          <avue-tabs :option="option1" @change="handleChange"></avue-tabs>
          <div>
            <avue-crud :option="option2" :table-loading="loading" :data="data" :page.sync="page" :permission="permissionList" v-model="form" ref="crud" :before-open="beforeOpen" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
              <template slot="isPass" slot-scope="scope">
                <!-- <span>{{ scope.row.auditStatus == 1 ?'通过':'不通过' }}</span> -->
                <el-tag v-if="scope.row.auditStatus == 1" type="success">通过</el-tag>
                <el-tag v-else type="danger">不通过</el-tag>
              </template>
              <template slot="isPassForm">
                <el-tag v-if="form.auditStatus == 1" type="success">通过</el-tag>
                <el-tag v-else type="danger">不通过</el-tag>
              </template>
              <template slot="auditRemarkForm">
                <span v-if="form.auditStatus == 1">同意</span>
                <span v-else style="color:red">{{form.auditRemark}}</span>
              </template>
              <!-- <template slot="menuLeft">
                        <el-button type="danger" size="small" plain icon="el-icon-delete"
                                   v-if="type.prop==='tab2' && permission.system_regCheck_delete" @click="remove">删 除
                        </el-button>
                    </template> -->
              <template slot-scope="scope" slot="menu">
                <div class="cell">
                  <button v-if="permission.system_regCheck_view" type="button" class="el-button el-button--text el-button--small" @click="$refs.crud.rowView(scope.row)">
                    <i class="el-icon-view"></i>
                    <span>查看</span>
                  </button>
                  <el-dropdown v-if="type.prop==='tab1' && permission.system_regCheck_check" style="margin-left: 20px;">
                    <el-button type="primary" size="mini">
                      审核<i class="el-icon-arrow-down el-icon--right"></i>
                    </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item @click.native="checkPass(scope.row,1)">通过</el-dropdown-item>
                      <el-dropdown-item @click.native="checkPass(scope.row,2)">不通过</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                  <!-- <button v-if="type.prop==='tab2' && permission.system_regCheck_delete" type="button"
                                    class="el-button el-button--text el-button--small"
                                    @click="remove(scope.row,scope.index)">
                                <i class="el-icon-delete"></i>
                                <span>删除</span>
                            </button> -->
                </div>
              </template>
              <!-- <template slot-scope="" slot="menuLeft">
                      <el-button type="primary"
                        size="small"
                        @click="showAutoAudit">审核设置</el-button>
                    </template> -->
            </avue-crud>
          </div>
        </basic-container>
        <regCheckDetail :detail="detail" v-if="dialogFormVisible"></regCheckDetail>
      </el-col>
    </el-row>
    <el-dialog title="确定审核不通过？" :visible.sync="rejectVisible" width="25%" :modal="true" top="15%" :append-to-body="true" :close-on-click-modal="false" :before-close="beforeClose" :destroy-on-close="true">
      <el-row>
        <el-col :span="6"><span>审核意见：</span></el-col>
        <el-col :span="18">
          <el-input type="textarea" maxlength="200" show-word-limit style="padding:0 0 25px 0" placeholder='请填写不通过意见' v-model="rejectText" rows="4" resize="none"></el-input>
        </el-col>
      </el-row>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="beforeClose">取 消</el-button>
        <el-button size="small" type="primary" @click="reject">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="审核设置" :visible.sync="auditVisible" width="25%" :modal="false" top="15%" :append-to-body="true" :close-on-click-modal="false" :destroy-on-close="true">
      <div>
        自动审核：<el-switch v-model="autoCheck" active-value="1" inactive-value="0"></el-switch>
        <div class="autoAuditTip">注：开启自动审核后，村民在小程序注册后，将直接加入村庄。请谨慎使用！</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="auditVisible=false">取 消</el-button>
        <el-button size="small" type="primary" @click="setAutoAudit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getRegList, getRegDetail, updateReg, regRemove, getAutoState, setAutoAudit } from "@/api/system/regCheck";
import { mapGetters } from "vuex";
import regCheckDetail from './components/regCheckDetail'
import { getDeptTree, getDeptUserTree } from "@/api/system/dept";
export default {
  components: {
    regCheckDetail
  },
  data() {
    return {
      type: {},
      option1: {
        column: [{
          label: '待审核',
          prop: 'tab1',
        }, {
          label: '已审核',
          prop: 'tab2',
        }]
      },
      form: {},
      query: {},
      detail: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      dialogFormVisible: false,
      selectionList: [],
      option2: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        border: true,
        index: true,
        viewBtn: true,
        editBtn: true,
        delBtn: true,
        labelWidth: 120,
        selection: false,
        searchMenuSpan: 4,
        menu: true,
        column: [

          {
            label: "用户姓名",
            prop: "realName",
            type: "input",
            search: true,
            maxlength: 20,
          },
          {
            label: "用户昵称",
            prop: "name",
            type: "input",
            hide: true,
            search: true,
            maxlength: 20,
          },
          {
            label: "手机号码",
            prop: "phone",
            type: "input"
          },
          {
            label: "所属村镇",
            prop: "deptName",
            type: "input"
          },
          {
            label: "注册时间",
            prop: "createTime",
            type: "input",
            span: 24,
          },
          {
            label: "审核结果",
            prop: "isPass",
            type: "input",
            hide: true,
            viewDisplay: false,
          },
          {
            label: "审核时间",
            prop: "auditTime",
            type: "input",
            hide: true,
            viewDisplay: false,
          },
          {
            label: "审核人",
            prop: "auditUserName",
            type: "input",
            hide: true,
            viewDisplay: false,
          },
          {
            label: "审核意见",
            prop: "auditRemark",
            type: "textarea",
            span: 24,
            hide: true,
            viewDisplay: false,
          },
        ]
      },
      rejectVisible: false,
      rejectText: '',
      rejectId: '',
      data: [],
      auditVisible: false,
      autoCheck: "0",

      treeGridId: "",
      treeData: [],
      treeOption: {
        nodeKey: "id",
        defaultExpandAll: false,
        addBtn: false,
        menu: false,
        size: "small",
        props: {
          label: "title",
          value: "id",
          children: "children",
        },
      },
    }
  },
  created() {
    this.type = this.option1.column[0];
  },
  watch: {
    // 监听选项卡，为已审核页展示‘审核结果’列
    'type.prop': {
      handler(val) {
        console.log(val)
        // let isPass = this.findObject(this.option2.column, 'isPass')
        const column = this.findObject(this.option2.column, "isPass");
        const column1 = this.findObject(this.option2.column, "auditTime");
        const column2 = this.findObject(this.option2.column, "auditUserName");
        const column3 = this.findObject(this.option2.column, "auditRemark");
        // column.dicData = res.data.data;
        if (val === 'tab1') {
          column.hide = true
          column.viewDisplay = false
          column1.viewDisplay = false
          column2.viewDisplay = false
          column3.viewDisplay = false
          console.log(column)
        }
        else {
          column.hide = false
          column.viewDisplay = true
          column1.viewDisplay = true
          column2.viewDisplay = true
          column3.viewDisplay = true
          this.$refs.crud.refreshTable()
        }
      }
    }
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: false
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      getDeptUserTree().then(res => {
        this.treeData = res.data.data;
      });
    },
    nodeClick(data) {
      this.treeGridId = data.id;
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    /********选项卡********/
    handleChange(column) {
      this.data = []
      this.type = column
      this.page.currentPage = 1
      this.$refs.crud.searchReset()
      this.onLoad(this.page)
    },
    /*******业务处理*******/
    isShowDetail(isShowDialog, isRefresh) {
      this.dialogFormVisible = isShowDialog
      if (isRefresh) {
        this.refreshChange();
      }
    },
    // view(row) {
    //   getRegDetail(row.id).then(res => {
    //     this.detail = res.data.data;
    //     console.log(this.detail)
    //   })
    //   this.isShowDetail(true);
    // },
    remove(row, index) {
      if (index !== undefined) {
        this.$confirm('是否删除此数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          regRemove(row.id).then(() => {
            this.$message.success('删除成功');
            this.onLoad(this.page);
          })
        })
      } else {
        if (this.ids === '') {
          this.$message.warning('请至少选择一项数据删除');
          return false;
        }
        this.$confirm('是否删除选中数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          regRemove(this.ids).then(() => {
            this.$message.success('删除成功');
            this.onLoad(this.page);
          })
        })
      }
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    searchReset() {
      this.query = {};
      this.page.currentPage = 1;
      this.$refs.tree.setCurrentKey()
      this.treeGridId = "";
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    async beforeOpen(done, type) {
      if (type === "add") {
        this.form = {};
        this.dataPoints = []
        done();
        return;
      }
      if (["edit", "view"].includes(type)) {
        getRegDetail(this.form.id)
          .then((res) => {
            this.form = res.data.data;
            done();
          });
      }
    },
    onLoad(page, params = {}) {
      this.detail = {}
      this.loading = true;
      const _params = { ...params };
      // console.log(_params);
      if (this.type.prop == 'tab1') {
        _params.status = 0
      } else if (this.type.prop == 'tab2') {
        _params.status = 1
      }
      getRegList(page.currentPage, page.pageSize, this.treeGridId, Object.assign(_params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
      // console.log(this.option2.column[6])
    },
    checkPass(row, num) {
      console.log(row)
      if (num === 1) {
        this.$confirm("确定审核通过？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          let params = {
            id: row.id,
            reason: '',
            isPass: true
          }
          updateReg(Object.assign(params)).then(() => {
            this.$message.success('审核成功')
            this.onLoad(this.page)
          })
        })
      }
      else if (num === 2) {
        this.rejectId = row.id;
        this.rejectVisible = true;
      }
    },
    reject() {
      let params = {
        id: this.rejectId,
        auditRemark: this.rejectText,
        isPass: false
      }
      updateReg(Object.assign(params)).then(() => {
        this.$message.success('审核成功')
        this.beforeClose();
        this.onLoad(this.page)
      })
    },
    beforeClose() {
      this.rejectId = '';
      this.rejectText = '';
      this.rejectVisible = false
    },
    showAutoAudit() {
      getAutoState().then(res => {

        if (res.data.code === 200) {
          this.autoCheck = res.data.data
          console.log(res.data.data)
          this.auditVisible = true
        }
      })
      // this.auditVisible = true
    },
    setAutoAudit() {
      setAutoAudit(this.autoCheck).then(res => {
        if (res.data.code === 200) {
          this.$message.success('设置成功')
          this.auditVisible = false
        }
      }).finally(() => {
        this.auditVisible = false
      })
    }
  }
};
</script>

<style scoped>
.autoAuditTip {
  margin-top: 20px;
  color: brown;
  font-size: 12px;
}
</style>

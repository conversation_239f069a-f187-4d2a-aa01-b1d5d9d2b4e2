/*
 * @Description: 统一接口方法
 * @Author: chenz76
 * @Date: 2022-01-21 11:40:22
 * @LastEditors: linqh21
 * @LastEditTime: 2025-06-25 14:51:47
 */
import { downloadStatistics } from "@/api/common/common"
import { myMessage } from './myMessage.js';
/**
 * @description 外部调用导出函数
 * @param {string} url 导出地址
 * @param {object} params 导出参数，跟在地址栏
 * @param {object} data 请求的json数据
 */
export const handleDownload = async (url, params ={},data={}) => {
    let flag = false;
    return await new Promise((resolve) => {
        downloadStatistics(url, params,data).then((res) => {
            if (res) {
                flag = downloadFile(res);
                if (flag) {
                    myMessage.success("数据导出成功")
                    resolve(true);
                } else {
                    resolve(false)
                }
            }
        }).catch((error) => {
            myMessage.error(
                error.toString().replace("Error:", "导出失败：") || "导出失败"
            );
            resolve(false);
        });
    });
}



export const handleDownloadFile = async (url, params ={},data={}) => {
    let flag = false;
    return await new Promise((resolve) => {
        downloadStatistics(url, params,data).then((res) => {
            if (res) {
                flag = downloadFile(res);
                if (flag) {
                    // myMessage.success("数据导出成功")
                    resolve(true);
                } else {
                    resolve(false)
                }
            }
        }).catch((error) => {
            myMessage.error(
                error.toString().replace("Error:", "导出失败：") || "导出失败"
            );
            resolve(false);
        });
    });
},



/**
 * @description 导出方法
 * @param {*} res
 * @param {*} that
 * @returns
 */
downloadFile = (res) => {
    let data = res.data;
    // 此处提示自定义提示语，从header中获取
    if (res.headers["errormsg"] || !data) {
        myMessage.error(decodeURI(res.headers["errormsg"]) || "导出失败");
        return false;
    }
    let url = window.URL.createObjectURL(new Blob([data]));
    let link = document.createElement("a");
    link.style.display = "none";
    link.href = url;
    // 文件名在后端设置
    link.setAttribute(
        "download",
        decodeURI(res.headers["content-disposition"]).split("filename=")[1]
    );
    document.body.appendChild(link);
    link.click();
    return true;
}

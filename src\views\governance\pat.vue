<!--
 * @Description: 随手拍界面代码
 * @Author: lim126
 * @Date: 2021-06-23 20:33:24
 * @LastEditors: linzq33
 * @LastEditTime: 2025-07-01 09:54:06
-->

<template>
  <div>
    <el-row>
      <el-col :span="5">
        <div class="box">
          <el-scrollbar>
            <basic-container v-loading="treeLoading">
              <avue-tree :option="treeOption" :data="treeData" :filter-node-method="filterNodeMethod"
                @node-click="nodeClick" />
            </basic-container>
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="19">
        <basic-container>
          <avue-crud :option="option" :table-loading="loading" :data="data" ref="crud" v-model="form"
            :permission="permissionList" :before-open="beforeOpen" :before-close="beforeClose" :page.sync="page"
            :upload-before="uploadBefore" :upload-error="uploadError" :upload-after="uploadAfter"
            :upload-delete="uploadDelete" @search-change="searchChange" @search-reset="searchReset"
            @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange"
            @row-update="rowUpdate" @on-load="onLoad">
            <template slot-scope="scope" slot="menu">
              <el-button type="text" size="small" v-if="permission.pat_view" plain class="none-border"
                @click.stop="$refs.crud.rowView(scope.row, scope.index)"><i class="el-icon-view"></i> 查看
              </el-button>
              <el-button type="text" size="small" v-if="scope.row.status != 2 && permission.pat_accept" plain
                class="none-border" @click.stop="$refs.crud.rowEdit(scope.row)"><i class="el-icon-bell"></i> 去处理
              </el-button>
            </template>
          </avue-crud>
        </basic-container>
      </el-col>
    </el-row>
  </div>

</template>

<script>
import { mapGetters } from "vuex";
import {
  getPatList,
  getPatDetail,
  rowUpdate,
  getHandlerList,
} from "@/api/governance/index";
import { getDeptTree } from "@/api/infoRelease/partyLead"
import { getDictionary } from "@/api/system/dict";

import axios from "axios";
const picLimit = 9;
const sizeLimit = 10;
export default {
  data () {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      picLimit,
      sizeLimit,
      option: {
        align: "center",
        height: "auto",
        calcHeight: 30,
        tip: false,
        // simplePage: true,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        selection: false,
        editBtn: false,
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        dialogWidth: 900,
        menuWidth: 150,
        dialogClickModal: false,
        editTitle: "办理",
        updateBtnText: "提交",
        dialogCustomClass: "pat-dialog",
        column: [
          {
            label: "状态",
            prop: "status",
            search: true,
            type: "select",
            dicData: [
              { label: "已提交", value: 0 },
              { label: "已受理", value: 1 },
              { label: "已办结", value: 2 },
            ],
            props: {
              label: "label",
              value: "value",
            },
            dataType: "number",
            editDisplay: false,
            viewDisplay: false,
          }, {
            label: "标题",
            prop: "title",
            search: true,
            searchSpan: 6,
            editDisabled: true,
            showWordLimit: true,
            maxlength: 32,
            span: 24,
          }, {
            label: "信息类型",
            prop: "type",
            editDisabled: true,
            type: "select",
            search: true,
            dicData: [
              { label: "小刺猬", value: 0 },
              { label: "点赞台", value: 1 },
            ],
            props: {
              label: "label",
              value: "value",
            },
          }, {
            label: "提交人",
            prop: "createName",
            editDisabled: true,
            showWordLimit: true,
            maxlength: 10,
          }, {
            label: "地址",
            prop: "address",
            editDisabled: true,
            showWordLimit: true,
            maxlength: 50,
            span: 24,
          }, {
            label: "详情图片",
            prop: "pictureLinks",
            dataType: "array",
            disabled: true,
            hide: true,
            span: 24,
            type: "upload",
            listType: "picture-card",
            propsHttp: {
              res: "data",
              url: "link",
            },
          }, {
            label: "提交时间",
            prop: "createTime",
            editDisabled: true,
            span: 24,
          },
        ],
      },
      operation: {
        label: '处理操作',
        prop: 'operation',
        span: 12,
        type: "select",
        dicData: [
          { label: "转办", value: 1 },
          { label: "办结", value: 2 },
        ],
        change: ({ column, value }) => {
          this.handleOperation(value)
        },
        hide: true,
      },
      // 办理流程
      process: [
        // 转派/办结流程
        {
          label: '处理流程',
          prop: 'transferList',
          type: 'dynamic',
          labelWidth: 0,
          hide: true,
          span: 24,
          children: {
            align: 'center',
            type: 'form',
            index: false,
            headerAlign: 'center',
            labelWidth: 140,
            column: [
              {
                label: '处理操作',
                prop: 'operation',
                span: 12,
                type: "select",
                dicData: [
                  { label: "转办", value: 1 },
                  { label: "办结", value: 2 },
                ],
              },
              { label: '处理人', prop: 'userName', span: 12 },
              { label: '转办人', prop: 'transferName', span: 12 },
              { label: '转办时间', prop: 'updateTime', span: 12 },
              { label: '转办说明', prop: 'transferComment', span: 24 },
            ]
          }
        },
        {
          label: '处理流程',
          prop: 'completedList',
          type: 'dynamic',
          labelWidth: 0,
          hide: true,
          span: 24,
          children: {
            align: 'center',
            type: 'form',
            index: false,
            headerAlign: 'center',
            labelWidth: 140,
            column: [
              {
                label: '处理操作',
                prop: 'operation',
                span: 12,
                type: "select",
                dicData: [
                  { label: "转办", value: 1 },
                  { label: "办结", value: 2 },
                ],
              },
              { label: '处理人', prop: 'userName', span: 12 },
              { label: '事项分类', prop: 'itemTypeName', span: 12 },
              {
                label: '随手拍广场显示', prop: 'isSquare', span: 12, type: "radio",
                dicData: [
                  { label: "否", value: 0 },
                  { label: "是", value: 1 },
                ],
              },
              {
                label: "处理图片",
                prop: "handleLinks",
                hide: true,
                span: 24,
                type: "upload",
                listType: "picture-card",
                accept: ".jpeg,.jpg,.png",
                tip: `最多只能上传${picLimit}张jpg/png格式图片，且不超过${sizeLimit}Mb`,
                loadText: "图片上传中…请稍等",
                action: "/api/blade-resource/oss/endpoint/put-file-attach",
                propsHttp: {
                  res: "data",
                  url: "link",
                },
              },
              { label: '处理意见', prop: 'handleOpinion', span: 24 },

            ]
          }
        }
      ],
      // 转办
      transfer: [
        {
          label: "处理人",
          prop: "transferUser",
          hide: true,
          type: "select",
          dicData: [],
          props: { label: "realName", value: "id", desc: 'phone' },
          dataType: "string",
          span: 12,
          rules: [{ required: true, message: "请选择处理人", trigger: "blur" }],
          hide: true,
        }, {
          label: "转办说明",
          prop: "transferComment",
          type: "textarea",
          span: 24,
          minRows: 2,
          hide: true,
          showWordLimit: true,
          maxlength: 100,
          rules: [{ required: true, whitespace: true, message: "请输入转办说明", trigger: "blur" }],
        },
      ],
      // 办结
      completed: [
        {
          label: "事项分类",
          prop: "itemTypeKey",
          hide: true,
          type: "select",
          dicData: [],
          props: { label: "dictValue", value: "dictKey" },
          dataType: "number",
          span: 12,
          rules: [{ required: true, message: "请选择事项分类", trigger: "blur" }],
        }, {
          label: "随手拍广场显示",
          prop: "isSquare",
          span: 12,
          hide: true,
          type: "radio",
          dicData: [
            { label: "否", value: 0 },
            { label: "是", value: 1 },
          ],
          rules: [
            {
              required: true,
              message: "请选择随手拍广场显示",
              trigger: "blur",
            },
          ],
        }, {
          label: "处理意见",
          prop: "handleOpinion",
          type: "textarea",
          span: 24,
          minRows: 2,
          hide: true,
          showWordLimit: true,
          maxlength: 100,
          rules: [{ required: true, whitespace: true, message: "请输入处理意见", trigger: "blur" }],
        }, {
          label: "处理图片",
          prop: "handleLink",
          hide: true,
          span: 24,
          type: "upload",
          listType: "picture-card",
          accept: ".jpeg,.jpg,.png",
          tip: `最多只能上传${picLimit}张jpg/png格式图片，且不超过${sizeLimit}Mb`,
          loadText: "图片上传中…请稍等",
          action: "/api/blade-resource/oss/endpoint/put-file-attach",
          propsHttp: {
            res: "data",
            url: "link",
            // name: "attachId",
          },
          rules: [
            {
              required: true,
              message: "请上传照片",
              trigger: "blur",
            },
          ],
        },
      ],
      data: [],
      // 组织树
      deptId: "",
      treeLoading: false,
      treeOption: {
        nodeKey: "id",
        addBtn: false,
        menu: false,
        size: "small",
        props: {
          label: "title",
          value: "value",
          children: "children",
        },
      },
      treeData: [],
    };
  },
  computed: {
    ...mapGetters(["permission", "flowRoutes", "userInfo"]),
    permissionList () {
      return {
        acceptBtn: this.vaildData(this.permission.pat_accept, false),
        doneBtn: this.vaildData(this.permission.pat_done, false),
        viewBtn: this.vaildData(this.permission.pat_view, false),
      };
    },
  },
  watch: {
  },
  created () {
    this.treeLoading = true
    this.initTreeData(this.userInfo.tenant_id)
  },
  methods: {
    handleOperation (val) {
      if (val == '') return;
      if (this.$refs.crud.clearValidate) {
        this.$refs.crud.clearValidate();
      }
      if (val == 1) {
        // 转办
        const column = this.findObject(this.option.column, "itemTypeKey");
        if (column) {
          this.option.column.splice(this.option.column.indexOf(column), 4)
        }
        this.option.column = [...this.option.column, ...this.transfer]
      } else {
        const column = this.findObject(this.option.column, "transferUser");
        if (column) {
          this.option.column.splice(this.option.column.indexOf(column), 2)
        }
        this.option.column = [...this.option.column, ...this.completed]
      }
    },
    searchReset () {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange (params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    beforeOpen (done, type) {
      if (["edit", "view"].includes(type)) {
        getPatDetail(this.form.id).then((res) => {
          this.form = res.data.data;
          if (type == "edit") {
            if (this.form.isSquare == -1) this.form.isSquare = 0;
            this.option.column = [...this.option.column, ...[this.operation]]
          }
          // 转办/办理流程处理
          if (type == "view") {
            // 审核处理
            const transfer = res.data.data.accessList.some(item => item.operation == 1)
            const completed = res.data.data.accessList.some(item => item.operation == 2)
            if (transfer) this.option.column = [...this.option.column, ...[this.process[0]]]
            if (completed) this.option.column = [...this.option.column, ...[this.process[1]]]

            const transferList = []
            const completedList = []
            for (let i = 0; i < res.data.data.accessList.length; i++) {
              const e = res.data.data.accessList[i]

              if (e.operation == 1) {
                transferList.push(e)
              } else {
                completedList.push(e)
              }
            }
            this.form.transferList = transferList
            this.form.completedList = completedList

            let arr = [];
            const handleLinks = this.form.handleLinks
            const handleIds = this.form.handleIds
            if (handleLinks && handleIds && handleIds.length == handleLinks.length) {
              this.form.handleIds.forEach((cv, index) => {
                arr.push({ label: cv, value: this.form.handleLinks[index] });
              });
              this.form.handleLink = arr; // 显示数据
              this.form.handleLinks = arr; // 编辑后提交保存数据
            } 
          }
        }).finally(() => {
          done();
        });
      }
    },
    beforeClose (done, type) {
      if (type == "view") {
        // 移除流程字段
        const column = this.findObject(this.option.column, "transferList");
        if (column) {
          this.option.column.splice(this.option.column.indexOf(column))
        }
        const column2 = this.findObject(this.option.column, "completedList");
        if (column2) {
          this.option.column.splice(this.option.column.indexOf(column2))
        }
      }
      if (type == "edit") {
        // 转办
        const column = this.findObject(this.option.column, "itemTypeKey");
        if (column) {
          this.option.column.splice(this.option.column.indexOf(column) - 1)
        }
        const column2 = this.findObject(this.option.column, "transferUser");
        if (column2) {
          this.option.column.splice(this.option.column.indexOf(column2) - 1)
        }
        // 处理操作字段移除
        const column3 = this.findObject(this.option.column, "operation");
        if (column3) {
          this.option.column.splice(this.option.column.indexOf(column3) - 1)
        }
      }
      done();
    },
    rowUpdate (row, index, done, loading) {
      let data = {}
      if (row.operation == 1) {
        // 转办
        data = {
          operation: row.operation,
          transferUser: row.transferUser,
          transferComment: row.transferComment,
        }
      } else {
        // 办结
        data = {
          handleIds: row.handleLinks.map((cv) => cv.label).join(),
          handleOpinion: row.handleOpinion,
          isSquare: row.isSquare,
          itemTypeKey: row.itemTypeKey,
          operation: row.operation,
        }
      }
      rowUpdate(row.id, data).then(
        () => {
          this.refreshChange();
          done();
          this.$message.success("操作成功");
        },
        (error) => {
          this.$message.error(error);
          loading();
        }
      );
    },
    uploadBefore (file, done, loading) {
      let isLength = true;
      if (this.form.handleLink) {
        const length = this.form.handleLink.length;
        isLength = length < this.picLimit;
        if (this.form.handleLink.length >= this.picLimit) {
          this.$message.error(`最多只能上传${this.picLimit}张照片`);
          loading();
          return false;
        }
      }
      const isJpgOrPng = file.type == "image/jpeg" || file.type == "image/png";
      if (!isJpgOrPng) {
        this.$message.error("请上传 JPG/PNG 格式的图片!");
        loading();
        return false;
      }
      const sizeLimit = file.size / 1024 / 1024 < this.sizeLimit;
      if (!sizeLimit) {
        this.$message.error(`上传照片大小不能超过 ${this.sizeLimit}MB!`);
        loading();
        return false;
      }
      if (isLength && isJpgOrPng && sizeLimit) {
        axios.defaults.timeout = 600000; //上传前设置超时时间为10分钟
        done();
      } else loading();
    },
    uploadAfter (res, done, loading) {
      // handleLink是form展示的[{label,value}],handleIds是记录每次上传完的id, handleLinks是存储提交需要的数据
      axios.defaults.timeout = 10000;
      if (!res || !res.attachId) {
        this.$message.error("上传失败");
        loading();
      } else {
        this.$message.success("上传成功");
        if (!this.form.handleLinks) {
          this.form.handleLinks = [];
          this.form.handleLinks.push({ label: res.attachId, value: res.link });
        } else {
          this.form.handleLinks.push({ label: res.attachId, value: res.link });
        }
        done();
      }

    },
    // 删除已上传文件
    uploadDelete (file, column) {
      // console.log({ file, column });
      return this.$confirm("是否确定移除该图片？").then(() => {
        this.form.handleLink.splice(file.uid, 1);
        this.form.handleLinks.splice(file.uid, 1);
      });
    },
    uploadError (error) {
      axios.defaults.timeout = 10000;
      this.$message.error(error);
    },
    currentChange (currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange (pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange () {
      this.onLoad(this.page, this.query);
    },
    onLoad (page, params = {}) {
      params.deptId = this.deptId
      this.loading = true;
      getPatList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
      });
    },

    filterNodeMethod (value, data) {
      if (!value) return true
      return data.deptName.indexOf(value.trim()) !== -1
    },
    nodeClick (data) {
      this.query.deptId = data.id
      this.deptId = data.id
      this.page.currentPage = 1
      this.onLoad(this.page, this.query)
    },
    async initTreeData (tenantId) {
      this.treeData = (await getDeptTree(tenantId)).data.data
      this.treeLoading = false
      // 字典获取
      getDictionary({ code: 'snapshot_item_type' }).then(res => {
        const column = this.findObject(this.completed, "itemTypeKey");
        column.dicData = res.data.data;
      })
      getHandlerList().then(res => {
        const column = this.findObject(this.transfer, "transferUser");
        column.dicData = res.data.data;
      })
    },
  },
};
</script>
<style lang="scss" scoped>
.none-border {
  border: 0;
  background-color: transparent !important;
}

.avue-dialog {
  .pat-dialog {
    .el-textarea {
      span {
        background: transparent;
        height: 25px;
        line-height: 25px;
        bottom: -25px !important;
      }
    }

    .uploadToDisable {
      display: none;
    }
  }
}
</style>

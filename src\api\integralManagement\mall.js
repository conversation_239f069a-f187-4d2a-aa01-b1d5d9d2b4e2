/*
 * @Description:
 * @Author: wangyy553
 * @Date: 2021-12-22 10:38:24
 * @LastEditors: wangyy553
 * @LastEditTime: 2021-12-28 16:57:11
 */
import request from '@/router/axios'


/**
 * @description: 获得商品分页
 * @param {int}current
 * @param {int}size
 * @param {object}params
 */
export const getList = (current, size, params) => {
  return request({
    url: '/api/admin/integral-goods/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

/**
 * @description: 商品详情
 * @param {int} id
 */
export const getDetail = (id) => {
  return request({
    url: '/api/admin/integral-goods/' + id,
    method: 'get',
  })
}
/**
 * @description: 字典
 * @param {object} params
 */
export const getDictionary = (params) => {
  return request({
    url: '/api/blade-system/dict/dictionary',
    method: 'get',
    params,
  })
}

/**
 * @description: 裁剪图片上传
 * @param {file} data
 */
export const upload = (data) => {
  return request({
    url: '/api/blade-resource/oss/endpoint/put-file-attach',
    method: 'post',
    data
  })
}

/**
 * @description: 新增
 * @param {object} data
 */
export const add = (data) => {
  return request({
    url: '/api/admin/integral-goods',
    method: 'post',
    data
  })
}

/**
 * @description: 编辑
 * @param {object} data
 */
export const edit = (data) => {
  return request({
    url: '/api/admin/integral-goods',
    method: 'put',
    data
  })
}

/**
 * @description: 获取商品已同步部门列表
 * @param {int} id
 */
export const getSync = (id) => {
  // console.log(id, 'id')
  return request({
    url: '/api/admin/integral-goods/copy/list',
    method: 'get',
    params: {
      id: id
    }
  })
}
/**
 * @description: 同步部门列表
 * @param {object} data
 */
export const submitSync = (data) => {
  // console.log(id, 'id')
  return request({
    url: '/api/admin/integral-goods/copy',
    method: 'post',
    data
  })
}

/**
 * @description: 商品出入库记录分页
 * @param {int}current
 * @param {int}size
 * @param {int}id
 */
export const getGoodsStock = (current, size, id) => {
  return request({
    url: '/api/admin/integral-goods/stock/page',
    method: 'get',
    params: {
      current,
      size,
      id
    }
  })
}

/**
 * @description: 商品出入库
 * @param {object}data
 */
export const submitStock = (data) => {
  return request({
    url: '/api/admin/integral-goods/stock',
    method: 'put',
    data
  })
}

/**
 * @description: 上下架商品
 * @param {string}ids
 * @param {string}status
 */
export const changeStatus = (ids, status) => {
  return request({
    url: '/api/admin/integral-goods/shelf',
    method: 'put',
    params: {
      ids,
      status
    }
  })
}

/**
 * @description: 删除商品
 * @param {int}id
 */
export const remove = (id) => {
  return request({
    url: '/api/admin/integral-goods/' + id,
    method: 'delete',
  })
}

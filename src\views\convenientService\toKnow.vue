<template>
  <el-row ref="test">
    <el-col :span="5" v-loading="treeLoading">
      <div class="box">
        <el-scrollbar>
          <basic-container>
            <avue-tree :option="treeOption" :data="treeData" @node-click="nodeClick" show-checkbox>
            </avue-tree>
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :span="19">
      <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :search.sync="query"
          :permission="permissionList" v-model="form" ref="crud" :before-open="beforeOpen" :upload-before="uploadBefore"
          :upload-after="uploadAfter" :upload-delete="uploadDelete" :upload-exceed="uploadExceed"
          @row-update="rowUpdate" @row-save="rowSave" @row-del="rowDel" @search-change="searchChange"
          @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange"
          @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad" class="directSupply-dialog">
          <!-- 左侧菜单按钮 -->
          <template slot="menuLeft">
            <el-button type="danger" size="small" icon="el-icon-delete" plain @click="handleDelete"
              v-if="permissionList.delBtn">
              删除
            </el-button>
          </template>
          <template slot-scope="{}" slot="legalCompletionForm">
            <el-input v-model.number="form.legalCompletion" maxlength="9"></el-input>
          </template>
          <template slot-scope="{}" slot="promiseCompletionForm">
            <el-input v-model.number="form.promiseCompletion" maxlength="9"></el-input>
          </template>
          <!-- 行操作按钮 -->
          <template slot-scope="{ type, size, row, index }" slot="menu">
            <!-- <el-button :type="type" :size="size" @click.stop="preview(row)">预览</el-button> -->
            <el-button :type="type" :size="size" @click.stop="$refs.crud.rowView(row, index)">
              查看
            </el-button>
            <el-button :type="type" :size="size" @click="$refs.crud.rowEdit(row, index)" v-if="
              permissionList.editBtn
            ">
              编辑
            </el-button>
            <el-button :type="type" :size="size" @click="rowDel(row)" v-if="
              permissionList.delBtn
            ">
              删除
            </el-button>
          </template>
        </avue-crud>
      </basic-container>
    </el-col>
    <Uview :dialogVisible="uviewVisible" :changeVisible="changeUviewVisible" ref="uview" />
  </el-row>
</template>

<script>
import Uview from "@/components/uview/main.vue";
import { mapGetters } from "vuex";
import { validateFile } from "@/views/components/util";
import { getDeptTree } from "@/api/infoRelease/partyLead"
// 审核状态
import * as funList from "@/api/infoRelease/toKnow";
export default {
  components: {
    Uview,
  },
  props: {},
  data () {
    return {
      uviewVisible: false,
      dialogVisible: false,
      limitCountImg: 1, //上传图片的最大数量
      srcList: [],
      tabLoading: true, //类别loading
      form: {},
      query: {},
      loading: true,
      box: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      type: "",
      // allTypeData: [],
      selectionList: [],
      // viewDialog: false, //详情弹窗
      option: {
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        selection: true,
        // menuWidth: 210,
        // menuAlign:"center",
        labelWidth: 120,
        dialogWidth: 1200,
        dialogClickModal: false,
        viewBtn: false,
        viewBtnText: "预览",
        delBtn: false, //默认是有删除、编辑按钮
        editBtn: false,
        dialogCustomClass: "directSupply-dialog",
        column: [
          {
            label: "事项名称",
            prop: "title",
            search: true,
            searchSpan: 8,
            searchFilterable: true,
            span: 24,
            maxlength: 100,
            showWordLimit: true,
            rules: [
              //表单规则
              {
                required: true,
                message: "请填写事项名称",
                whitespace: true,
                trigger: "blur",
              },
            ],
          },
          {
            label: "信息类别",
            prop: "type",
            editDisabled: true,
            width: 100,
            span: 24,
            search: true,
            searchSpan: 5,
            type: "select",
            dicUrl: `/api/blade-system/dict/dictionary?code=instructionType`,
            props: {
              //对应select的属性
              label: "dictValue",
              value: "dictKey",
            },
            filterable: true,
            rules: [
              {
                required: true,
                message: "请选择信息类别",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "发布人",
            prop: "releaseUserName",
            width: 100,
            addDisplay: false, //表单新增时是否可见
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "发布机构",
            prop: "deptName",
            width: 100,
            addDisplay: false, //表单新增时是否可见
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "实施主体",
            prop: "sub",
            hide: true, //隐藏列
            span: 24,
            maxlength: 100,
            showWordLimit: true,
            rules: [
              //表单规则
              {
                required: true,
                message: "请填写实施主体",
                whitespace: true,
                trigger: "blur",
              },
            ],
          },
          {
            label: "服务对象",
            prop: "client",
            hide: true, //隐藏列
            span: 24,
            maxlength: 100,
            showWordLimit: true,
            rules: [
              //表单规则
              {
                required: true,
                message: "请填写服务对象",
                whitespace: true,
                trigger: "blur",
              },
            ],
          },
          {
            label: "法定办结时限（工作日）",
            prop: "legalCompletion",
            hide: true, //隐藏列
            formslot: true, //开启表单自定义
            span: 24,
            maxlength: 9,
            showWordLimit: true,
            rules: [
              //表单规则
              {
                required: true,
                message: "请填写法定办结时限（工作日）",
                trigger: "blur",
              },
              {
                type: "number",
                min: 1,
                message: "填写的值必须为数字值,且最小值为1",
              },
            ],
          },
          {
            label: "承诺办结时限（工作日）",
            prop: "promiseCompletion",
            hide: true, //隐藏列
            formslot: true, //开启表单自定义
            span: 24,
            maxlength: 9,
            showWordLimit: true,
            rules: [
              //表单规则
              {
                required: true,
                message: "请填写承诺办结时限（工作日）",
                trigger: "blur",
              },
              {
                type: "number",
                min: 1,
                message: "填写的值必须为数字值,且最小值为1",
              },
            ],
          },
          {
            label: "咨询电话",
            prop: "consultPhone",
            hide: true, //隐藏列
            span: 24,
            maxlength: 20,
            minlength: 1,
            showWordLimit: true,
            rules: [
              //表单规则
              {
                required: true,
                message: "请填写正确的咨询电话",
                trigger: "blur",
              },
            ],
          },
          {
            label: "投诉电话",
            prop: "complaintPhone",
            hide: true, //隐藏列
            span: 24,
            maxlength: 20,
            minlength: 1,
            showWordLimit: true,
            rules: [
              //表单规则
              {
                required: true,
                message: "请填写正确的投诉电话",
                trigger: "blur",
              },
            ],
          },
          {
            label: "申报材料",
            prop: "fileList",
            dataType: "object",
            slot: true,
            type: "upload",
            // listType: "picture",
            limit: 9,
            // accept: ".jpeg,.jpg,.png",
            span: 24,
            propsHttp: {
              res: "data",
              url: "link",
              name: "originalName",
            },
            tip: "仅支持上传图片、视频、音频、office文件,最多只能上传9个文件，单个文件不超过10MB",
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            hide: true,
            showColumn: false, //不出现显隐面板中
          },
          // {
          //   label: "受理条件",
          //   prop: "requirement",
          //   formslot: true,
          //   hide: true, //隐藏列
          //   minRows: 4,
          //   span: 24,
          // },
          {
            type: "ueditor",
            label: "受理条件",
            prop: "requirement",
            component: "AvueUeditor",
            // options: {
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            customConfig: {
              excludeMenus: [
                "code",
                "todo",
                "fontName",
                "video",
                "table",
                "source",
                "fullScreen",
              ],
              uploadImgMaxLength: 1, //限制单次图片上传张数
            }, //wangEditor编辑的配置
            propsHttp: {
              res: "data",
              url: "link",
            },
            // },
            hide: true,
            span: 24,
            showColumn: false, //不出现显隐面板中
          },
          // {
          //   label: "办理流程",
          //   prop: "process",
          //   formslot: true,
          //   hide: true, //隐藏列
          //   addDisplay: true, //表单新增时是否可见
          //   editDisplay: true,
          //   viewDisplay: true,
          //   component: "AvueUeditor",
          //   minRows: 4,
          //   span: 24,
          // },
          {
            type: "ueditor",
            label: "办理流程",
            prop: "process",
            component: "AvueUeditor",
            // options: {
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            customConfig: {
              excludeMenus: [
                "code",
                "todo",
                "fontName",
                "video",
                "table",
                "source",
                "fullScreen",
              ],
              uploadImgMaxLength: 1, //限制单次图片上传张数
            }, //wangEditor编辑的配置
            propsHttp: {
              res: "data",
              url: "link",
            },
            // },
            hide: true,
            span: 24,
            showColumn: false, //不出现显隐面板中
          },
          {
            label: "办理地址",
            prop: "address",
            hide: true, //隐藏列
            // showColumn: false, //是否加入动态现隐列
            type: "textarea",
            span: 24,
            maxlength: 100,
            showWordLimit: true,
            minRows: 2,
          },
          {
            label: "办理时间",
            prop: "processingTime",
            hide: true, //隐藏列
            type: "textarea",
            span: 24,
            maxlength: 100,
            showWordLimit: true,
            minRows: 2,
          },
        ],
      },
      data: [],
      treeLoading: false,
      treeData: [],
      treeOption: {
        addBtn: false,
        menu: false,
        size: 'small',
        props: {
          labelText: '标题',
          label: 'title',
          value: 'value',
          children: 'children'
        }
      },
      deptId: ''
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),

    // 优化后的权限计算属性
    permissionList () {
      const prefix = "toKnow";
      return Object.entries({
        addBtn: "_add",
        viewBtn: "_view",
        editBtn: "_edit",
        delBtn: "_delete",
      }).reduce((acc, [key, suffix]) => {
        acc[key] = this.vaildData(this.permission[`${prefix}${suffix}`], false);
        return acc;
      }, {});
    },
    ids () {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created () {
    console.log(this.permission);
    this.treeLoading = true
    this.initTreeData(this.userInfo.tenant_id)
  },
  methods: {
    //Uview预览 start
    showUview () {
      this.uviewVisible = true;
      this.$refs.uview.sendMessage(this.form.title, this.form.process);
    },
    changeUviewVisible () {
      this.uviewVisible = false;
    },
    // 详情弹窗
    preview (row) {
      this.form = row;
      this.queryDetail();
    },
    // 新增
    rowSave (row, done, loading) {
      if (!this.form.attachList) {
        this.form.attachList = [];
      }
      let submitData = {
        title: row.title,
        type: row.type,
        sub: row.sub,
        client: row.client,
        legalCompletion: row.legalCompletion,
        promiseCompletion: row.promiseCompletion,
        consultPhone: row.consultPhone,
        complaintPhone: row.complaintPhone,
        requirement: row.requirement,
        address: row.address,
        process: row.process,
        processingTime: row.processingTime,
        attachIds: row.attachList,
      };
      funList.save(submitData).then(
        async () => {
          this.$message.success(`成功新增`);
          await this.onLoad(this.page, this.query);
          done();
        },
        () => {
          loading();
        }
      );
    },
    // 修改
    rowUpdate (row, index, done, loading) {
      if (!this.form.attachList) {
        this.form.attachList = [];
      }
      let submitData = {
        id: row.id,
        title: row.title,
        type: row.type,
        sub: row.sub,
        client: row.client,
        legalCompletion: row.legalCompletion,
        promiseCompletion: row.promiseCompletion,
        consultPhone: row.consultPhone,
        complaintPhone: row.complaintPhone,
        requirement: row.requirement,
        address: row.address,
        process: row.process,
        processingTime: row.processingTime,
        attachIds: row.attachList,
      };
      funList.update(submitData).then(
        async () => {
          this.$message.success(`成功修改`);
          await this.onLoad(this.page, this.query);
          done();
        },
        () => {
          loading();
        }
      );
    },
    // 删除
    rowDel (row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funList.remove({
            ids: row.id,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // 重置
    searchReset () {
      this.query = {};
      this.onLoad(this.page);
    },
    // 搜索
    searchChange (params, done) {
      this.page.currentPage = 1;
      this.query = params;
      this.onLoad(this.page, params);
      done();
    },
    // 勾选
    selectionChange (list) {
      this.selectionList = list;
    },
    // 重置勾选
    selectionClear () {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    // 多选删除
    handleDelete () {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.selectionList.findIndex((item) => item.status === 1) !== -1) {
        this.$message.warning("存在已发布的数据,已发布的数据无法删除");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funList.remove({
            ids: this.ids,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    // 查看预览
    queryDetail () {
      funList
        .detail({
          id: this.form.id,
        })
        .then(() => {
          this.showUview();
        });
    },
    // 打开前回调
    async beforeOpen (done, type) {
      if (type === "add") {
        this.form = {};
        done();
        return;
      }
      if (["edit", "view"].includes(type)) {
        funList
          .detail({
            id: this.form.id,
          })
          .then((res) => {
            this.form = res.data.data;
            this.form.fileList = [];
            this.form.themePictureList = [];
            if (res.data.data.attachList) {
              let tempAttachList = [];
              if (res.data.data.themePicture) {
                this.form.themePictureList = [
                  {
                    label: res.data.data.themePicture.originalName,
                    value: res.data.data.themePicture.link,
                  },
                ];
                this.form.themePicture = res.data.data.themePicture.id;
              }
              res.data.data.attachList.map((value) => {
                this.form.fileList.push({
                  label: value.originalName,
                  value: value.link,
                });
                tempAttachList.push(value.id);
              });
              this.form.attachList = tempAttachList;
            }
            done();
          });
      }
    },
    // 上传前回调
    uploadBefore (file, done, loading, column) {
      console.log({ file, done, loading, column });
      //文件个数可以在limit属性设置,超过则不会继续上传,也不会走这个函数,这个组件目前只能一次一个个传
      if (column.prop == "themePictureList") {
        if (validateFile.call(this, "image", 10, file)) {
          done();
        } else {
          loading();
        }
      } else {
        if (validateFile.call(this, "", 10, file)) {
          done();
        } else {
          loading();
        }
      }
    },
    // 上传后执行操作
    uploadAfter (res, done, loading, column) {
      //fileList是form展示的[{label,value}],attachList是记录每次上传完的id
      if (!res || !res.attachId) {
        this.$message.error("上传失败");
        loading();
      } else {
        this.$message.success("上传成功");
        if (column.prop == "themePictureList") {
          this.form.themePicture = res.attachId;
          done();
        } else {
          if (!this.form.attachList) {
            this.form.attachList = [];
            this.form.attachList.push(res.attachId);
          } else {
            this.form.attachList.push(res.attachId);
          }
          done();
        }
      }
    },
    // 删除已上传文件
    uploadDelete (file, column) {
      console.log({ file, column });

      return this.$confirm("是否确定移除该项？").then(() => {
        if (column.prop == "themePictureList") {
          this.form.themePicture = "";
          this.form.themePictureList.splice(file.uid, 1);
        } else {
          this.form.fileList.splice(file.uid, 1);
          this.form.attachList.splice(file.uid, 1);
        }
      });
    },
    // 上传限制
    uploadExceed (limit) {
      this.$message.error(`最多只能上传${limit}个文件`);
    },
    // 当前页切换
    currentChange (currentPage) {
      this.page.currentPage = currentPage;
    },
    // 页面显示条数切换
    sizeChange (pageSize) {
      this.page.pageSize = pageSize;
    },
    // 刷新
    refreshChange () {
      this.onLoad(this.page, this.query);
    },
    // 首次加载
    async onLoad (page, params = {}) {
      this.loading = true;
      let query = {
        ...params,
        current: this.page.currentPage,
        size: this.page.pageSize,
        module: this.moduleName,
      };
      if (query.updateTime) {
        if (Array.isArray(query.updateTime)) {
          query.begin = query.updateTime[0];
          query.end = query.updateTime[1];
        }
        delete query.updateTime;
      }
      query.deptId = this.deptId; // 默认企业id空值
      let res = await funList.getList(query);
      if (res && res.data && res.data.success) {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        if (this.page.currentPage > 1 && this.page.currentPage > data.pages) {
          this.page.currentPage = data.pages
          this.onLoad()
        }
        this.selectionClear();
      }
    },
    nodeClick (data) {
      this.deptId = data.id
      this.page.currentPage = 1
      this.onLoad(this.page, this.query)
    },
    async initTreeData (tenantId) {
      this.treeData = (await getDeptTree(tenantId)).data.data
      this.treeLoading = false
    },
  },
};
</script>
<style scoped lang="scss">
.img-tiny {
  height: 100px;
  width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(245, 247, 250);
}

.btn-group-container {
  display: flex;
}
</style>

<!--
 * @Description: 机构管理
 * @Author: 黄海斌
 * @Date: 2021-08-12 17:30:57
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-31 14:38:28
-->
<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" ref="crud" v-model="form" :permission="permissionList" :before-open="beforeOpen" :before-close="beforeClose" @row-del="rowDel" @row-update="rowUpdate" @row-save="rowSave" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad" @tree-load="treeLoad">
      <template slot="menuLeft">
        <!-- <el-button
          type="danger"
          size="small"
          icon="el-icon-delete"
          v-if="permission.dept_delete"
          plain
          @click="handleDelete"
          >删 除
        </el-button> -->
        <el-button
          type="success"
          size="small"
          plain
          icon="el-icon-upload2"
          v-if="permission.dept_upload"
          @click="handleImport"
          >导入
        </el-button>
        <el-button
          type="warning"
          size="small"
          plain
          :icon="icon"
          v-if="permission.dept_export"
          @click="handleExport"
          :disabled="disableButton"
          >导出
        </el-button>
      </template>
      <template slot-scope="scope" slot="menu">
        <!-- 编辑按钮 -->
        <el-button size="small" type="text" icon="el-icon-edit" v-if="permission.dept_edit && scope.row.deptCategory == 6" @click="$refs.crud.rowEdit(scope.row)">编辑
        </el-button>
        <el-button size="small" type="text" icon="el-icon-delete" v-if="permission.dept_delete && scope.row.deptCategory == 6" @click="$refs.crud.rowDel(scope.row)">删除
        </el-button>
        <el-button type="text" icon="el-icon-circle-plus-outline" size="small" @click.stop="handleAdd(scope.row, scope.index)" v-if="permission.dept_addChild && (scope.row.deptCategory == 4 || scope.row.deptCategory == 5)">新增部门
        </el-button>
      </template>
      <template slot-scope="{ row }" slot="isOpen">
        <div v-if="row.deptCategory!==6">
          <el-tag v-if="row.isOpen">
          已开通
        </el-tag>
        <el-tag v-else type="danger">
         未开通
        </el-tag>
        </div>
        
      </template>
      <template slot="isOpenForm">
        <div v-if="form.deptCategory!==6">
        <el-tag v-if="form.isOpen">
          已开通
        </el-tag>
        <el-tag v-else type="danger">
         未开通
        </el-tag>
        </div>
      </template>
    </avue-crud>
    <el-dialog title="数据导入" append-to-body :visible.sync="excelBox" width="555px">
      <avue-form :option="excelOption" v-if="excelBox" v-model="excelForm" :upload-before="uploadBefore" :upload-error="uploadError" :upload-after="uploadAfter">
        <template slot="excelTemplate">
          <el-button type="primary" @click="handleTemplate">
            点击下载<i class="el-icon-download el-icon--right"></i>
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getLazyList,
  remove,
  update,
  add,
  getDept,
} from "@/api/system/dept";
// import { getLazyTreeList } from "@/api/base/region";
import { mapGetters } from "vuex";
import { getToken } from '@/util/auth'
import { handleDownload,handleDownloadFile } from '@/util/download';
export default {
  data() {
    let validateBlank = (rule, value, callback) => {
      if (value.length !== 0 && value.match(/^[ ]*$/)) {
        callback(new Error('输入的内容不能全为空格'))
      }
      callback()
    }
    return {
      icon: "el-icon-download el-icon--right",
      disableButton: false,
      form: {},
      selectionList: [],
      query: {},
      loading: true,
      parentId: '',
      grantParentId: '',
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        height: 'auto',
        calcHeight: 30,
        lazy: true,
        tip: false,
        simplePage: true,
        searchShow: true,
        searchMenuSpan: 6,
        tree: true,
        border: true,
        index: false,
        selection: true,
        menuWidth: 300,
        labelWidth: 120,
        dialogClickModal: false,
        addBtn: false,
        viewBtn: true,
        delBtn: false,
        editBtn: false,
        column: [
          // {
          //   label: "行政区划",
          //   prop: "regionCode",
          //   dicData: [],
          //   type: "select",
          //   dataType: "string",
          //   props: {
          //     value: "id",
          //     label: "name"
          //   },
          //   rules: [{
          //     required: true,
          //     message: "请选择行政区划",
          //     trigger: "change"
          //   }],
          //   hide: true,
          //   span: 12,
          //   change:(value)=>{
          //     console.log(value,'value')
          //     if(!value.item) return
          //     this.form.deptName = value.item.name
          //     this.form.fullName = value.item.name
          //   }
          // },

          {
            label: "机构名称",
            prop: "deptName",
            search: true,
            span: 12,
            maxlength: 45,
            overHidden: true,
            rules: [{
              required: true,
              message: "请输入机构名称",
              trigger: "change"
            },
            {
              min: 2,
              max: 45,
              message: '机构名称由2-45个字符组成',
              trigger: 'change'
            },
            {
              validator: validateBlank,
              trigger: 'change'
            }]
          },
          {
            label: "机构全称",
            prop: "fullName",
            search: true,
            maxlength: 45,
            span: 12,
            overHidden: true,
            rules: [{
              required: true,
              message: "请输入机构全称",
              trigger: "change"
            },
            {
              min: 2,
              max: 45,
              message: '机构全称由2-45个字符组成',
              trigger: 'change'
            },
            {
              validator: validateBlank,
              trigger: 'change'
            }]
          },
          {
            label: "上级机构",
            prop: "parentId",
            dicData: [],
            type: "tree",
            hide: true,
            disabled: true,
            addDisplay: false,
            viewDisplay: false,
            editDisplay: false,
            // props: {
            //   label: "title"
            // },
            // rules: [{
            //   required: true,
            //   message: "请选择上级机构",
            //   trigger: "click"
            // }]
          },
          {
            label: "上级机构",
            prop: "parentName",
            hide: true,
            disabled: true,
          },

          {
            label: "机构类型",
            type: "select",
            value: "6",
            disabled: true,
            dicUrl: "/api/blade-system/dict/dictionary?code=org_category",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            dataType: "number",
            prop: "deptCategory",
            slot: true,
            rules: [{
              required: true,
              message: "请输入机构类型",
              trigger: "change"
            }]
          },

          {
            label: "行政区划码",
            prop: "regionCode",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "是否开通",
            prop: "isOpen",
            // display: false,
            formslot:true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            type: "string",
          },
          {
            label: "开通时间",
            prop: "openTime",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            hide: true,
            // type: "string",
          },
          {
            label: "排序",
            prop: "sort",
            type: "number",
            width: 80,
            rules: [{
              required: true,
              message: "请输入排序",
              trigger: "blur"
            },
            {
              pattern: /^([1-9][0-9]{0,4})$/,
              trigger: 'blur',
              message: '请输入1-99999之间的整数'
            }]
          },

          {
            label: "备注",
            prop: "remark",
            span: 24,
            maxlength: 500,
            overHidden: true,
            type: "textarea",
            rules: [{
              required: false,
              message: "请输入备注",
              trigger: "blur"
            },
            {
              max: 500,
              message: '备注内容不能超过500字符',
              trigger: 'blur'
            },
            {
              validator: validateBlank,
              trigger: 'blur'
            }],
            hide: true
          }
        ]
      },
      data: [],
      excelBox: false,
      excelForm: {},
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: '机构上传',
            prop: 'excelFile',
            type: 'upload',
            accept: ['.xls', '.xlsx'],
            drag: true,
            loadText: '机构上传中，请稍等',
            span: 24,
            propsHttp: {
              res: 'data'
            },
            tip: '请上传 .xls,.xlsx 标准格式文件（大小不超过10M）',
            action: `/api/blade-system/dept/admin/import`,
          },
          {
            label: '模板下载',
            prop: 'excelTemplate',
            formslot: true,
            span: 24,
          }
        ]
      },
      maps: new Map()
    };
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.dept_add, false),
        viewBtn: this.vaildData(this.permission.dept_view, false),
        delBtn: this.vaildData(this.permission.dept_delete, false),
        editBtn: this.vaildData(this.permission.dept_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    // initData () {
    // getDeptTree().then(res => {
    //   const column = this.findObject(this.option.column, "parentId");
    //   column.dicData = res.data.data;
    // });
    // },
    async handleAdd(row) {
      console.log(row);
      if(!row.isOpen){
        this.$message.warning("请先开通当前租户")
        return
      }
      this.parentId = row.id;
      if(!row.hasChildren){
        this.grantParentId = row.parentId;
      }else{
        this.grantParentId = "";
      }
      // row.regionCode = row.regionCode ? row.regionCode : 35;
      // const res = await getLazyTreeList(row.regionCode);
      const column = this.findObject(this.option.column, "parentId");
      // const column2 = this.findObject(this.option.column, "regionCode");
      // column2.dicData = res.data.data;
      column.value = row.id;
      this.$refs.crud.rowAdd();
      this.form.parentName = row.deptName;
    },
    rowSave(row, done, loading) {
      console.log(row,'row')
      let params = {
        fullName: row.fullName,
        // regionCode: row.regionCode,
        parentId: this.parentId,
        deptName: row.deptName,
        sort: row.sort,
        deptCategory: row.deptCategory,
        remark: row.remark
      }
      add(params).then((res) => {
        // 获取新增数据的相关字段
        console.log(res,'res')
        const data = res.data.data;
        row.id = data.id;
        row.deptCategoryName = data.deptCategoryName;
        row.tenantId = data.tenantId;
        this.$message.success("操作成功!");
        // this.onLoad(this.page);
        // 数据回调进行刷新
        let parentId = this.parentId;
        if(this.grantParentId) parentId = this.grantParentId
        let treeData = this.maps.get(parentId)
        // console.log(treeData,'treeData')
        if (treeData) this.treeLoad(treeData.tree, treeData.treeNode, treeData.resolve)
        else{
          this.parentId = "0"
          this.onLoad(this.page);
        }
        done();
      }).catch(err => {
        console.log(err,'err')
        loading()
      })
    },
    rowUpdate(row, index, done, loading) {
      let deptDetail = {
        // "ancestors": "",
        // "createTime": "",
        "deptCategory": 0,
        "deptName": "",
        "fullName": "",
        "id": 0,
        // "integral": 0,
        // "isDeleted": 0,
        "parentId": 0,
        "remark": "",
        "sort": 0,
        // "tenantId": "",
        // "regionCode": "",
      };
      Object.keys(deptDetail).forEach(key => {
        deptDetail[key] = row[key];
      });
      console.log(deptDetail);
      update(deptDetail).then(() => {
        this.$message.success("操作成功!");
        // 数据回调进行刷新
        // this.onLoad(this.page);
        let treeData = this.maps.get(row.parentId)
        // console.log(treeData,'treeData')
        if (treeData) this.treeLoad(treeData.tree, treeData.treeNode, treeData.resolve)
        // else{
        //   this.parentId = "0"
        //   this.onLoad(this.page);
        // }
        done();
      }).catch(err => {
        console.log(err,'err')
        loading()
      })
    },
    rowDel(row, index, done) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          // 数据回调进行刷新
          let treeData = this.maps.get(row.parentId)
          // console.log(treeData,'treeData')
          if (treeData) this.treeLoad(treeData.tree, treeData.treeNode, treeData.resolve)
          this.$message.success("操作成功!");
          done(row);
        });
    },
    // handleDelete() {
    //   if (this.selectionList.length === 0) {
    //     this.$message.warning("请选择至少一条数据");
    //     return;
    //   }
    //   this.$confirm("确定将选择数据删除?", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning"
    //   })
    //     .then(() => {
    //       return remove(this.ids);
    //     })
    //     .then(() => {
    //       // 刷新表格数据并重载
    //       this.data = [];
    //       this.$refs.crud.refreshTable();
    //       this.$refs.crud.toggleSelection();
    //       // 表格数据重载
    //       this.onLoad(this.page);
    //       this.$message.success("操作成功!");
    //     });
    // },
    uploadAfter(res, done) {
      if (!res) {
        this.$message.success("导入成功")
      } else {
        let res1 = res.toString().replaceAll('<br/>', "\n\r").replace("Error: ", "");
        this.$confirm(<div style="white-space:pre-wrap;word-break:break-all">{res1}</div>, "错误提示", {
          showConfirmButton: false,
          cancelButtonText: "关闭",
          type: "error"
        }).then(() => {
        }).catch(() => { });
      }
      this.excelForm = {};
      // 刷新表格数据并重载
      this.excelBox = false;
      this.$refs.crud.refreshTable();
      // 表格数据重载
      this.refreshChange();
      done();
    },
    uploadBefore(file, done) {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel';
      const isLt10M = file.size / 1024 / 1024 <= 10;
      if (!isExcel) {
        this.$message.error('只能上传Excel文件!');
        return;
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过10M!');
        return;
      }
      done();
    },
    uploadError(error) {
      // if (error) this.$message.error("上传失败!" + error);
    },
    handleImport() {
      const column = this.findObject(this.excelOption.column, "excelFile");
      column.action = `/api/blade-system/dept/admin/import`;
      this.excelBox = true;
    },
    handleTemplate() {
      const url =`/api/blade-system/dept/admin/export-template?${this.website.tokenHeader}=${getToken()}`
      handleDownloadFile(url);
    },
    handleExport() {
      this.$confirm("是否导出机构数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(async () => {
        let deptIdList = this.selectionList.map(item => { return item.id });
        // window.open(`/api/blade-system/dept/admin/export?${this.website.tokenHeader}=${getToken()}&deptName=${this.query.deptName || ''}&tenantId=${this.query.tenantId || ''}&fullName=${this.fullName || ''}&deptIdList=${deptIdList || []}`);
        this.icon = "el-icon-loading el-icon--right";
        this.disableButton = true;
        const url = `/api/blade-system/dept/admin/export?${this.website.tokenHeader}=${getToken()}&deptName=${this.query.deptName || ''}&fullName=${this.fullName || ''}&deptIdList=${deptIdList || []}`;
        const result = await handleDownload(url);
        if (result != null) {
          this.icon = "el-icon-download el-icon--right";
          this.disableButton = false;
        }
      }).catch(() => { });
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.parentId = '';
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    async beforeOpen(done, type) {
      if (["add", "edit"].includes(type)) {
        // this.initData();
      }
      if (["edit", "view"].includes(type)) {
        getDept(this.form.id).then(res => {
          this.form = res.data.data;
          // this.form.parentRegionCode = this.form.parentRegionCode ? this.form.parentRegionCode : 35
          // console.log(this.form);
          // getLazyTreeList(this.form.parentRegionCode).then(res => {
          //   const column = this.findObject(this.option.column, "regionCode");
          //   column.dicData = res.data.data;
          // });

        });
      }
      done();
    },
    beforeClose(done) {
      this.parentId = '';
      const column = this.findObject(this.option.column, "parentId");
      column.value = "";
      column.addDisabled = false;
      done();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    async onLoad(page, params = {}) {
      this.loading = true;
      const result = await new Promise((resolve) => {
        getLazyList(this.parentId, Object.assign(params, this.query)).then(res => {
          resolve(res.data.data);
        });
      });
      this.$nextTick(() => {
        this.loading = false;
        this.data = result;
        this.$refs.crud.refreshTable();
        this.selectionClear();
      })
    },
    async treeLoad(tree, treeNode, resolve) {
      const parentId = tree.id;
      this.maps.set(parentId, { tree, treeNode, resolve });
      const result = await new Promise((resolve) => {
        getLazyList(parentId).then(res => {
          resolve(res.data.data);
        });
      });
      // this.$nextTick(() => {
      //   this.$refs.crud.refreshTable();
      // })
      resolve(result)
    }
  }
};
</script>

<style>
</style>

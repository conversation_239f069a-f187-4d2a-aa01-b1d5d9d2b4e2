<!-- 
/**
  * @Author: 林中奇
  * @Date: 2025/08/04
  * @lastAuthor:
  * @lastChangeDate:
  * @Explain: 党组织管理- 荣誉信息
  */
 
-->

<template>
  <div>
    <avue-crud ref="crud" v-model="form" :option="option" :table-loading="loading" :data="data" :page.sync="page"
      :before-open="beforeOpen" @row-update="rowUpdate" @row-save="rowSave" @row-del="rowDel"
      @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
      @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <template slot="menuLeft">
        <el-button v-if="permission.census_delete" type="danger" size="small" icon="el-icon-delete" plain
          @click="handleDelete">批量删除 </el-button>
      </template>
      <template slot="menu" slot-scope="scope">
        <el-button type="text" size="mini" icon="el-icon-delete" @click="$refs.crud.rowDel(scope.row)">删
          除</el-button>
      </template>
    </avue-crud>
  </div>
</template>

<script>
import { getList, getDetail, add, update, remove } from '@/api/governance/census'
import { mapGetters } from 'vuex'
import { isMobile } from '@/util/validate'

export default {
  components: {},
  props: {
    dept: {
      default: '',
      type: String
    }
  },
  data () {
    var checkMobile = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入联系方式'))
      } else if (value.length > 0 && !isMobile(value)) {
        callback(new Error('手机号码格式错误'))
      } else {
        callback()
      }
    }
    const checkName = (rule, value, callback) => {
      if (value !== '' && value.length > 1 && value.length < 21) {
        if (!value.trim()) {
          callback(new Error('户主姓名不能为纯空格'))
        }
      } else {
        callback(new Error('户主姓名长度在2到20个字符'))
      }
      callback()
    };
    return {
      icon: "el-icon-download el-icon--right", // del
      disableButton: false,
      dialogTitle: '成员',
      memberBox: false,
      familyId: '',
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        border: true,
        index: true,
        labelPosition: 'right',
        viewBtn: true,
        delBtn: false,
        selection: true,
        menuWidth: 200,
        searchMenuSpan: 4,
        labelWidth: 120,
        dialogWidth: 600,
        column: [
          {
            label: '荣誉名称',
            prop: 'householderName',
            type: 'input',
            maxlength: 20,
            showWordLimit: true,
            span: 24,
            search: true,
            searchSpan: 6,
            rules: [
              {
                required: true,
                validator: checkName,
                trigger: "blur",
              }
            ],
          }, {
            label: '获奖时间',
            prop: 'birthDate',
            type: 'date',
            rules: [{ required: true, message: '请选择获奖时间', trigger: 'change' }],
            span: 24,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            search: true,
          }, {
            label: '荣誉描述',
            prop: 'householderName',
            type: 'textarea',
            maxlength: 500,
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入荣誉描述",
                trigger: "blur",
              }
            ],
          }, {
            label: '荣誉图片',
            prop: 'imgUrl',
            type: 'upload',
            accept: '.png, .jpg, .jpeg',
            span: 24,
            loadText: '图片上传中，请稍等',
            tip: '只能上传png/jpg/jpeg文件，且不超过50MB',
            propsHttp: {
              res: 'data',
              name: 'originalName',
              url: 'attachId'
            },
            action: '/api/blade-resource/oss/endpoint/put-file-attach',
            required: true,
          },

        ]
      },
      data: [],
      deptId: ''
    }
  },
  computed: {
    ...mapGetters(['permission', 'userInfo']),
    ids () {
      let ids = []
      this.selectionList.forEach((ele) => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  watch: {
    dept: function (val) {
      this.deptId = val
      this.page.currentPage = 1
      this.onLoad(this.page)
    },
  },
  created () {
  },
  methods: {
    rowSave (row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          done()
        },
        (error) => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate (row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          done()
        },
        (error) => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowDel (row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        })
    },
    handleDelete () {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.$refs.crud.toggleSelection()
        })
    },
    async beforeOpen (done, type) {
      if (['edit', 'view'].includes(type)) {
        await new Promise((resolve) => {
          getDetail(this.form.id).then((res) => {
            const data = res.data.data || {}
            this.form = data
            resolve()
          })
        })
      }
      done()
    },
    searchReset () {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange (params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange (list) {
      this.selectionList = list
    },
    selectionClear () {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange (currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange (pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange () {
      this.onLoad(this.page, this.query)
    },
    onLoad (page, params = {}) {
      this.loading = true
      params.deptId = this.deptId
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then((res) => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .census-user-option {
  display: flex;
  align-items: center;
  padding: 5px 10px;

  .content {
    margin-left: 10px;
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>

<style lang="scss">
.census-dialog .el-textarea .el-input__count {
  height: 25px;
  line-height: 25px;
  bottom: -25px !important;
}

.census-dialog .el-input .el-input__count {
  margin-top: 25px;
}
</style>

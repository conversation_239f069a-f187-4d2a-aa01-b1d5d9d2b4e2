/*
 * @Date: 2025-06-24 09:38:27
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-21 08:58:05
 * @Description:
 * @FilePath: \src\api\system\tenant.js
 */
import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-system/tenant/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/blade-system/tenant/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-system/tenant/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-system/tenant/submit',
    method: 'post',
    data: row
  })
}

export const setting = (ids, form) => {
  return request({
    url: '/api/blade-system/tenant/setting',
    method: 'post',
    params: {
      ...form,
      ids
    }
  })
}

export const datasource = (tenantId, datasourceId) => {
  return request({
    url: '/api/blade-system/tenant/datasource',
    method: 'post',
    params: {
      tenantId,
      datasourceId
    }
  })
}

export const info = (domain) => {
  return request({
    url: '/api/blade-system/tenant/info',
    method: 'get',
    params: {
      domain
    }
  })
}

export const packageInfo = (tenantId) => {
  return request({
    url: '/api/blade-system/tenant/package-detail',
    method: 'get',
    params: {
      tenantId
    }
  })
}

export const packageSetting = (tenantId, packageId) => {
  return request({
    url: '/api/blade-system/tenant/package-setting',
    method: 'post',
    params: {
      tenantId,
      packageId
    }
  })
}

export const recycle = ids => {
  return request({
    url: '/api/blade-system/tenant/recycle',
    method: 'post',
    params: {
      ids,
    },
  });
};

export const pass = ids => {
  return request({
    url: '/api/blade-system/tenant/pass',
    method: 'post',
    params: {
      ids,
    },
  });
};

export const remove = ids => {
  return request({
    url: '/api/blade-system/tenant/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};


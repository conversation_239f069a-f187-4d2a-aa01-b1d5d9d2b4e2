<!--
 * @Date: 2025-06-26 09:35:16
 * @LastEditors: linqh21
 * @LastEditTime: 2025-08-04 09:33:13
 * @Description:
 * @FilePath: \src\views\systemSettings\appConfig.vue
-->
<template>
  <el-row :gutter="10">
    <el-col :xs="24" :sm="6" :md="4" :lg="4" :xl="5">
      <div class="box">
        <el-scrollbar>
          <basic-container>
            <avue-tree ref="tree" :option="treeOption" :data="treeData" @node-click="nodeClick" node-key="id" :current-node-key="treeGridId" />
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :xs="24" :sm="9" :md="8" :lg="8" :xl="7">
      <basic-container>
        <PhoneMockup
          :app-list="appList"
          :content-list="contentList"
          :show-content-list-box="showContentListBox"
          @app-list-click="onAppListClick"
          @empty-card-click="onEmptyCardClick"
          @remove-content-block="removeContentBlock"
          @update-content-list="updateContentList"
        />
      </basic-container>
    </el-col>
    <el-col :xs="24" :sm="9" :md="12" :lg="12" :xl="12">

      <basic-container>
        <div class="appListBox" v-show="!showContentListBox">
          <div class="app-title-row">
            <div class="app-title">
              <span class="dept-label">{{ currentDeptName || '' }}</span>
              <span>应用配置</span>
            </div>
            <el-button class="app-save-btn" type="primary" size="mini" :disabled="appList.length == 0" @click="setAppConfigList">保存配置</el-button>
          </div>
          <div class="app-title-divider"></div>
          <div class="app-grid">
            <!-- <template v-for="(app) in appList">
              <div v-if="app.disabled" :key="app.id" class="app-item">
                <span class="app-disabled-tag">上级应用</span>
                <img :src="app.menuSourceLink" alt="app icon" class="app-icon" />
                <div class="app-name-tag">{{ app.menuName }}</div>
              </div>
            </template> -->

            <draggable v-model="appList" :animation="200" class="app-draggable" :ghost-class="'app-dragging'" :chosen-class="'app-chosen'">
              <template v-for="(app,idx) in appList">
                <div v-if="!app.disabled" :key="app.menuId" class="app-item">
                  <span class="app-remove" @click.stop="removeApp(idx)">×</span>
                  <img :src="app.menuSourceLink" alt="app icon" class="app-icon" />
                  <div class="app-name-tag">{{ app.menuName }}</div>
                </div>
                <div v-else :key="app.menuId" class="app-item">
                  <span class="app-disabled-tag">镇村联动</span>
                  <img :src="app.menuSourceLink" alt="app icon" class="app-icon" />
                  <div class="app-name-tag">{{ app.menuName }}</div>
                </div>
              </template>
              <!-- <div v-for="(app, idx) in appList" :key="app.id" class="app-item">
                <span v-if="!app.disabled" class="app-remove" @click.stop="removeApp(idx)">×</span>
                <span v-else class="app-disabled-tag">上级应用</span>
                <img :src="app.menuSourceLink" alt="app icon" class="app-icon" />
                <div class="app-name-tag">{{ app.menuName }}</div>
              </div> -->
            </draggable>
            <div class="app-item app-add" @click="openAddDialog">
              <div class="add-icon">+</div>
              <div class="app-name">新增</div>
            </div>
          </div>
        </div>
        <div class="contentListBox" v-show="showContentListBox">
          <div class="content-title-row">
            <div class="content-title">
              <span class="dept-label">{{ currentDeptName || '' }}</span>
              <span>内容配置</span>
            </div>
            <el-button class="content-save-btn" type="primary" size="mini" @click="saveContentConfig">保存配置</el-button>
          </div>
          <div class="content-title-divider"></div>
          <div class="content-blocks-container">
            <div v-for="(block, idx) in contentBlocks" :key="block.id" class="color-block" :class="block.id" @mouseenter="block.hover = true" @mouseleave="block.hover = false" :style="getBlockStyle(block.id)">
              <template v-if="block.id === 'ConvenientService'">
                <ConvenientService />
              </template>
              <template v-else-if="block.id === 'ThreeAffairsDisclosure'">
                <ThreeAffairsDisclosure />
              </template>
              <template v-else-if="block.id === 'RuralDynamics'">
                <RuralDynamics />
              </template>
              <template v-else-if="block.id === 'Monitoring'">
                <Monitoring />
              </template>
              <template v-else-if="block.img">
                <img :src="block.img" alt="" style="width:100%;height:100%;object-fit:cover;" />
              </template>
              <template v-else>
                {{ idx+1 }}
              </template>
              <el-button v-if="block.hover" class="select-btn" size="mini" type="primary" @click.stop="addBlockToContentList(block)">
                选择
              </el-button>
            </div>
          </div>
        </div>
      </basic-container>
      <!-- 新增弹窗 -->
      <el-dialog title="选择应用" :visible.sync="dialogVisible" width="420px" :close-on-click-modal="false" append-to-body @close="closeAddDialog">
        <div class="tree-dialog-content">
          <el-tree v-if="dialogVisible" ref="addTree" :data="menuMiniIdTree" node-key="id" :props="{ label: 'title', children: 'children' }" show-checkbox highlight-current :default-expand-all="true" :expand-on-click-node="false" :default-checked-keys="defaultCheckedKeys" @check-change="onTreeCheckChange" class="tree-select">
            <span slot-scope="{ node, data }">
              <span>{{ node.label +`${data.isLinkage ? ' （镇村联动）' : ''}`}}</span>
            </span>
          </el-tree>
        </div>
        <div class="tree-dialog-footer">
          <el-button size="mini" @click="closeAddDialog">取消</el-button>
          <el-button size="mini" type="primary" :disabled="!hasCheckedNodes" @click="confirmAddApp">确定</el-button>
        </div>
      </el-dialog>
    </el-col>
  </el-row>
</template>

<script>
import { getDeptTree } from "@/api/system/dept";
import draggable from 'vuedraggable';
import { getAppTree, getAppConfig, setAppConfig,getContentConfig, setContentConfig } from "@/api/system/menu";
import { mapGetters } from "vuex";
// 导入子组件
import ConvenientService from './appConfigComponents/ConvenientService.vue';
import ThreeAffairsDisclosure from './appConfigComponents/ThreeAffairsDisclosure.vue';
import RuralDynamics from './appConfigComponents/RuralDynamics.vue';
import Monitoring from './appConfigComponents/Monitoring.vue';
import PhoneMockup from './appConfigComponents/PhoneMockup.vue';
// 工具函数：合并并去重应用列表，优先保留disabled=true的项
function mergeAppList(townList, villageList, linkageIds) {
  const villageMap = new Map(villageList.map(item => [item.menuId, item]));

  townList
    .filter(item => linkageIds.includes(item.menuId))
    .forEach(item => {
      villageMap.set(item.menuId, {
        ...(villageMap.get(item.menuId) || item),
        disabled: true
      });
    });
  console.log(villageMap);

  return Array.from(villageMap.values());
}

export default {
  components: {
    draggable,
    ConvenientService,
    ThreeAffairsDisclosure,
    RuralDynamics,
    Monitoring,
    PhoneMockup
  },
  data() {
    return {
      treeGridId: "",
      treeData: [],
      treeOption: {
        nodeKey: "id",
        defaultExpandAll: false,
        addBtn: false,
        menu: false,
        size: "small",
        props: {
          label: "title",
          value: "id",
          children: "children",
        },
      },
      appList: [],
      menuMiniIdTree: [],
      dialogVisible: false,
      checkedCount: 0,
      currentDeptName: '',
      isTown: false,
      defaultCheckedKeys: [],
      disabledKeys: [],
      contentList: [],
      showContentListBox: false,
      contentBlocks: [
        { id: 'ConvenientService', img: '', hover: false },
        { id: 'ThreeAffairsDisclosure', img: '', hover: false },
        { id: 'RuralDynamics', img: '', hover: false },
        { id: 'Monitoring', img: '', hover: false }
      ],
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    hasCheckedNodes() {
      return this.checkedCount > 0;
    }
  },
  mounted() {
    // console.log('userInfo:', this.userInfo.tenant_id);

    this.initData();
  },

  methods: {
    // 初始化部门树和菜单树数据
    initData() {
      Promise.all([getDeptTree(true), getAppTree()]).then(([deptRes, menuRes]) => {
        this.treeData = deptRes.data.data;
        this.menuMiniIdTree = menuRes.data.data;
      });
    },
    /**
     * 获取当前部门的应用配置列表
     * - 镇级只显示 townAppList
     * - 村级合并镇级（disabled）和村级应用，去重
     */
    getAppConfigList() {
      getAppConfig(this.treeGridId).then(res => {
        if (this.isTown) {
          this.appList = res.data.data.townAppList;
        } else {
          this.appList = mergeAppList([], res.data.data.villageAppList, res.data.data.linkageIds ? res.data.data.linkageIds : []);
          // console.log(this.appList)
        }
      });
      getContentConfig(this.treeGridId).then(res => {
        // console.log(res);

        // let temp = {},
        let temp = []
        for(let i = 0; i < res.data.data.length; i++){
          temp.push({
            id: res.data.data[i].code,
            img: '',
            hover: false
          })
        }
        this.contentList = temp;
      });
    },
    // 处理树节点点击，切换部门
    nodeClick(data) {
      console.log(data);
      //切换时候要删除menuMiniIdTree树中节点的disabled属性
      this.menuMiniIdTree.forEach(item => {
        item.disabled = false;
      })
      this.appList = []
      this.treeGridId = data.id;
      this.isTown = data.hasChildren;
      this.currentDeptName = data.title || data.label || '';
      this.$refs.tree.setCurrentKey(data.id);
      this.getAppConfigList();
    },
    // 打开新增应用弹窗，设置已选项和禁用项
    openAddDialog() {
      if (!this.treeGridId) {
        this.$message && this.$message.warning('请先选择左侧村镇');
        return;
      }
      this.checkedCount = 0;
      this.defaultCheckedKeys = this.appList.map(item => item.menuId);
      if (!this.isTown) {
        // 设置树中disabled属性
        this.appList.forEach(app => {
          if (app.disabled) {
            this.menuMiniIdTree.forEach(menu => {
              if (menu.id === app.menuId) menu.disabled = true;
            });
          }
        });
      }
      // console.log(this.appList,"appList",this.defaultCheckedKeys,"defaultCheckedKeys");
      this.dialogVisible = true;
    },
    closeAddDialog() {
      this.dialogVisible = false;
      this.defaultCheckedKeys = []
      this.checkedCount = 0;
    },
    // 确认新增应用，去重添加
    confirmAddApp() {
      // 先保留appList中disabled为true的项，去除其他项目，然后再根据选择结果添加
      this.appList = this.appList.filter(item => item.disabled);
      const checkedNodes = this.$refs.addTree.getCheckedNodes();
      checkedNodes.forEach(node => {
        //先判断node.id是否与this.appList数组中项目的menuId相等,相等直接继续循环
        if (this.appList.some(item => item.menuId === node.id)) {
          return;
        }
        this.appList.push({
          menuName: node.title,
          menuSourceLink: node.sourceLink,
          menuId: node.id
        });
      });
      this.closeAddDialog();
    },
    // 移除应用
    removeApp(idx) {
      this.appList.splice(idx, 1);
    },
    // 监听树选中变化，更新已选数量
    onTreeCheckChange() {
      this.checkedCount = (this.$refs.addTree.getCheckedNodes() || []).length;
    },
    // 保存应用配置，增加异常处理
    setAppConfigList() {
      const data = {
        deptId: this.treeGridId,
        //menuIds为appList先过滤disabled为true的,在map出menuId数组
        menuIds: this.appList.map(item => item.menuId)
      };
      setAppConfig(data).then(res => {
        if (res.data.success) {
          this.$message.success('保存成功');
        } else {
          this.$message.error('保存失败');
        }
      }).catch(() => {
        this.$message.error('保存异常');
      });
    },
    onEmptyCardClick() {
      if (!this.treeGridId) {
        this.$message && this.$message.warning('请先选择左侧村镇');
        return;
      }
      // 这里可以写点击后的逻辑
      this.showContentListBox = true;
      // this.$message && this.$message.info('你点击了空内容卡片');
    },
    onAppListClick() {
      this.showContentListBox = false;
    },
    getBlockStyle(id) {
      if (id === 'ConvenientService') {
        return {
          background: 'transparent',
          width: '350px',
          height: '140px',
          padding: '0',
          display: 'flex',
          gap: '8px',
        };
      } else if (id === 'ThreeAffairsDisclosure') {
        return {
          background: 'transparent',
          width: '350px',
          height: '200px',
          padding: '0',
          display: 'flex',
        };
      } else if (id === 'RuralDynamics') {
        return {
          background: 'transparent',
          width: '350px',
          height: '200px',
          padding: '0',
          display: 'flex',
        };
      } else if (id === 'Monitoring') {
        return {
          background: 'transparent',
          width: '350px',
          height: '230px',
          padding: '0',
          display: 'flex',
        };
      }
      return {};
    },
    saveContentConfig() {
      console.log(this.contentList);
      // return
      // 这里可以写保存内容配置的逻辑
      // this.$message.success('内容配置已保存');
      // const data = [];
      let data = {
        codes: this.contentList.map(item => item.id),
        deptId: this.treeGridId,
      };
      console.log(data)
      // return
      setContentConfig(data).then(res => {
        if (res.data.success) {
          this.$message.success('保存成功');
        } else {
          this.$message.error('保存失败');
        }
      }).catch(() => {
        this.$message.error('保存异常');
      });
    },
    addBlockToContentList(block) {
      // 判断是否已存在，避免重复添加
      if (!this.contentList.some(item => item.id === block.id)) {
        this.contentList.push({ ...block });
      } else {
        this.$message && this.$message.info('该内容已添加');
      }
    },
    removeContentBlock(idx) {
      this.contentList.splice(idx, 1);
    },
    updateContentList(newContentList) {
      this.contentList = newContentList;
    }
  }
}
</script>

<style scoped>
/* ================== 卡片样式 ================== */
.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 16px;
  padding: 20px;
  /* min-height: 400px; */
}

/* 响应式调整应用网格 */
@media (max-width: 1200px) {
  .app-grid {
    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
    gap: 12px;
    padding: 16px;
  }
}

@media (max-width: 992px) {
  .app-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .app-grid {
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    gap: 8px;
    padding: 10px;
  }
}
.app-item {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  aspect-ratio: 1 / 1;
  min-width: 0;
  min-height: 0;
  position: relative;
  padding: 0;
  overflow: hidden;
}
.app-item:hover {
  box-shadow: 0 6px 24px rgba(58, 142, 230, 0.1);
  transform: translateY(-2px) scale(1.03);
}
.app-disabled-tag {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.92);
  border: 1px solid #b6e0fe;
  border-radius: 0 8px 0 12px;
  padding: 0 8px;
  font-size: 11px;
  color: #3a8ee6;
  font-weight: 400;
  z-index: 2;
  height: 22px;
  line-height: 22px;
  min-width: 40px;
  text-align: center;
  box-shadow: none;
  letter-spacing: 1px;
  pointer-events: none;
  user-select: none;
}
.app-icon {
  width: 100%;
  aspect-ratio: 1 / 1;
  object-fit: cover;
  border-radius: 10px 10px 0 0;
  margin: 0;
  display: block;
}
.app-name-tag {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.96);
  border-top: 1px solid #e0eafc;
  border-radius: 0 0 10px 10px;
  font-size: 12px;
  color: #000;
  opacity: 0.7;
  font-weight: 500;
  height: 20px;
  line-height: 20px;
  box-shadow: none;
  letter-spacing: 1px;
  pointer-events: none;
  user-select: none;
  z-index: 2;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.app-remove {
  position: absolute;
  top: 0;
  right: 0;
  width: 22px;
  height: 20px;
  background: #fff;
  border-radius: 0 10px 0 12px;
  color: red;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 1px 4px rgba(245, 108, 108, 0.06);
  opacity: 0.7;
  z-index: 4;
  border: 1px solid #ffeaea;
  border-width: 0 1.5px 1.5px 0;
  transition: all 0.15s;
  line-height: 1;
  padding: 0;
}
.app-remove:hover {
  background: #f56c6c;
  color: #fff;
  opacity: 1;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.18);
}

/* 拖拽相关样式 */
.app-draggable {
  display: contents;
}
.app-dragging {
  opacity: 0.5;
}
.app-chosen {
  box-shadow: 0 0 0 2px #409eff;
}

/* 新增按钮样式 */
.app-item.app-add {
  border: 2px dashed #bbb;
  background: #fafbfc;
  color: #bbb;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  transition: border-color 0.2s, color 0.2s;
}
.app-item.app-add:hover {
  border-color: #409eff;
  color: #409eff;
}
.add-icon {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
  line-height: 1;
}

/* ================== 弹窗及树样式 ================== */
.tree-dialog-content {
  /* background: #fcfdff; */
  /* border-radius: 12px; */
  padding: 16px;
  min-height: 120px;
  /* border: 1px solid #e8f4fd; */
}

.tree-select {
  min-height: 240px;
  max-height: 400px;
  overflow-y: auto;
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e8eaec;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tree-dialog-footer {
  border-top: 1px solid #e8f4fd;
  padding: 16px 0 0 0;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* ================== 弹窗整体优化 ================== */
::v-deep .el-dialog {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 380px;
  max-width: 420px;
}
::v-deep .el-dialog__header {
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid #f0f2f7;
  padding-bottom: 8px;
  margin-bottom: 0;
}

/* ================== 弹窗整体优化 ================== */
::v-deep .el-dialog {
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

::v-deep .el-dialog__header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f2f5;
}

::v-deep .el-dialog__title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

::v-deep .el-dialog__body {
  padding: 20px 24px 16px;
}


/* ================== 按钮优化 ================== */
::v-deep .tree-dialog-footer .el-button {
  border-radius: 8px;
  min-width: 80px;
  height: 36px;
  font-weight: 500;
  transition: all 0.2s ease;
}

::v-deep .tree-dialog-footer .el-button--default {
  background: #f8fafc;
  border-color: #e2e8f0;
  color: #64748b;
}

::v-deep .tree-dialog-footer .el-button--default:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
  transform: translateY(-1px);
}

::v-deep .tree-dialog-footer .el-button--primary {
  background: #3b82f6;
  border-color: #3b82f6;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

::v-deep .tree-dialog-footer .el-button--primary:hover {
  background: #2563eb;
  border-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

::v-deep .tree-dialog-footer .el-button--primary.is-disabled {
  background: #cbd5e1;
  border-color: #cbd5e1;
  transform: none;
  box-shadow: none;
}

/* ================== 滚动条优化 ================== */
.tree-select::-webkit-scrollbar {
  width: 6px;
}

.tree-select::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 3px;
}

.tree-select::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.tree-select::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* ================== 其它 ================== */
.app-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 16px 0 0 16px;
}
.app-title {
  font-size: 22px;
  font-weight: bold;
  color: #222;
  letter-spacing: 2px;
  margin: 0;
}
.app-save-btn {
  margin-left: 24px;
  height: 28px;
  font-size: 14px;
  padding: 0 18px;
}
.app-title-divider {
  height: 1.5px;
  background: #ececec;
  margin: 12px 0 18px 0;
  border-radius: 1px;
  width: 100%;
}
.dept-label {
  color: #409eff;
  font-size: 18px;
  font-weight: 500;
  margin-right: 18px;
}

.miniapp-banner {
  width: 100%;
  height: 140px;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 16px;
  position: relative;
  background: linear-gradient(135deg, #a8e063 0%, #56ab2f 100%);
}
.miniapp-banner-title {
  position: absolute;
  left: 24px;
  top: 32px;
  z-index: 2;
}
.miniapp-banner-title-main {
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  letter-spacing: 2px;
}
.miniapp-banner-title-sub {
  font-size: 22px;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);
  margin-top: 4px;
}
.miniapp-banner-tools {
  position: absolute;
  right: 16px;
  top: 16px;
  z-index: 3;
  display: flex;
  gap: 8px;
}


.contentListBox {
  width: 100%;
  /* margin: 10px auto; */
  padding: 0;
}
.content-blocks-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 12px; /* 统一设置块之间的间隔 */
  padding: 8px;
}
.color-block {
  position: relative;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: #888;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin: 0 8px;
  transition: box-shadow 0.2s;
}
.select-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 2;
  padding: 0 10px;
  height: 24px;
  line-height: 24px;
}
.content-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 16px 0 0 16px;
}
.content-title {
  font-size: 22px;
  font-weight: bold;
  color: #222;
  letter-spacing: 2px;
  margin: 0;
}
.content-save-btn {
  margin-left: 24px;
  height: 28px;
  font-size: 14px;
  padding: 0 18px;
}
.content-title-divider {
  height: 1.5px;
  background: #ececec;
  margin: 12px 0 18px 0;
  border-radius: 1px;
  width: 100%;
}
.content-blocks-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  background-color: #f5f5f5;
}
/* contentList 区域 color-block 间距和删除按钮样式 */
.contentListBox .color-block {
  margin-right: 16px;
  margin-bottom: 16px;
}
.content-remove-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 22px;
  height: 22px;
  background: #fff;
  border-radius: 50%;
  color: #f56c6c;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 1px 4px rgba(245, 108, 108, 0.08);
  border: 1px solid #ffeaea;
  transition: all 0.15s;
  z-index: 10;
}
.content-remove-btn:hover {
  background: #f56c6c;
  color: #fff;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.18);
}
/* 内容块拖拽相关样式 */
.content-draggable {
  display: contents;
}

.content-dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.content-chosen {
  box-shadow: 0 0 0 2px #409eff;
  cursor: grabbing;
}

.color-block {
  cursor: grab;
  transition: all 0.2s ease;
}

.color-block:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.more-item {
  opacity: 0.8;
}

.more-icon {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.more-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.more-dots {
  font-size: 24px;
  color: #4a9b8e;
  font-weight: bold;
  letter-spacing: 2px;
}







/* 移动端优化 */
@media (max-width: 768px) {
  .box {
    margin-bottom: 15px;
  }
}
</style>







<!--
 * @Author: chenn26
 * @Date: 2023-05-10 16:23:14
 * @LastEditors: chenn26
 * @LastEditTime: 2023-05-29 11:31:23
 * @Description: 乡镇大屏
-->
<template>
  <div class="screenTown">
    <!-- 背景图片 -->
    <!-- <img src="/img/screen/star-squashed.jpg" class="back-shadow-screen" alt="shadow" /> -->
    <div>
      <!--标题 -->
      <div class="title-header">
        <img src="/img/townScreen/header.png" class="title-back" alt="" />
      </div>
      <div class="title-back-desc">数字乡村镇级大屏</div>
      <!--时间 -->
      <div class="screen-time">
        <div class="screen-time-desc">{{ time }}</div>
      </div>
      <!--地点 -->
      <div class="screen-local">
        <div class="screen-local-desc">
          <!-- <span class="screen-local-title">&nbsp;&nbsp;{{this.villageName}}</span>\ -->
          <!-- <select class="screen-local-title">
            <option value="">{{ townName }}</option>
          </select> -->
          <el-select popper-class="townSelect" size="mini" v-model="townName" placeholder="请选择" class="screen-local-title"
            clearable @change="townNameChange">
            <el-option v-for="item in countryList" :key="item.value" :label="item.label" :value="item.value"
              @click.native="townClick(item)">
            </el-option>
          </el-select>
        </div>
      </div>
    </div>
    <!-- 内容栏 -->
    <div class="screen-content">
      <img src="/img/townScreen/borderoutside.png" alt="left" class="outside-on-back" />
      <!--左侧栏 -->
      <div class="screen-left pointer-events-auto">
        <div class="screen-left-all">
          <img src="/img/townScreen/boxborder.png" alt="left" class="left-on-back" />
          <!--左侧栏 1-->
          <div class="left-on">
            <LeftOn :townLeaderList="townLeaderList" :townBrachDesc="townBrachDesc" />
          </div>
          <img src="/img/townScreen/boxborder.png" alt="left" class="left-bottom-back" />
          <!--左侧栏 2-->
          <div class="left-bottom">
            <LeftBottom :honorList="honorList" />
          </div>
        </div>
      </div>
      <!--中间栏 -->
      <div class="screen-center pointer-events-auto">
        <img src="/img/townScreen/boxborder.png" alt="" class="center-on-back" />
        <!--中间栏 1-->
        <div class="center-on">
          <CenterOn :villageDesc="villageDesc" :descImageList="descImageList" :villageName="villageName"
            :villageData="villageData" />
        </div>
        <img src="/img/townScreen/boxborder.png" alt="" class="center-bottom-back" />
        <!--中间栏 2-->
        <div class="center-bottom">
          <CenterBottom :populationNum="populationNum" :farmerNum="farmerNum" :partyMemberNum="partyMemberNum"
            :situationData="situationData" :areaUnit="areaUnit" :villageArea="villageArea" />
        </div>
      </div>
      <!--  -->
      <!--右侧栏 -->
      <div class="screen-right pointer-events-auto">
        <img src="/img/townScreen/boxborder.png" alt="" class="right-on-back" />
        <!--右侧栏 1-->
        <div class="right-on">
          <RightOn :deptId="deptId"/>
        </div>
        <img src="/img/townScreen/boxborder.png" alt="" class="right-center-back" />
        <!--右侧栏 2-->
        <div class="right-center">
          <RightCenter />
        </div>
        <img src="/img/townScreen/boxborder.png" alt="" class="right-bottom-back" />
        <!--右侧栏 3-->
        <div class="right-bottom">
          <RightBottom />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// import { mapGetters } from "vuex";

import LeftOn from "./leftOn";
import LeftBottom from "./leftBottom";
import CenterOn from "./centerOn";
import CenterBottom from "./centerBottom";
import RightOn from "./rightOn";
import RightCenter from "./rightCenter";
import RightBottom from "./rightBottom";

import { getTownList } from "@/api/screen/townScreen";
import { getDateAndTime } from '@/util/util'
import "./index.scss";
import { getVillage } from "@/api/screen/screen";

export default {
  components: {
    LeftOn,
    LeftBottom,
    CenterOn,
    CenterBottom,
    RightOn,
    RightCenter,
    RightBottom,
  },
  data() {
    return {
      townBrachDesc: '', //左上-镇支部详情简介
      townLeaderList: {}, //左上-乡镇党领导列表

      honorList: [], //左下-荣誉列表

      villageDesc: "", //中上-简介内容
      descImageList: [], //中上-简介图片
      villageData: { //中上-镇级数据
        villageSize: 0, //行政村数
        villageArea: 0, //总面积
        populationNum: 0, //总人口
        homeSize: 0, //家庭户数
        partyMemberNum: 0, //党员人数
        totalOutput: 0,//现国内生产总值
      },

      situationData: [
        {
          name: "耕地面积",
          value: 0,
        },
        {
          name: "林地面积",
          value: 0,
        },
        {
          name: "水域面积",
          value: 0,
        }
      ],
      areaUnit: "单位",
      timeTimer: null,
      time: "",
      townName: "",
      countryList: [],
      isShow: false,
      deptId: "",
      clickId:""
    };
  },

  created() {
    //获取应用使用情况
    // this.getTownList();
    // this.getVillage();
    this.deptId = this.$store.getters.userInfo.dept_id;
    this.$router.$avueRouter.setTitle("镇级大屏");
    //一秒刷新一次显示时间
    this.timeTimer = setInterval(() => {
      let date = new Date(); // 修改数据date
      this.time = getDateAndTime(date);
    }, 1000);
  },
  destroyed() {
    if (this.timeTimer) {
      clearInterval(this.timeTimer)
    }
  },
  computed: {
    // ...mapGetters(["userInfo"]),
  },
  props: [],
  methods: {
    townNameChange(val) {
      if (val === '') {
        this.townName = this.villageName;
      }
    },
    townClick(row) {
      this.clickId = row.value;
      var cur = window.document.location.href;
      var pathname = '#';
      var pos = cur.indexOf(pathname);
      var localhostPath = cur.substring(0, pos+2);
      console.log(localhostPath);
      window.open(localhostPath+'bigScreen2/index?type=v2&deptId='+this.clickId);
      this.townName = this.villageName;
      return false;
    },
    getVillage() {
      getVillage().then(res => {
        this.townLeaderList = res.data.data.townLeaderList || {};
        this.townBrachDesc = res.data.data.branchDesc || '';
        this.honorList = res.data.data.honorList;
        this.villageDesc = res.data.data.villageDesc;
        this.descImageList = res.data.data.descImageList;
        this.villageName = res.data.data.villageName;
        this.villageData.villageSize = res.data.data.villageSize || 0;
        this.villageData.villageArea = res.data.data.allArea || 0;
        this.villageData.populationNum = res.data.data.populationNum || 0;
        this.villageData.homeSize = res.data.data.homeSize || 0;
        this.villageData.partyMemberNum = res.data.data.partyMemberNum || 0;
        this.villageData.totalOutput = res.data.data.totalOutput || 0;
      })
    },
    getTownList() {
      let params = {
        parentId: this.deptId
      }
      getTownList(params).then(res => {
        this.townName = res.data.data[0].deptName;
        if (res.data.data[0].children) {
          res.data.data[0].children.forEach(item => {
            this.countryList.push({
              value: item.id,
              label: item.deptName
            })
          })
        }
      })
    },
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-input--mini .el-input__inner {
  height: 3vh !important;
  line-height: 3vh !important;
}

::v-deep .el-input--mini .el-input__icon {
  line-height: 3vh !important;
}

.townSelect {
  .el-scrollbar {
    background: #05072c !important;
    color: #fff !important;
  }

  .el-scrollbar .el-select-dropdown__item {
    line-height: 4vh !important;
  }
}
</style>

/*
 * @Date: 2025-06-24 09:38:28
 * @LastEditors: linqh21
 * @LastEditTime: 2025-06-30 17:29:05
 * @Description:
 * @FilePath: \src\util\sm2.js
 */
import website from '@/config/website';
import { sm2 } from 'sm-crypto';

/**
 * sm2 加密方法
 * @param data
 * @returns {*}
 */
export function encrypt(data) {
  try {
    return sm2.doEncrypt(data, website.oauth2.publicKey, 0);
  } catch {
    return '';
  }
}

.center-bottom-content1 {
  position: absolute;
  width: 100%;
  height: 100%;
  .center-bottom-header {
    position: absolute;
    width: 100%;
    white-space: nowrap;
    top: 1vh;
    left: 50%;
    transform: translate(-50%, 0);
    .left-decorate {
      width: 40%;
    }
    .title {
      letter-spacing: 3px;
      vertical-align: top;
      margin: 0 5%;
      color: #5bffdc;
      font-size: 2vh;
    }
    .right-decorate {
      width: 40%;
      transform: rotateY(180deg);
    }
  }
  .content {
    position: absolute;
    float: left;
    margin-top: 4vh;
    width: 100%;
    color: #fff;

    .select-1 {
      display: inline-block;
      width: 33%;
      height: 3vh;
      font-size: 2vh;
      text-align: center;
      cursor: pointer;
    }

    .select-2 {
      width: 34%;
      font-size: 2vh;
      height: 3vh;
      cursor: pointer;
      display: inline-block;
      text-align: center;
    }
    .select-3 {
      width: 33%;
      font-size: 2vh;
      height: 3vh;
      cursor: pointer;
      display: inline-block;
      text-align: center;
    }

    .title-select-back {
      position: absolute;
      width: 33%;
      height: 3vh;
    }

    .content-value {
      // margin-top: 1vh;
      .header-item {
        text-align: center;
        font-size: 13px;
        height: 35px;
        color: white;
      }
      .rows {
        .row-item {
          .ceil {
            text-align: center;
          }
        }
      }
    }
  }
}

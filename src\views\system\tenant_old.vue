<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" ref="crud" v-model="form" :page.sync="page" :permission="permissionList" :before-open="beforeOpen" @row-del="rowDel" @row-update="rowUpdate" @row-save="rowSave" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <template slot="menuLeft">
        <el-button v-if="permission.tenant_add && !recycleMode" type="primary" size="small" icon="el-icon-plus" @click="$refs.crud.rowAdd()">新 增
        </el-button>
        <el-button type="danger" size="small" icon="el-icon-delete" v-if="permission.tenant_delete && !recycleMode" plain @click="handleDelete">删 除
        </el-button>
        <el-button type="primary" plain size="small" icon="el-icon-refresh" v-if="userInfo.role_name.includes('administrator') && !recycleMode" @click="handleRecycle">回收站
        </el-button>
        <el-button type="success" plain size="small" icon="el-icon-check" v-if="userInfo.role_name.includes('administrator') && recycleMode" @click="handleRecyclePass">恢 复
        </el-button>
        <el-button type="danger" plain size="small" icon="el-icon-close" v-if="userInfo.role_name.includes('administrator') && recycleMode" @click="handleRecycleRemove">删 除
        </el-button>
        <el-button type="primary" plain size="small" icon="el-icon-refresh-left" v-if="userInfo.role_name.includes('administrator') && recycleMode" @click="handleRecycleBack">返 回
        </el-button>
        <el-tooltip class="item" effect="dark" content="给租户配置账号额度、过期时间等授权信息" placement="top">
          <el-button size="small" plain type="info" v-if="userInfo.role_name.includes('administrator') && !recycleMode" icon="el-icon-setting" @click="handleSetting">批量授权配置
          </el-button>
        </el-tooltip>
      </template>
      <template slot="menuRight">
        <!-- <el-tooltip class="item" effect="dark" content="将自定义的数据源定制为租户绑定的独立数据源" placement="top">
          <el-button size="small"
                     plain
                     v-if="userInfo.role_name.includes('administrator') && !recycleMode"
                     icon="el-icon-tickets"
                     @click="handleDatasourceSetting">数据源管理
          </el-button>
        </el-tooltip> -->
        <el-tooltip class="item" effect="dark" content="将自定义的菜单集合定制为租户绑定的菜单产品包" placement="top">
          <el-button size="small" plain v-if="userInfo.role_name.includes('administrator') && !recycleMode" icon="el-icon-set-up" @click="handlePackageSetting">产品包管理
          </el-button>
        </el-tooltip>
      </template>
      <template #menu="scope">
        <!-- <el-button
          v-if="userInfo.role_name.includes('administrator') && !recycleMode"
          type="text"
          icon="el-icon-coin"
          size="small"
          @click.stop="handleRowDatasource(scope.row)"
        >数据源
        </el-button> -->
        <el-button v-if="userInfo.role_name.includes('administrator') && !recycleMode" type="text" icon="el-icon-delete" size="small" @click.stop="$refs.crud.rowDel(scope.row)">删 除
        </el-button>
        <el-button v-if="userInfo.role_name.includes('administrator') && recycleMode" type="text" icon="el-icon-delete" size="small" @click.stop="handleRecycleRemoveOne(scope.row)">删 除
        </el-button>
        <el-button v-if="userInfo.role_name.includes('administrator') && !recycleMode" type="text" icon="el-icon-notebook-1" size="small" @click.stop="handleRowPackage(scope.row)">产品包
        </el-button>
      </template>
      <template slot-scope="{row}" slot="accountNumber">
        <el-tag>{{ row.accountNumber > 0 ? row.accountNumber : '不限制' }}</el-tag>
      </template>
      <template slot-scope="{row}" slot="expireTime">
        <div v-if="row.expireTime">
          <el-tag v-if=" new Date(row.expireTime) < new Date()" type="danger">{{ row.expireTime ? row.expireTime.substring(0,10) : '不限制' }}</el-tag>
          <el-tag v-else type="success">{{ row.expireTime ? row.expireTime.substring(0,10) : '不限制' }}</el-tag>
        </div>
        <el-tag v-else>{{ '不限制'}}</el-tag>
      </template>
      <!-- <template slot-scope="{type}" slot="regionCodeForm">
        <avue-input-tree :props="areaDataProps" v-model="form.regionCode" placeholder="请选择内容" :dic="areaData"></avue-input-tree>
      </template> -->
    </avue-crud>
    <el-dialog title="租户授权配置" append-to-body :visible.sync="box" width="450px">
      <avue-form :option="settingOption" v-model="settingForm" @submit="handleSubmit" />
    </el-dialog>
    <!-- <el-dialog title="租户数据源配置"
               append-to-body
               :visible.sync="datasourceBox"
               width="450px">
      <avue-form :option="datasourceOption" v-model="datasourceForm" @submit="handleDatasourceSubmit"/>
    </el-dialog> -->
    <el-dialog title="租户产品包配置" append-to-body :visible.sync="packageBox" @close="handlePackageFormReset">
      <avue-form v-if="packageBox" ref="formPackage" :option="packageOption" v-model="packageForm" @submit="handlePackageSubmit" @reset-change="handlePackageFormReset">
        <template slot="webMenuIds">
          <el-tree :data="menuWebIdTree" default-expand-all read-only show-checkbox node-key="id" ref="menuWebIdTree" :default-checked-keys="packageForm.webMenuIds" :props="{ label: 'title', children: 'children',disabled:()=>true }" highlight-current style="border: 1px solid #ebeef5; border-radius: 4px; padding: 8px; height: 300px; overflow-y: auto;" />
        </template>
        <template slot="appMenuIds">
          <el-tree :data="menuMiniIdTree" default-expand-all read-only show-checkbox node-key="id" ref="menuWebIdTree" :default-checked-keys="packageForm.appMenuIds" :props="{ label: 'title', children: 'children',disabled:()=>true }" highlight-current style="border: 1px solid #ebeef5; border-radius: 4px; padding: 8px; height: 300px; overflow-y: auto;" />
        </template>
      </avue-form>

    </el-dialog>
    <!-- <el-drawer title="租户数据源管理" append-to-body :visible.sync="datasourceSettingBox" size="1000px">
      <tenant-datasource></tenant-datasource>
    </el-drawer> -->
    <el-drawer title="租户产品包管理" append-to-body :visible.sync="packageSettingBox" size="1000px">
      <!-- <tenant-package></tenant-package> -->
    </el-drawer>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  remove,
  update,
  add,
  setting,
  packageInfo,
  packageSetting,
  recycle, pass
} from "@/api/system/tenant";
import { getDetail as packageDetail } from "@/api/system/tenantpackage";
import { mapGetters } from "vuex";
import { getMenuTree } from "@/api/system/menu";
import { validatenull } from "@/util/validate";
import { getLazyTreeList } from "@/api/base/region";
// import { myMessage } from '@/util/myMessage.js';
export default {
  data() {
    return {
      form: {},
      selectionList: [],
      query: {},
      loading: true,
      box: false,
      datasourceBox: false,
      datasourceSettingBox: false,
      packageBox: false,
      packageSettingBox: false,
      recycleMode: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        selection: true,
        addBtn: false,
        viewBtn: true,
        menuWidth: 380,
        dialogWidth: 900,
        dialogClickModal: false,
        delBtn: false,
        column: [
          // {
          //   label: "行政区划",
          //   prop: "regionCode",
          //   type: "tree",
          //   hide: true,
          //   span: 24,
          //   filter: false,
          //   viewDisplay: false,
          //   addDisplay: true,
          //   editDisplay: false,
          //   props: {
          //     label: "name",
          //     value: "id",
          //     // title: 'title',
          //   },
          //   dicData: [],
          //   lazy: true,
          //   nodeClick: (value) => {
          //     console.log(value, 'value')
          //     this.form.tenantName = value.name
          //     // console.log(value1,'value1')
          //     // console.log(value2,'value2')
          //   },
          //   treeLoad: (node, resolve) => {
          //     // console.log("node", node)
          //     var list = [];
          //     let callback = () => {
          //       resolve((list || []).map(ele => {
          //         return Object.assign(ele, {
          //           leaf: !ele.hasChildren,
          //           disabled: ele.hasChildren
          //         })
          //       }));
          //     }
          //     if (node.checked) {
          //       callback()
          //     }
          //     if (node.level == 0) {
          //       getLazyTreeList(35).then(res => {
          //         // console.log("获取子节点", res)
          //         list = res.data.data
          //         callback()
          //       });
          //     } else if (node.level == 1) {
          //       getLazyTreeList(node.data.id).then(res => {
          //         // console.log("获取子节点", res)
          //         list = res.data.data
          //         callback()
          //       });
          //     } else if (node.level == 2) {
          //       getLazyTreeList(node.data.id).then(res => {
          //         // console.log("获取子节点2", res)
          //         list = res.data.data
          //         resolve((list || []).map(ele => {
          //           // ele.title = `${node.data.provinceName}/${node.data.cityName}/${node.data.districtName}/${ele.name}`;
          //           return Object.assign(ele, {
          //             leaf: true,
          //             disabled: false
          //           })
          //         }));
          //       });

          //     }
          //   },
          //   rules: [
          //     {
          //       required: true,
          //       message: "请选择所属村镇",
          //       trigger: ["blur", "change"],
          //     },
          //   ],
          // },
          {
            label: "行政区划",
            prop: "regionName",
            hide: true,
            span: 24,
            addDisplay: false,
            editDisplay: true,
            editDisabled: true,
          },
          {
            label: "租户ID",
            prop: "tenantId",
            width: 100,
            search: true,
            addDisplay: false,
            editDisplay: false,
            minLength: 2,
            maxlength: 20,
            span: 24,
            rules: [{
              required: true,
              message: "请输入租户ID",
              trigger: "blur"
            }]
          },
          {
            label: "租户名称",
            prop: "tenantName",
            search: true,
            span: 24,
            minLength: 2,
            maxlength: 20,
            rules: [{
              required: true,
              message: "请输入租户名称",
              trigger: "blur"
            }]
          },
          {
            label: "联系人",
            prop: "linkman",
            search: true,
            maxlength: 20,
            rules: [{
              required: true,
              message: "请输入联系人",
              trigger: "blur"
            }]
          },
          {
            label: "联系电话",
            prop: "contactNumber",
            maxlength: 20,
            rules: [
              {
                required: true,
                message: "请输入联系电话",
                trigger: "blur"
              },
              {
                pattern: /^(?!.*-$)^\d+(-\d+)*$/,
                message: "请输入正确的联系电话",
                trigger: "blur"
              },
            ]
          },
          {
            label: "联系地址",
            prop: "address",
            span: 24,
            minRows: 2,
            maxlength: 250,
            type: "textarea",
            hide: true,
          },
          {
            label: "账号额度",
            prop: "accountNumber",
            slot: true,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "过期时间",
            prop: "expireTime",
            slot: true,
            addDisplay: false,
            editDisplay: false,
            format: "yyyy-MM-dd",
          },
          {
            label: "开通时间",
            prop: "createTime",
            addDisplay: false,
            editDisplay: false,
          },
        ]
      },
      data: [],
      settingForm: {},
      settingOption: {
        column: [
          {
            label: "账号额度",
            labelTip: '代表租户可创建的最大额度，若不限制则默认为-1',
            prop: "accountNumber",
            type: "number",
            span: 24,
            max: 99999,
          },
          {
            label: "过期时间",
            labelTip: '代表租户可使用的最后日期，若不限制则默认为空',
            prop: "expireTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            span: 24,
          },
        ]
      },

      packageForm: {},
      packageOption: {
        labelWidth: 140,
        column: [
          {
            label: "产品包",
            prop: "packageId",
            search: true,
            clearable: false,
            dicFlag: true,
            span: 24,
            type: "select",
            dicUrl: "/api/blade-system/tenant-package/select",
            props: {
              label: "packageName",
              value: "id"
            }
          },
          {
            label: "web菜单预览",
            prop: "webMenuIds",
            span: 12,
            formslot: true,
          },
          {
            label: "小程序菜单预览",
            prop: "appMenuIds",
            span: 12,
            formslot: true,
          },

        ]
      },
      menuWebIdTree: [],
      menuMiniIdTree: [],

      // areaData: [],
      // areaDataProps: {
      //     label: 'title',
      //     value: 'value',
      //     leaf: 'hasChildren'
      // }
    };
  },
  watch: {
    'packageForm.packageId'() {
      if (!validatenull(this.packageForm.packageId)) {
        packageDetail(this.packageForm.packageId).then(res => {
          this.initData();
          this.packageForm.webMenuIds = res.data.data.webMenuIds;
          this.packageForm.appMenuIds = res.data.data.appMenuIds;
        });
      } else {
        this.packageForm.menuMiniId = [];
        this.packageForm.menuWebId = [];
        //  this.initData();
      }
    }
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.tenant_add, false),
        viewBtn: this.vaildData(this.permission.tenant_view, false),
        delBtn: this.vaildData(this.permission.tenant_delete, false),
        editBtn: this.vaildData(this.permission.tenant_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    // tenantId() {
    //   return this.selectionList[0].tenantId;
    // }
  },
  mounted() {
    // this.initData();
  },
  methods: {

    initData() {
      getMenuTree(1).then(res => {
        this.menuWebIdTree = res.data.data;
      });
      getMenuTree(2).then(res => {
        this.menuMiniIdTree = res.data.data;
      });
    },


    rowSave(row, done, loading) {
      let params = {
        regionCode: row.regionCode,
        tenantName: row.tenantName,
        linkman: row.linkman,
        contactNumber: row.contactNumber,
        address: row.address
      }
      add(params).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowDel(row) {
      this.$confirm("删除后所选租户将进入回收站，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return recycle(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    beforeOpen(done, type) {
      if (["view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          const data = res.data.data;
          if (!(data.accountNumber > 0)) {
            data.accountNumber = "不限制";
          }
          if (!data.expireTime) {
            data.expireTime = "不限制";
          }
          this.form = data;
        });
      }
      done();
    },
    handlePackageFormReset() {
      this.webMenuIds = [];
      this.appMenuIds = [];
      this.menuWebIdTree = [];
      this.menuMiniIdTree = [];
    },
    // beforeFormPackageOpen(done, type){
    //   this.initData();
    //   console.log("sss");
    //   done();
    // },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("删除后所选租户将进入回收站，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return recycle(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleRecycle() {
      this.recycleMode = true;
      this.$refs.crud.option.editBtn = false;
      this.page.currentPage = 1;
      // this.$refs.crud.option.delBtn = false;
      this.onLoad(this.page, this.query);
    },
    handleRecyclePass() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据');
        return;
      }
      this.$confirm('确定将所选租户进行恢复?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return pass(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleRecycleRemoveOne(row) {
      this.$confirm('确定将所选租户永久删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleRecycleRemove() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据');
        return;
      }
      this.$confirm('确定将所选租户永久删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleRecycleBack() {
      this.recycleMode = false;
      this.$refs.crud.option.editBtn = true;
      // this.$refs.crud.option.delBtn = true;
      // this.$refs.crud.refreshTable();
      this.onLoad(this.page, this.query);
    },
    handleSetting() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.selectionList.length === 1) {
        getDetail(this.selectionList[0].id).then(res => {
          const data = res.data.data;
          this.settingForm.accountNumber = data.accountNumber;
          this.settingForm.expireTime = data.expireTime;
        });
      } else {
        this.settingForm.accountNumber = -1;
        this.settingForm.expireTime = '';
      }
      this.box = true;
    },
    // handleRowDatasource(row) {
    //   getDetail(row.id).then(res => {
    //     const data = res.data.data;
    //     this.datasourceForm.tenantId = row.tenantId;
    //     this.datasourceForm.datasourceId = data.datasourceId;
    //   });
    //   this.datasourceBox = true;
    // },
    // handleDatasource() {
    //   if (this.selectionList.length === 0) {
    //     this.$message.warning("请选择至少一条数据");
    //     return;
    //   }
    //   if (this.selectionList.length !== 1) {
    //     this.$message.warning("只能选择一条数据");
    //     return;
    //   }
    //   getDetail(this.selectionList[0].id).then(res => {
    //     const data = res.data.data;
    //     this.datasourceForm.tenantId = this.selectionList[0].tenantId;
    //     this.datasourceForm.datasourceId = data.datasourceId;
    //   });
    //   this.datasourceBox = true;
    // },
    handleRowPackage(row) {
      // console.log(row);
      packageInfo(row.id).then(res => {
        const data = res.data.data;
        console.log(data, "data");
        if (data) {
          this.initData();
        }
        this.packageForm.tenantId = row.tenantId;
        this.packageForm.packageId = data ? data.id : "";
        this.packageForm.webMenuIds = data ? data.webMenuIds : [];
        this.packageForm.appMenuIds = data ? data.appMenuIds : [];
        // console.log(this.packageForm.webMenuIds,"this.packageForm.webMenuIds");
        this.packageBox = true;
      });

      //更新字典远程数据
      // setTimeout(() => {
      //   const form = this.$refs.formPackage;
      //   form.updateDic('packageId');
      // }, 10);
    },
    handlePackage() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.selectionList.length !== 1) {
        this.$message.warning("只能选择一条数据");
        return;
      }
      if (this.selectionList.length === 1) {
        packageInfo(this.selectionList[0].id).then(res => {
          const data = res.data.data;
          this.packageForm.tenantId = this.selectionList[0].tenantId;
          this.packageForm.packageId = data.id;
          this.packageForm.menuId = data.menuId;
        });
      } else {
        this.packageForm.menuId = '';
      }
      this.packageBox = true;
      //更新字典远程数据
      setTimeout(() => {
        const form = this.$refs.formPackage;
        form.updateDic('packageId');
      }, 10);
    },
    handleDatasourceSetting() {
      this.datasourceSettingBox = true;
    },
    handlePackageSetting() {
      this.packageSettingBox = true;
    },
    handleSubmit(form, done, loading) {
      if (form.expireTime) form.expireTime = form.expireTime + " 23:59:59";
      setting(this.ids, form).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "配置成功!"
        });
        done();
        this.box = false;
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    // handleDatasourceSubmit(form, done, loading) {
    //   datasource(form.tenantId, form.datasourceId).then(() => {
    //     this.$message({
    //       type: "success",
    //       message: "配置成功!"
    //     });
    //     done();
    //     this.datasourceBox = false;
    //   }, error => {
    //     window.console.log(error);
    //     loading();
    //   });
    // },
    handlePackageSubmit(form, done, loading) {
      packageSetting(form.tenantId, form.packageId).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "配置成功!"
        });
        done();
        this.handlePackageFormReset()
        this.packageBox = false;
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query, {
          status: this.recycleMode ? -1 : 1,
        })
      ).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      }).finally(() => {
        this.loading = false;
      });
    },
  }
};
</script>

<style>
</style>

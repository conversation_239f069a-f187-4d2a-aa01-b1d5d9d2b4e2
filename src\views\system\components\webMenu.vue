<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" :search.sync="query" ref="crud" v-model="form" :permission="permissionList" :before-open="beforeOpen" :before-close="beforeClose" @row-del="rowDel" @row-update="rowUpdate" @row-save="rowSave" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad" @tree-load="treeLoad">
      <template slot="menuLeft">
        <el-button type="primary" size="small" icon="el-icon-plus" v-if="permission.menu_add" @click="handleAddLevel">新 增</el-button>
        <el-button type="danger" size="small" icon="el-icon-delete" v-if="permission.menu_delete" @click="handleDelete">删 除</el-button>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button type="text" icon="el-icon-circle-plus-outline" size="small" @click.stop="handleAdd(scope.row,scope.index)" v-if="userInfo.role_name.includes('admin') && scope.row.category === 1">新增子项</el-button>
      </template>
      <template slot="sourceForm">
        <avue-input-icon v-model="form.source" placeholder="请选择图标" :icon-list="iconList"></avue-input-icon>
      </template>
      <!-- <template slot-scope="{type}" slot="parentIdForm">
      </template> -->
      <template slot-scope="{row}" slot="source">
        <div style="text-align:center">
          <el-image v-if="row.sourceLink" :src="row.sourceLink" :preview-src-list="[row.sourceLink]" style="width: 40px; height: 40px; cursor: pointer;" fit="cover" />
          <i v-else :class="row.source" />
        </div>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getLazyList, remove, update, add, getMenu, getMenuTree } from "@/api/system/menu";
import { mapGetters } from "vuex";
import iconList from "@/config/iconList";

export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      selectionList: [],
      parentId: 0,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        height: 'auto',
        calcHeight: 30,
        lazy: true,
        tip: false,
        simplePage: true,
        searchShow: true,
        searchMenuSpan: 6,
        dialogWidth: "60%",
        tree: true,
        border: true,
        index: false,
        selection: true,
        viewBtn: true,
        menuWidth: 300,
        dialogClickModal: false,
        addBtn: false,
        column: [
          {
            label: "菜单名称",
            prop: "name",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入菜单名称",
                trigger: "blur"
              }
            ]
          },
          {
            label: "平台类型",
            prop: "type",
            type: "select",
            dataType: "string",
            dicUrl: "/api/blade-system/dict/dictionary?code=menu_type",
            dicFlag: true,
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            disabled: true,
            hide: false,
            rules: [
              { required: true, message: "请选择平台类型", trigger: "change" }
            ]
          },
          {
            label: "菜单类型",
            prop: "category",
            type: "radio",
            editDisabled: true,
            span: 12,
            dicData: [
              {
                label: "菜单",
                value: 1
              },
              {
                label: "按钮",
                value: 2
              }
            ],
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择菜单类型",
                trigger: "blur"
              }
            ]
          },
          {
            label: "路由地址",
            prop: "path",
            rules: [
              {
                required: true,
                message: "请输入路由地址",
                trigger: "blur"
              }
            ]
          },
          {
            label: "上级菜单",
            prop: "parentId",
            type: "tree",
            dicData: [],
            hide: true,
            addDisabled: false,
            display: false,
            props: {
              label: "title"
            },
            rules: [
              {
                required: false,
                message: "请选择上级菜单",
                trigger: "click"
              }
            ]
          },
          {
            label: "菜单图标",
            prop: "source",
            formslot: true,
            display: true,
            rules: [
              {
                required: true,
                message: '请上传图标',
                trigger: 'change'
              }
            ]
          },
          {
            label: "菜单别名",
            prop: "alias",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入菜单别名",
                trigger: "blur"
              }
            ]
          },
          {
            label: "菜单编号",
            prop: "code",
            search: true,
            span: 12,
            rules: [
              {
                required: true,
                message: "请输入菜单编号",
                trigger: "blur"
              }
            ]
          },
          {
            label: "菜单排序",
            prop: "sort",
            type: "number",
            span: 12,
            max: 99999999,
            rules: [
              {
                required: true,
                message: "请输入菜单排序",
                trigger: "blur"
              }
            ]
          },
          {
            label: "菜单备注",
            prop: "remark",
            type: "textarea",
            span: 24,
            minRows: 2,
            hide: true
          },
        ]
      },
      data: [],
      iconList: iconList,
      maps: new Map()
    };
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.menu_add, false),
        viewBtn: this.vaildData(this.permission.menu_view, false),
        delBtn: this.vaildData(this.permission.menu_delete, false),
        editBtn: this.vaildData(this.permission.menu_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  watch: {
    'form.category'() {
      const category = Number(this.form.category);
      this.$refs.crud.option.column.filter(item => {
        if (item.prop === "path") {
          item.rules[0].required = category === 1;
        }
        if (item.prop === "parentId") {
          item.display = true;
        }
        if (item.prop === "source") {
          item.display = true;
        }
      });
    },
  },
  methods: {
    vaildData(val, def) {
      return typeof val !== "undefined" && val !== null ? val : def;
    },
    findObject(list, prop) {
      return list.find(item => item.prop === prop);
    },
    initData(flag=false,id) {
      function filterTree(tree, id) {
        return tree
          .filter(item => item.id !== id) // 筛选当前层级的节点
          .map(item => ({
            ...item,
            // 递归处理 children
            children: item.children ? filterTree(item.children, id) : undefined
          }));
      }

      getMenuTree(1).then(res => {
        const column = this.findObject(this.option.column, "parentId");
        if(flag){
          //res.data.data是一颗树 有children也要变量
         column.dicData = filterTree(res.data.data, id);
        }else{
          column.dicData = res.data.data;
        }

        // column.dicData = res.data.data;
      });
    },
    // initEditData() {
    //   getMenuTree(1).then(res => {
    //     const column = this.findObject(this.option.column, "parentId");
    //     column.dicData = res.data.data;
    //   });
    // },
    handleAddLevel() {
      this.$refs.crud.rowAdd();
    },
    handleAdd(row) {
      this.parentId = row.id;
      const column = this.findObject(this.option.column, "parentId");
      column.value = row.id;
      column.addDisabled = true;
      this.$refs.crud.rowAdd();
    },
    rowSave(row, done, loading) {
      console.log(row,'row')
      row.type = 1;
      add(row).then((res) => {
        const data = res.data.data;
        row.id = data.id;
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        let treeData = this.maps.get(row.parentId)
        // console.log(treeData,'treeData')
        if (treeData) this.treeLoad(treeData.tree, treeData.treeNode, treeData.resolve)
        else{
          this.parentId = "0"
          this.onLoad(this.page);
        }
        done(row);
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowUpdate(row, index, done, loading) {
      console.log(row,'row')
      row.type = 1;
      if(row.id == row.parentId){
        this.$message.error('上级菜单不能是自己')
        loading()
        return
      }
      update(row).then((res) => {
        // console.log(res,'res')
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        let treeData = this.maps.get(row.parentId)
        // console.log(treeData,'treeData')
        if (treeData) this.treeLoad(treeData.tree, treeData.treeNode, treeData.resolve)
        else{
          this.parentId = "0"
          this.onLoad(this.page);
        }
        done(row);
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowDel(row, index, done) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id, 1);
        })
        .then(() => {
          // this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done(row);
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids, 1);

        })
        .then(() => {
          this.data = [];
          this.parentId = 0;
          this.$refs.crud.refreshTable();
          this.$refs.crud.toggleSelection();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    searchReset() {
      this.query = {};
      this.parentId = 0;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.parentId = '';
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    beforeOpen(done, type) {
      if (["add","view"].includes(type)) {
        this.initData(false);
        this.form.type = 1;
      }if (["edit"].includes(type)) {
        this.initData(true,this.form.id);
        this.form.type = 1;
      }
      if (["edit", "view"].includes(type)) {
        getMenu(this.form.id, 1).then(res => {
          // if(res.data.data.parentId == "0"){
          //   res.data.data.parentId = ""
          // }
          this.form = res.data.data;
        });
      }
      done();
    },
    beforeClose(done) {
      this.parentId = "";
      const column = this.findObject(this.option.column, "parentId");
      column.value = "";
      column.addDisabled = false;
      done();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      // console.log(this.page, this.query,'refreshChange');
      this.onLoad(this.page, this.query);
      this.$refs.crud.refreshTable();
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getLazyList(this.parentId, Object.assign({}, params, this.query, { type: 1 })).then(res => {
        this.data = res.data.data;
        this.loading = false;
        this.selectionClear();
      });

    },
    treeLoad(tree, treeNode, resolve) {
      console.log(tree, treeNode, resolve,'tree');
      const parentId = tree.id;
      this.maps.set(parentId, { tree, treeNode, resolve });
      getLazyList(parentId, { type: 1 }).then(res => {
        resolve(res.data.data);
      });
    },
  }
};
</script>

<style>
</style>

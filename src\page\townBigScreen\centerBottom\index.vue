<!--
 * @Author: chenn26
 * @Date: 2023-05-10 16:23:14
 * @LastEditors: chenn26
 * @LastEditTime: 2023-06-05 15:15:52
 * @Description: 中下
-->

<template>
  <div class="town-center-bottom-content">
    <div class="center-bottom-header">
      <img src="/img/screen/title-decorate-center.png" class="left-decorate" alt="" />
      <span class="title">通信合作社</span>
      <img src="/img/screen/title-decorate-center.png" class="right-decorate" alt="" />
    </div>
    <div class="content">
      <div class="content-value">
        <dv-scroll-board :config="config" ref="scrollBoard" @click="openDetail" />
      </div>
    </div>
    <detail v-if="dialogVisible" :detailId="detailId"></detail>
  </div>
</template>
<script>
import "./index.scss";
import {
   getCommunicate, 
  //getCommunicateDetail 
} from "@/api/screen/townScreen";
import detail from './detail'
export default {
  components:{
    detail
  },
  data() {
    return {
      dialogVisible:false,
      config: {
        header: [
          "合作社名称",
          "服务村民数",
          "村集体收入"
        ],
        rowNum: 6,
        headerBGC: "#031e7a",
        headerHeight: 30,
        columnWidth: [100, 220, 250, 250],
        evenRowBGC: '#00135d', //偶数行
        oddRowBGC: '#00176b', //奇数行
        index: true,
        data: [],
        waitTime: 5000,
      },
      timer: null,
      deptId: "",
      currentPage: 1,
      tmp: [],
      communicateData: [],
      detailId:'',
    };
  },
  created() {
    this.getCommunicate();
    this.deptId = this.$store.getters.userInfo.dept_id;
    // this.timer = setInterval(this.getCommunicate, 1000);
  },
  destroyed() {
    this.stopTimer();
  },
  methods: {
    getCommunicate() {
      let params = {
        current: this.currentPage,
        size: 999,
        deptId: this.deptId
      };
      getCommunicate(params).then(res => {
        // if (res.data.data.records.length === 6) {
        //   this.currentPage++;
        // } else {
        //   this.currentPage = this.currentPage;
        //   clearInterval(this.timer);
        // }
        this.tmp = this.tmp.concat(res.data.data.records);
        for (let i = 0; i < this.tmp.length; i++) {
          let editData = [];
          editData[0] = this.tmp[i].villageName;
          // "<span style='cursor:pointer;color:#fff' title='" +
          // this.tmp[i].villageName +
          // "'>" +
          // this.tmp[i].villageName +
          // "</span>";
          editData[1] = this.tmp[i].villagers
          // "<span style='cursor:pointer;color:#fff' title='" +
          // this.tmp[i].villagers +
          // "'>" +
          // this.tmp[i].villagers +
          // "</span>";
          editData[2] = this.tmp[i].income
          // "<span style='cursor:pointer;color:#fff' title='" +
          // this.tmp[i].income +
          // "'>" +
          // this.tmp[i].income +
          // "</span>";
          this.communicateData[i] = editData;
        }
        this.config = {
          header: [
            "合作社名称",
            "服务村民数",
            "村集体收入"
          ],
          rowNum: 6,
          headerBGC: "#031e7a",
          headerHeight: 30,
          columnWidth: [100, 220, 250, 250],
          evenRowBGC: '#00135d', //偶数行
          oddRowBGC: '#00176b', //奇数行
          index: true,
          hoverPause: true,
          data: this.communicateData,
          waitTime: 5000,
        };
      });
    },
    stopTimer() {
      if (this.timer) clearInterval(this.timer);
    },
    openDetail(row) {
      if(this.dialogVisible){
        this.dialogVisible = false;
        this.$nextTick(()=>{
          this.detailId = this.tmp[row.rowIndex].id;
          this.dialogVisible = true;
        })
      }else{
        this.detailId = this.tmp[row.rowIndex].id;
        this.dialogVisible = true;
      }
    },
    handleClose(){
      this.dialogVisible = false;
    }
  },
};
</script>

<style scoped>
::v-deep .dv-scroll-board .row-item {
  height: 40px !important;
  line-height: 40px !important;
  cursor: pointer !important;
}

::v-deep .el-dialog__body{
    padding:0 10px !important;
}
</style>
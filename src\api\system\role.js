import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-system/role/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
export const getPageList = (current, size, params) => {
  return request({
    url: '/api/blade-system/role/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
export const grantTree = (tenantId) => {
  return request({
    url: '/api/blade-system/menu/grant-tree',
    method: 'get',
    params: {
      tenantId,
      type:1
    },
  })
}
export const grantTreeTypeTwo = (tenantId) => {
  return request({
    url: '/api/blade-system/menu/grant-tree',
    method: 'get',
    params: {
      tenantId,
      type:2
    },
  })
}

export const grant = (roleIds, menuIds, dataScopeIds, apiScopeIds) => {
  return request({
    url: '/api/blade-system/role/grant',
    method: 'post',
    data: {
      roleIds,
      menuIds,
      dataScopeIds,
      apiScopeIds
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/blade-system/role/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/blade-system/role/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/blade-system/role/submit',
    method: 'post',
    data: row
  })
}


export const getRole = (roleIds,type) => {
  return request({
    url: '/api/blade-system/menu/role-tree-keys',
    method: 'get',
    params: {
      roleIds,
      type
    }
  })
}

export const getRoleTree = (tenantId) => {
  return request({
    url: '/api/blade-system/role/tree',
    method: 'get',
    params: {
      tenantId,
    }
  })
}

export const getRoleTreeById = (roleId) => {
  return request({
    url: '/api/blade-system/role/tree-by-id',
    method: 'get',
    params: {
      roleId,
    }
  })
}

export const getRoleAlias = () => {
  return request({
    url: '/api/blade-system/role/alias',
    method: 'get',
    params: {},
  });
};

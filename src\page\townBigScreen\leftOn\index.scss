.town-left-on-content {
  position: absolute;
  width: 100%;
  height: 32vh;

  .header {
    position: absolute;
    width: 100%;
    white-space: nowrap;
    top: 2vh;
    left: 50%;
    transform: translate(-50%, 0);

    .left-decorate {
      width: 34%;
    }

    .title {
      letter-spacing: 3px;
      vertical-align: top;
      margin: 0 5%;
      color: #597cff;
      font-size: 2vh;
    }

    .right-decorate {
      width: 34%;
      transform: rotateY(180deg);
    }
  }

  .content {
    position: absolute;
    float: left;
    margin-top: 6vh;
    width: 92%;
    margin-left: 4%;
    color: #fff;

    .select-1 {
      display: inline-block;
      width: 50%;
      height: 3vh;
      font-size: 2vh;
      text-align: center;
      cursor: pointer;
    }

    .select-2 {
      width: 50%;
      font-size: 2vh;
      height: 3vh;
      cursor: pointer;
      display: inline-block;
      text-align: center;
    }
    .select-3 {
      width: 100%;
      font-size: 2vh;
      height: 3vh;
      cursor: pointer;
      display: inline-block;
      text-align: center;
    }

    .title-select-back {
      position: absolute;
      width: 50%;
      height: 3vh;
    }

    .title-select-back1 {
      position: absolute;
      width: 100%;
      height: 3vh;
    }

    .content-value {
      margin-top: 2vh;
      .el-carousel__button {
        display: block;
        opacity: 0.48;
        width: 10px;
        height: 2px;
        background-color: #fff;
        border: none;
        outline: 0;
        padding: 0;
        margin: 0;
        cursor: pointer;
        -webkit-transition: 0.3s;
        transition: 0.3s;
      }
      .content-img {
        width: 100%;
        height: 18vh;
      }

      .content-info {
        position: absolute;
        width: 100%;
        height: 4vh;
        background-color: #2191e1;
        bottom: 0;
        z-index: 10;
        display: flex;
        align-items: center;

        .content-name {
          width: 40%;
          text-align: right;
          font-size: 1.5vh;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .content-post {
          width: 60%;
          text-align: center;
          font-size: 1.4vh;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .content-desc {
      height:17vh;
      width:100%;
      margin-top:1vh;
      position: relative;
      .desc{
        width:98%;
        height:14vh;
        top:0vh;
        left:0.5vw;
        color:#fff;
        font-size:1.5vh;
        position: absolute;
        box-sizing: border-box;
        overflow: auto;
      }
    }
  }
}

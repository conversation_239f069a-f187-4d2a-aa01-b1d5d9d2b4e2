/*
 * @Description: 公共方法
 * @Author: chenz76
 * @Date: 2021-11-10 17:07:15
 * @LastEditors: chenz76
 * @LastEditTime: 2022-01-21 16:01:40
 */
import request from '@/router/axios';
import { getToken } from "@/util/auth";
import website from '@/config/website';
/**
 * @description 统一导出接口
 * @param {string} url
 * @param {object} params url参数
 * @param {object} data 
 * @returns 
 */
export const downloadStatistics = (url,params={},data={}) => {
    data[`${website.tokenHeader}`]=`${getToken()}`;
    return request({
      url: url,
      method: 'get',
      responseType: 'blob',
      data: data,
      params
    })
  }
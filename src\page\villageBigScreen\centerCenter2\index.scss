.center-center2-content1 {
    position: absolute;
    width: 100%;
    height: 100%;
  
    .header {
      position: absolute;
      width: 100%;
      white-space: nowrap;
      top: 1vh;
      left: 50%;
      transform: translate(-50%, 0);
  
      .left-decorate {
        width: 40%;
      }
  
      .title {
        letter-spacing: 3px;
        vertical-align: top;
        margin: 0 5%;
        color: #5bffdc;
        font-size: 2vh;
      }
  
      .right-decorate {
        width: 40%;
        transform: rotateY(180deg);
      }
    }
  
    .content {
      position: relative;
      float: left;
      width: 100%;
      margin-top: 4vh;

      .month-select{
        width:25%;
        height:3vh;
        position: absolute;
        right:10px;
        span{
            color:#fff;
            font-size: 1vh;
        }
      }

      .showPanel{
        width:100%;
        height:15vh;
        position: relative;
        top:4vh;

        .box{
            width:100%;
            height:30%;
            background: blue;
            margin-bottom:5px;
            float: left;
            .box-decorate{
                width:100%;
                height:100%;
                z-index: -1;
                float: left;
            }
            .box-panel{
                float: left;
                width:100%;
                height:100%;
                z-index: 9;
                display: flex;
                justify-content: space-around;
                background-image: url("../../../../public/img/screen/center-li.png");
                background-size: cover;
                background-position: 100% 100%;
                .panel_left{
                    width:50%;
                    height:100%;
                    display: flex;
                    align-items: center;
                    span:nth-child(1){
                        display: block;
                        width:50%;
                        height:100%;
                        color:#fff;
                        font-size:14px;
                        line-height: 5vh;
                    }
                    span:nth-child(2){
                        margin-left:20px;
                        font-size:18px;
                        color:#fff;
                        font-family: bold;
                    }
                    span:nth-child(3){
                        display: block;
                        width:20%;
                        height:100%;
                        color:#fff;
                        font-size:14px;
                        line-height: 5vh;
                        margin-left:5px;
                    }
                }
                .panel_right{
                    width:50%;
                    height:100%;
                    display: flex;
                    align-items: center;
                    span:nth-child(1){
                        display: block;
                        width:50%;
                        height:100%;
                        color:#fff;
                        font-size:14px;
                        line-height: 5vh;
                        font-family: bold;
                    }
                    span:nth-child(2){
                        font-size:18px;
                        color:#eb9658;
                        font-family: bold;
                        padding-left: 20px;
                    }
                    span:nth-child(3){
                        font-size:18px;
                        color:#eb9658;
                        font-family: bold;
                    }
                }
                .panel_desc{
                    width:25%;
                    height:100%;
                    span{
                        box-sizing: border-box;
                        display: block;
                        width:100%;
                        height:100%;
                        color:#fff;
                        font-size:14px;
                        line-height: 5vh;
                        // padding-right:10px;
                    }
                }
                .panel_count{
                    width:75%;
                    height:100%;
                    .count{
                        display: block;
                        width:10%;
                        height:100%;
                        float: left;
                        color:#fff;
                        font-size:14px;
                        line-height: 5vh;
                        padding-left:5%;
                    }
                    .number-decorate{
                        width: 3vh;
                        height: 3vh;
                        margin-top:0.5vh;
                        margin-right:5px;
                        float: left;
                        align-items: center;
                        position:relative;
                        .number{
                            position:absolute;
                            height:100%;
                            width:100%;
                            color:#fff;
                            font-size:2vh;
                            font-weight: bold;
                            left:0;
                            text-align: center;
                        }
                    }
                    .unit{
                        display: block;
                        width:8%;
                        height:100%;
                        float: left;
                        color:#fff;
                        font-size:14px;
                        line-height: 5vh;
                        padding-left:10px;
                    }
                    // span:nth-child(2){
                    //     display: block;
                    //     width:8%;
                    //     height:100%;
                    //     float: left;
                    //     color:#fff;
                    //     font-size:14px;
                    //     line-height: 5vh;
                    //     padding-left:10px;
                    // }
                }
            }
        }
      }
    }
}
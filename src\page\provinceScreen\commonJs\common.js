/*
 * @Description: 省级运营大屏公共方法
 * @Author: chenz76
 * @Date: 2022-03-11 09:47:11
 * @LastEditors: chenz76
 * @LastEditTime: 2022-03-11 09:53:30
 */

/**
 * @description 修改年月为中文的月
 * @param {object} keyList 输入['2022/03']
 * @returns 输出['三月']
 * <AUTHOR>
 */
export const changeDateMonth = (keyList=[]) =>{
    const afterMonthList =  keyList.map((item=>{
       let month = item.substring(item.length-2);
       let afterMonth = "一月";
       switch (month) {
         case '01': afterMonth = "一月"; break;
         case '02': afterMonth = "二月"; break;
         case '03': afterMonth = "三月"; break;
         case '04': afterMonth = "四月"; break;
         case '05': afterMonth = "五月"; break;
         case '06': afterMonth = "六月"; break;
         case '07': afterMonth = "七月"; break;
         case '08': afterMonth = "八月"; break;
         case '09': afterMonth = "九月"; break;
         case '10': afterMonth = "十月"; break;
         case '11': afterMonth = "十一"; break;
         case '12': afterMonth = "十二"; break;
         default:
           break;
       }
       return afterMonth;
     }))
     return afterMonthList;
}
/*
 * @Author: chenn26
 * @Date: 2021-10-14
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-01 14:18:28
 * @Description: 注册审核接口
 */
import request from '@/router/axios'

/**
 * @description 获取注册审核列表
 * @param {number} current
 * @param {number} size
 * @param {object} params
 */
export const getRegList = (current, size,deptId, params) => {
    return request({
        url: '/api/register/user/page',
        method: 'get',
        params: {
            ...params,
            deptId,
            current,
            size
        }
    })
}

/**
 * @description 注册审核详情
 * @param {number} id
 */
export const getRegDetail = id => {
	return request({
		url: '/api/register/user/detail',
		method: 'get',
    params: {
      id
    }
	})
}

/**
 * @description 注册审核更新
 * @param {object} params
 */
export const updateReg = (params) => {
    return request({
        url: '/api/register/user/audit',
        method: 'post',
        data: params
    })
}

/**
 * @description 注册审核信息删除
 * @param {string} ids
 */
export const regRemove = (ids) => {
    return request({
        url: '/api/admin/register/remove',
        method: 'post',
        params:{
          ids: ids
        }
    })
}


export const getAutoState = () => {
	return request({
		url: '/api/user/reviews/get-auto-approve',
		method: 'get'
	})
}


export const setAutoAudit = (isOpen) => {
  return request({
      url: '/api/user/reviews/set-auto-approve',
      method: 'post',
      params: {
        isOpen
      }
  })
}

.town-right-center-content {
    position: absolute;
    width: 100%;
    height: 35vh;
    .header {
        position: absolute;
        width: 100%;
        white-space: nowrap;
        top: 1vh;
        left: 50%;
        transform: translate(-50%, 0);
        .left-decorate {
            width: 34%;
        }
        .title {
            letter-spacing: 3px;
            vertical-align: top;
            margin: 0 5%;
            color: #597cff;
            font-size: 2vh;
        }
        .right-decorate {
            width: 34%;
            transform: rotateY(180deg);
        }
    }
    .content {
        float: left;
        width: 100%;
        margin-top: 7vh;
        position: relative;

        .content-value{
            width:50%;
            height:15vh;
            position: absolute;
            top:-1vh;
        }

        .content-desc{
            width:49%;
            height:18vh;
            position:absolute;
            top:-3vh;
            right:0;
            .desc{
                padding:10px;
                box-sizing: border-box;
                color:#fff;
                height:16vh;
                width: 100%;
                top:-1vh;
                font-size: 1.5vh;
                position: absolute;
                overflow: auto;
            }
        }
    }
}

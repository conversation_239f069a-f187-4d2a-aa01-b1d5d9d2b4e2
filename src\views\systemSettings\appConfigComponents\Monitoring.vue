<!--
 * @Date: 2025-07-31 21:50:00
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-31 21:50:00
 * @Description: 监控预警组件
 * @FilePath: \src\views\system\appConfigComponents\Monitoring.vue
-->
<template>
  <div class="monitoring-container">
    <div class="monitoring-header">
      <div class="monitoring-title">监控预警</div>
    </div>
    <div class="monitoring-content">
      <div class="video-item">
        <div class="video-thumbnail">
          <img src="@/assets/appConfig/swiper-def.png" alt="视频缩略图" />
          <div class="play-button">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </div>
        </div>
        <div class="video-info">
          <div class="video-title">2025-07-03 10:00:25</div>
          <div class="video-subtitle">罗联溪乐侧泊岸</div>
          <div class="video-tag">防溺水预警</div>
        </div>
        <div class="video-actions">
          <div class="arrow-icon">></div>
        </div>
      </div>
      <div class="video-item">
        <div class="video-thumbnail">
          <img src="@/assets/appConfig/swiper-def.png" alt="视频缩略图" />
          <div class="play-button">
            <svg viewBox="0 0 24 24" fill="currentColor">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </div>
        </div>
        <div class="video-info">
          <div class="video-title">2025-07-03 10:00:25</div>
          <div class="video-subtitle">罗联溪乐侧泊岸</div>
          <div class="video-tag">防溺水预警</div>
        </div>
        <div class="video-actions">
          <div class="arrow-icon">></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Monitoring'
}
</script>

<style scoped>
/* ================== 监控预警样式 ================== */
.monitoring-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  padding: 12px;
  box-sizing: border-box;
}

.monitoring-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.monitoring-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.monitoring-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.video-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px;
  border-radius: 6px;
  background: #fafafa;
  transition: background 0.2s ease;
}

.video-item:hover {
  background: #f0f8ff;
}

.video-thumbnail {
  position: relative;
  width: 80px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  flex-shrink: 0;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.play-button:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: translate(-50%, -50%) scale(1.1);
}

.play-button svg {
  width: 12px;
  height: 12px;
  margin-left: 1px;
}

.video-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.video-title {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  line-height: 1.2;
}

.video-subtitle {
  font-size: 12px;
  color: #666;
  line-height: 1.2;
}

.video-tag {
  font-size: 11px;
  color: #999;
  line-height: 1.2;
}

.video-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.arrow-icon {
  font-size: 25px;
  color: #d5cdcd;
  cursor: pointer;
  transition: color 0.2s ease;
}

.arrow-icon:hover {
  color: #409eff;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .monitoring-container {
    padding: 8px;
  }

  .monitoring-title {
    font-size: 14px;
  }

  .video-item {
    padding: 6px;
  }

  .video-thumbnail {
    width: 70px;
    height: 50px;
  }

  .video-title {
    font-size: 12px;
  }

  .video-subtitle {
    font-size: 11px;
  }

  .video-tag {
    font-size: 10px;
  }

  .arrow-icon {
    font-size: 25px;
  }
}
</style>

<template>
  <el-form class="login-form"
           status-icon
           v-loading="formLoading"
           :rules="loginRules"
           ref="loginForm"
           :model="loginForm"
           label-width="0">
    <el-form-item v-if="tenantMode" prop="tenantId">
      <!-- <el-input
        @keyup.enter="handleLogin"
        v-model="loginForm.tenantId"
        auto-complete="off"
        :placeholder="$t('login.tenantId')"
      >
        <template #prefix>
          <i class="icon-quanxian" />
        </template>
      </el-input> -->
      <!-- <el-cascader style="width: 100%" :props="cascaderProps" clearable :options="countryList" v-model="loginForm.tenantId" :placeholder="$t('login.tenantId')">

      </el-cascader> -->
    </el-form-item>
    <el-form-item prop="phone">
      <el-input size="small"
                @keyup.enter.native="handleLogin"
                v-model="loginForm.phone"
                auto-complete="off"
                :placeholder="$t('login.phone')">
        <i slot="prefix" @click="handleClick"
           class="icon-shouji"/>
      </el-input>
    </el-form-item>
    <el-form-item prop="smsCode">
      <el-row :span="24">
        <el-col :span="16">
          <el-input
            @keyup.enter="handleLogin"
            v-model="loginForm.smsCode"
            auto-complete="off"
            :placeholder="$t('login.code')"
          >
            <template #prefix>
              <i class="icon-yanzhengma" />
            </template>
          </el-input>
        </el-col>
        <el-col :span="8">
          <div class="login-code">
            <div @click="handleSend" class="login-code-phone">
              {{ msgText }}
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form-item>
    <el-form-item>
      <el-button size="small"
                 type="primary"
                 @click.native.prevent="handleLogin"
                 class="btn-submit">{{$t('login.submit')}}</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { isvalidatemobile } from "@/util/validate";
import { mapGetters } from "vuex";
import {info} from "@/api/system/tenant";
import {sendLoginSms} from "@/api/user";
import {getTopUrl} from "@/util/util";
import {encrypt} from "@/util/sm2";
export default {
  name: "codelogin",
  data() {
    const validatePhone = (rule, value, callback) => {
      if (isvalidatemobile(value)[0]) {
        callback(new Error(isvalidatemobile(value)[1]));
      } else {
        callback();
      }
    };
    return {
      tenantMode: this.website.tenantMode,
      msgText: '',
      msgTime: '',
      loginForm: {
        tenantId: '000000',
        phone: '',
        smsCode: '',
        smsKey: '',
      },
      loginRules: {
        phone: [{ required: true, trigger:['blur','change'], validator: validatePhone }],
        smsCode: [{ required: true, trigger: ['blur','change'],message:"验证码不能为空" }],
      },

      // clickCount:0,
      // timer: null,
      // countryList:[],
      // cascaderProps:{
      //   value:'tenantId',
      //   label:'regionName',
      //   children:'children',
      //   emitPath:false,
      //     // leaf:
      // },
      formLoading:false,
      // showSuccess:false,
    };
  },
  created() {
    this.getTenant();
    this.getMsg();
  },
  mounted() {
    // this.initDate()
  },
  computed: {
    ...mapGetters(["tagWel"]),
    config() {
      return {
        MSGINIT: this.$t("login.msgText"),
        MSGSCUCCESS: this.$t("login.msgSuccess"),
        MSGTIME: 60
      };
    }
  },
  props: [],
  methods: {
    initDate(){
        // getDeptTreeNo().then(res => {
        //   this.countryList = res.data.data;
        //   this.formLoading = false;
        // }).finally(() => {
        //   this.formLoading = false;
        // });
      },
    handleClick() {
        // // 清除之前的定时器
        // clearTimeout(this.timer)
        // if(this.showSuccess) return
        // // 增加点击计数
        // this.clickCount++
        // // 设置定时器，在一定时间后重置计数
        // this.timer = setTimeout(() => {
        //   this.clickCount = 0
        // }, 1000) // 1秒内不继续点击则重置

        // // 检查是否达到10次
        // if (this.clickCount >= 5) {
        //   this.showSuccess = true
        //   this.clickCount = 0
        //   this.countryList.unshift({
        //     regionName: '福建联通',
        //     tenantId: '000000',
        //     leaf:true,
        //   })
        // }
    },
    handleSend() {
      this.$refs.loginForm.validateField('phone',valid => {
        // console.log(valid);
        if (!valid) {
          sendLoginSms(this.loginForm.tenantId, encrypt(this.loginForm.phone)).then(res => {
            const data = res.data;
            console.log(data);
            if (data.success) {
              this.loginForm.smsKey = data.data;
              this.msgText = this.msgTime + this.config.MSGSCUCCESS;
              const time = setInterval(() => {
                this.msgTime--;
                this.msgText = this.msgTime + this.config.MSGSCUCCESS;
                if (this.msgTime === 0) {
                  this.msgTime = this.config.MSGTIME;
                  this.msgText = this.config.MSGINIT;
                  clearInterval(time);
                }
              }, 1000);
              this.$message.success(data.msg);
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    },
    handleLogin() {
      if(this.loginForm.smsKey == ""){
        this.$message.error("请先发送验证码");
        return
      }
      this.$refs.loginForm.validate(valid => {
        console.log(valid);
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: '登录中,请稍后',
            background: 'rgba(0, 0, 0, 0.7)',
          });
          this.$store
            .dispatch('LoginByPhone', this.loginForm)
            .then(() => {
              loading.close();
              this.$router.push({path: this.tagWel.value});
            })
            .catch(err => {
              console.log(err);
              loading.close();
            });
        }
      });
    },
    getMsg() {
      this.msgText = this.config.MSGINIT;
      this.msgTime = this.config.MSGTIME;
    },
    getTenant() {
      let domain = getTopUrl();
      // 临时指定域名，方便测试
      //domain = "https://bladex.cn";
      info(domain).then(res => {
        const data = res.data;
        if (data.success && data.data.tenantId) {
          this.tenantMode = false;
          this.loginForm.tenantId = data.data.tenantId;
          this.$parent.$refs.login.style.backgroundImage = `url(${data.data.backgroundUrl})`;
        }
      });
    },
  }
};
</script>

<style>
.msg-text {
  display: block;
  width: 60px;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
}
.msg-text.display {
  color: #ccc;
}
</style>

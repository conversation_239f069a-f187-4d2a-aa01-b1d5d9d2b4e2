import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/serviceActive/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/serviceActive/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/serviceActive/save',
    method: 'post',
    data: row,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  })
}

export const update = (row) => {
  return request({
    url: '/api/serviceActive/update',
    method: 'post',
    data: row,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  })
}

export const platformOpen = (data) => {
  return request({
    url: '/api/serviceActive/platformOpen',
    method: 'post',
    data: data,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  })
}

export const remove = ids => {
  return request({
    url: '/api/serviceActive/remove',
    method: 'post',
    params: {
      ids,
    },
  });
};

export const validCode = (phone) => {
  return request({
    url: '/api/blade-system/user/sms/validCode',
    method: 'post',
    params: {
      phone,
    },
  })
}

export const getDispositions = (id) => {
  return request({
    url: '/api/serviceActive/getDispositions',
    method: 'get',
    params: {
      id
    }
  })
}

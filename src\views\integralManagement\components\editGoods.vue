<!--
 * @Description:积分商城-新增、编辑
 * @Author: wangyy553
 * @Date: 2021-12-17 10:11:17
 * @LastEditors: wangyy553
 * @LastEditTime: 2022-01-19 15:04:56
-->
<template>
  <el-dialog :fullscreen="isFullscreen" :visible.sync="dialogVisible" append-to-body="true"
    :close-on-click-modal="false" top="100px" width="60%" @close="close()">
    <div slot="title" class="header">
      <div class="avue-crud__dialog__header">
        <span class="el-dialog__title">{{ title }}</span>
        <div class="avue-crud__dialog__menu">
          <i @click="isFullScreen" class="el-dialog__close el-icon-full-screen"></i>
        </div>
      </div>
      <button type="button" aria-label="Close" class="el-dialog__headerbtn">
        <i class="el-dialog__close el-icon el-icon-close"></i>
      </button>
    </div>
    <avue-form ref="form" v-model="form" :option="option">
      <template slot="attachId">
        <div class="btn-group-container">
          <ImgCutter ref="imgCutterModal" :cross-origin="true" :lock-scroll="true" :box-width="800" :box-height="500"
            :cut-width="250" :cut-height="500" :size-change="false" :move-able="true" :tool="false" @cutDown="cutDown"
            @onChooseImg="onChooseImg">
            <el-button slot="open" type="primary">选择图片</el-button>
            <el-button ref="chooseBtn" slot="choose">选择</el-button>
            <el-button ref="cancelBtn" slot="cancel">取消</el-button>
            <el-button style="margin-left: 10px" slot="confirm" type="primary">确定</el-button>
          </ImgCutter>
          <el-button v-if="bannerUrl" style="margin-left: 10px" type="danger" @click="delUrl">删除</el-button>
        </div>
        <div class="tip-container">
          只能上传jpg、jpeg、gif、png、bmp图片格式，且不超过3M
        </div>
        <div v-if="bannerUrl">
          <el-image class="img-tiny" fit="contain" :z-index="10000" :src="bannerUrl" :preview-src-list="srcList">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </div>
      </template>
      <template slot="stock">
        <avue-input-number v-model="form.stock" max="99999999" min="0" precision="0"
          :disabled="detail.status == 2" placeholder="请输入 库存"></avue-input-number>
      </template>
      <template slot="usageRule">
        <avue-ueditor v-model.trim="form.usageRule" :options="editorOption"></avue-ueditor>
        <div class="tip-container">
          使用规则可填写取货地址及联系人，商品介绍等。
        </div>
      </template>

      <template slot="menuForm">
        <el-button type="primary" icon="el-icon-circle-plus-outline" @click="handelSubmit">提交</el-button>
        <el-button icon="el-icon-circle-close" @click="close()">取 消</el-button>
      </template>
    </avue-form>
  </el-dialog>
</template>

<script>
import ImgCutter from "@/components/third-components/ImgCutter.vue";
import * as api from "@/api/integralManagement/mall";
import { mapGetters } from "vuex";

export default {
  components: {
    ImgCutter,
  },
  props: ["visible", "detail", "type"],
  data () {
    return {
      isFullscreen: false,
      // title: this.type == "edit" ? "编辑" : "新建",
      form: {},

      option: {
        column: [
          {
            type: "input",
            label: "商品名称",
            prop: "name",
            minlength: 2,
            maxlength: 30,
            rules: [{ whitespace: true, required: true, message: "请输入商品名称", trigger: ['blur', 'change'] }],
          },
          {
            type: "tree",
            label: "商品分类",
            prop: "typeId",
            filter: false,
            dicUrl:
              "/api/blade-system/dict/dictionary?code=integral_goods_type",
            dicMethod: "get",
            props: {
              //对应select的属性
              label: "dictValue",
              value: "dictKey",
            },
            rules: [{ required: true, message: "请选择商品分类", trigger: ['blur', 'change'] }],
          },
          {
            type: "number",
            label: "商品单价（积分）",
            prop: "price",
            max: 999999999,
            min: 1,
            precision: 0, //精度
            rules: [{ required: true, message: "请输入商品单价（积分）", trigger: ['blur', 'change'] }],
          },
          {
            type: "number",
            label: "市场参考价（￥）",
            prop: "marketPrice",
            max: 99999999,
            min: 0,
            precision: 0, //精度
            rules: [{ required: true, message: "请输入市场参考价（￥）", trigger: ['blur', 'change'] }],
          },
          {
            type: "tree",
            label: "商品单位",
            prop: "unit",
            dicUrl:
              "/api/blade-system/dict/dictionary?code=integral_goods_unit",
            dicMethod: "get",
            props: {
              //对应select的属性
              label: "dictValue",
              value: "dictKey",
            },
            rules: [{ required: true, message: "请选择商品单位", trigger: ['blur', 'change'] }],
          },
          {
            type: "number",
            label: "库存",
            prop: "stock",
            formslot: true,
            slot: true,
            rules: [{ required: true, message: "请输入库存", trigger: ['blur', 'change'] }],
          },
          {
            type: "number",
            label: "每人限购数",
            prop: "quota",
            max: 9999,
            min: 1,
            precision: 0, //精度
            rules: [{ required: true, message: "请输入每人限购数", trigger: ['blur', 'change'] }],
          },
          {
            type: "tree",
            label: "商品属性",
            prop: "labelIds",
            multiple: true,
            filter: false,
            dicUrl:
              "/api/blade-system/dict/dictionary?code=integral_goods_label",
            dicMethod: "get",
            props: {
              //对应select的属性
              label: "dictValue",
              value: "dictKey",
            },
            rules: [{ required: false, type: "array", message: "请选择商品属性", trigger: ['blur', 'change'] }],
          },
          {
            label: "商品图片",
            prop: "attachId",
            formslot: true,
            slot: true,
            span: 24,
            rules: [{ required: true, message: "请选择商品图片", trigger: ['blur', 'change'] }],
          },
          {
            label: "商品上架组织范围",
            prop: "deptIds",
            // formslot: true,
            // slot: true,
            filter: true,
            type: "tree",
            checkStrictly: true,
            multiple: true,
            dicUrl: "/api/blade-system/dept/tree",
            dicMethod: "get",
            dicQuery: {
              tenantId: ''
            },
            props: {
              label: "title",
              value: "value",
            },
            expandOnClickNode: false,
            span: 24,
            rules: [{ required: true, message: "请选择商品上架组织范围", type: "array", trigger: ['blur', 'change'] }],
          },
          {
            label: "使用规则",
            prop: "usageRule",
            formslot: true,
            slot: true,
            span: 24,
            maxlength: 65535,
            rules: [{ whitespace: true, required: true, message: "请输入使用规则", trigger: ['blur', 'change'] }],
          },
          {
            label: "温馨提示",
            prop: "reminder",
            type: "textarea",
            span: 24,
            value: "数量有限，先到先得，兑完为止，虚拟权益一旦兑换不支持退换。",
            maxlength: 1000,
            showWordLimit: true,
            rules: [{ whitespace: true, required: true, message: "请输入温馨提示", trigger: ['blur', 'change'] }],
          },
        ],
        labelPosition: "right",
        labelSuffix: "：",
        labelWidth: 150,
        gutter: 0,
        menuBtn: true,
        submitBtn: false,
        // submitText: "确定",
        emptyBtn: false,
        // cancelText: "取消",
        menuPosition: "center",
        tabs: false,
        detail: false,
      },
      editorOption: {
        action: "/api/blade-resource/oss/endpoint/put-file-attach",
        customConfig: {
          //去掉todo,code
          excludeMenus: ["code", "todo", "fontName"],
          uploadImgMaxLength: 1, //限制单次图片上传张数
        }, //wangEditor编辑的配置
        props: {
          res: "data",
          url: "link",
        },
      }, //编辑器配置
      bannerUrl: "", //裁剪图片路径
      srcList: [], //预览裁剪图片路径
      // sizeFlage: true,
    };
  },
  watch: {
    /**
     * @description: 监听detail,编辑时初始化表单
     * @param {object} val 表单内容
     */
    detail (val) {
      console.log('385', this.isFullscreen);

      if (this.type === "edit") {
        this.form = {
          attachId: null,
          deptIds: [],
          id: 0,
          labelIds: [],
          marketPrice: null,
          name: "",
          price: null,
          quota: null,
          reminder: "",
          stock: 0,
          typeId: null,
          unit: null,
          usageRule: null,
        };
        Object.keys(this.form).forEach((key) => {
          this.form[key] = val[key];
        });
        this.bannerUrl = val.attachLink;
        this.srcList = [val.attachLink];
        setTimeout(() => {
          this.$refs.form.clearValidate();
        }, 100);
      } else {
        this.form.reminder = "数量有限，先到先得，兑完为止，虚拟权益一旦兑换不支持退换。";
      }
    },

    /**
     * @description: 监听并验证图片
     */
    "form.attachId": {
      handler () {
        this.$refs.form.validateField("attachId");
      },
    },
    /**
     * @description: 监听并验证使用规则
     * @param {*}
     */
    "form.usageRule": {
      handler () {
        this.$refs.form.validateField("usageRule");
      },
    },
  },
  computed: {
    ...mapGetters(["userInfo"]),
    dialogVisible: {
      get () {
        return this.visible;
      },
      set (val) {
        this.$emit("update:visible", val);
      },
    },
    isDisabled () {
      if (this.detail.status == 2) {
        return true;
      } else {
        return false;
      }
    },
    title () {
      return this.type === "edit" ? "编辑" : "新增";
    },
  },
  created () {
    this.option.column[9].dicQuery.tenantId = this.userInfo.tenant_id
  },
  methods: {
    // onPrintImg() {
    //   if (!this.sizeFlage) {
    //     this.$refs.imgCutterModal.visible = false;
    //     this.$refs.imgCutterModal.clearAll();
    //   }
    //   this.sizeFlage = true;
    // },
    /**
     * @description: 选择裁剪图片
     * @param {object} file 图片文件
     */
    onChooseImg (file) {
      // const isLt2M = file.size / 1024 / 1024 < 3;
      const isJPG =
        file.type === "image/png" ||
          file.type === "image/jpeg" ||
          file.type === "image/gif" ||
          file.type === "image/jpg" ||
          file.type === "image/bmp"
          ? file.type
          : false;
      // if (!isLt2M) {
      //   this.$message.error(`上传图片大小不能超过3M!`);
      //   // this.sizeFlage = false;
      //   return;
      // }
      if (!isJPG) {
        this.$message.error(`请上传图片！`);
        this.$refs.cancelBtn.$el.click();
        return;
      }
    },
    cutDown (fileName) {
      var imgGs = fileName.dataURL.split(";")[0].split("/")[1];
      var file = this.dataURLtoBlob(fileName.dataURL, "裁剪图片." + imgGs);
      // console.log(file, "file"); //裁剪后file文件
      var formData = new FormData();
      formData.append("file", file);
      api.upload(formData).then((res) => {
        if (res.data.code === 200) {
          this.form.attachId = res.data.data.attachId;
          this.bannerUrl = res.data.data.link;
          this.srcList = [res.data.data.link];
        }
      });
    },
    dataURLtoBlob (dataurl, name) {
      var arr = dataurl.split(","),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n);
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }
      return new File([u8arr], name, { type: mime });
    },
    delUrl () {
      this.bannerUrl = "";
      this.form.attachId = "";
      this.srcList = [];
      this.$nextTick(() => {
        this.$forceUpdate();
      });
    },
    handelSubmit () {
      this.$refs.form.validate((valid, done) => {
        if (valid) {
          var obj = Object.assign({}, this.form);
          delete obj.$labelIds;
          delete obj.$typeId;
          delete obj.$unit;
          if (this.type === "add") {
            api
              .add(obj)
              .then((res) => {
                if (res.data.success) {
                  this.$message({
                    type: "success",
                    message: res.data.msg,
                  });
                } else {
                  this.$message({
                    type: "warning",
                    message: res.data.msg,
                  });
                }
                done();
                this.close();
                this.$emit("updateTable");
              })
              .catch(() => {
                done();
              });
          } else {
            api
              .edit(obj)
              .then((res) => {
                if (res.data.success) {
                  this.$message({
                    type: "success",
                    message: res.data.msg,
                  });
                } else {
                  this.$message({
                    type: "warning",
                    message: res.data.msg,
                  });
                }
                done();
                this.close();
                this.$emit("updateTable");
              })
              .catch(() => {
                done();
              });
          }
        }
      });
    },
    close () {
      this.dialogVisible = false;
      this.$refs.form.resetForm();
      this.bannerUrl = "";
      this.srcList = [];
    },
    isFullScreen () {
      this.isFullscreen = !this.isFullscreen;
    },
  },
};
</script>

<style scoped lang='scss'>
.btn-group-container {
  display: flex;
}

.taglist-container {
  // display: flex;
  border: 1px dashed #e3e3e3;
  line-height: 40px;
  border-radius: 5px;
  padding: 5px;
}

.tag-container {
  margin: 0 5px;
}

.select-container {
  margin-top: 10px;
}

.tip-container {
  font-size: 12px;
  color: #606266;
}
</style>

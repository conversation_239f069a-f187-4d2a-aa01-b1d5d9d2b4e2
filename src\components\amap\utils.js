/*
 * @Date: 2025-02-10 10:21:53
 * @LastEditors: linqh21
 * @LastEditTime: 2025-02-10 10:23:32
 * @Description:
 * @FilePath: \src\components\amap\utils.js
 */
/**
 * 判断两个线段是否相交
 * @param {Array} p1 线段1的起点 [x, y]
 * @param {Array} p2 线段1的终点 [x, y]
 * @param {Array} q1 线段2的起点 [x, y]
 * @param {Array} q2 线段2的终点 [x, y]
 * @returns {boolean} 是否相交
 */
export function isSegmentIntersect(p1, p2, q1, q2) {
  // 计算叉积
  function cross(a, b) {
      return a[0] * b[1] - a[1] * b[0];
  }

  // 计算向量
  function vector(from, to) {
      return [to[0] - from[0], to[1] - from[1]];
  }

  // 计算方向
  function direction(a, b, c) {
      return cross(vector(a, b), vector(a, c));
  }

  // 判断点是否在线段上
  function onSegment(a, b, c) {
      return (
          Math.min(a[0], b[0]) <= c[0] && c[0] <= Math.max(a[0], b[0]) &&
          Math.min(a[1], b[1]) <= c[1] && c[1] <= Math.max(a[1], b[1])
      );
  }

  const d1 = direction(q1, q2, p1);
  const d2 = direction(q1, q2, p2);
  const d3 = direction(p1, p2, q1);
  const d4 = direction(p1, p2, q2);

  // 判断是否相交
  if (((d1 > 0 && d2 < 0) || (d1 < 0 && d2 > 0)) &&
      ((d3 > 0 && d4 < 0) || (d3 < 0 && d4 > 0))) {
      return true;
  }

  // 处理特殊情况（共线）
  if (d1 === 0 && onSegment(q1, q2, p1)) return true;
  if (d2 === 0 && onSegment(q1, q2, p2)) return true;
  if (d3 === 0 && onSegment(p1, p2, q1)) return true;
  if (d4 === 0 && onSegment(p1, p2, q2)) return true;

  return false;
}

/**
* 判断多边形是否存在线段交叉
* @param {Array} polygon 多边形的顶点数组 [[x1, y1], [x2, y2], ...]
* @returns {boolean} 是否存在线段交叉
*/
export function isPolygonSelfIntersecting(polygon) {
  const n = polygon.length;

  // 遍历所有线段对
  for (let i = 0; i < n; i++) {
      for (let j = i + 1; j < n; j++) {
          const p1 = polygon[i];
          const p2 = polygon[(i + 1) % n]; // 下一个点，闭合多边形
          const q1 = polygon[j];
          const q2 = polygon[(j + 1) % n]; // 下一个点，闭合多边形

          // 排除相邻线段（相邻线段共享一个顶点，不会相交）
          if (i === j || (i + 1) % n === j || i === (j + 1) % n) {
              continue;
          }

          // 检测是否相交
          if (isSegmentIntersect(p1, p2, q1, q2)) {
              return true;
          }
      }
  }

  return false;
}

// // 测试数据
// const polygon = [
//   [117.401083, 26.338394],
//   [117.401362, 26.335029],
//   [117.406169, 26.335337],
//   [117.398723, 26.336798]
// ];

// // 判断是否存在线段交叉
// console.log(isPolygonSelfIntersecting(polygon)); // 输出 true 或 false

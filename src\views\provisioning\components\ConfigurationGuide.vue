<template>
  <div class="step-container">
    <div class="step-content">
      <div class="success-header">
        <div class="success-icon">
          <img width="80px" src="@/assets/provisioning/finish.png">
        </div>
        <h2 class="success-title">开通成功</h2>
        <p class="success-desc">您已完成服务开通流程，使用管理员账号及对应验证码即可登录平台<span v-if="detailInfo.length>0">，部分功能需手动配置，具体如下：</span><span v-else>。</span></p>
      </div>

      <div class="config-list" v-if="detailInfo.length>0">
        <div
          v-for="(item, index) in detailInfo"
          :key="index"
          class="config-item">
          <div class="config-header">
            <span class="config-number">{{ index + 1 }}.</span>
            <span class="config-name">{{ item.title || '模块名称' }}</span>
          </div>

          <div class="config-details">
            <div class="config-row">
              <span class="config-label">涉及功能：</span>
              <span class="config-value">{{ item.linkModule || '视频监管、监控规则配置、网管管理' }}</span>
            </div>

            <div class="config-row">
              <span class="config-label">配置流程：</span>
              <div class="config-steps">
                <template v-for="(items,index) in item.content">
                  <div :key="index"  class="step-item">
                    <span class="step-text">{{items.title}}：{{items.value}}</span>
                  </div>
                </template>
                
                <!-- <div class="step-item">
                  <span class="step-text">步骤二：收集设备信息，包括：设备名称、设备描述、设备地址、设备地址社区详述、经度、纬度、设备编码ID。</span>
                </div>
                <div class="step-item">
                  <span class="step-text">步骤三：联系技术支撑人员，进行设备的配置。</span>
                </div> -->
              </div>
            </div>

            <div class="config-row">
              <span class="config-label">相关文件：</span>
              <span class="config-value link-text" @click="downloadTemplate(item.templateLink,item.templateName)">{{ item.templateName || '' }}</span>
            </div>

            <div class="config-row">
              <span class="config-label">联系人：</span>
              <span class="config-value">{{ item.contactInfo || '' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="actions-divider"></div>

    <div class="action-buttons">
      <div></div>
      <div class="step-buttons">
        <el-button v-if="isViewMode" @click="$emit('cancel')">{{ isViewMode ? '关闭' : '取消' }}</el-button>
        <el-button v-if="isViewMode" @click="prev">上一步</el-button>
        <el-button v-if="!isViewMode" type="primary" @click="$emit('cancel')">完成</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getDispositions,} from "@/api/provisioning/index";
import { downloadFileBlob } from '@/util/util';
export default {
  props: {
    isViewMode: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      tenantForm: {},
      detailInfo: []
    }
  },
  created() {
    if (this.formData) {
      this.tenantForm = { ...this.formData };
      this.getData(this.tenantForm.id)
    }
  },
  methods: {
    getData(id) {
      getDispositions(id).then(res=>{
        console.log(res.data.data)
        this.detailInfo = res.data.data
      })
    },
    prev(){
      this.$emit('prev', this.tenantForm,"configurationGuide");
    },
    downloadTemplate(url,name) {
      downloadFileBlob(url,name)
    }
  }
};
</script>

<style lang="scss" scoped>
.form-title {
  display: flex;
  align-items: center;

  .title-bar {
    width: 4px;
    height: 20px;
    background-color: #409eff;
    margin-right: 8px;
  }

  span {
    font-size: 16px;
    font-weight: 500;
  }
}

.title-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 15px 0 20px 0;
}

.actions-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 20px -20px 20px -20px;
}

.action-buttons {
  display: flex;
  justify-content: end;
  align-items: center;
  width: 100%;

  .step-buttons {
    display: flex;
    gap: 10px;

    .el-button {
      min-width: 80px;
    }
  }
}
.step-content {
  padding: 20px;
  height: 540px;
  overflow: hidden;

  .success-header {
    text-align: center;
    margin-bottom: 40px;

    .success-icon {
      width: 80px;
      height: 80px;
      // background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 10px;
      position: relative;
    }

    .success-title {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 16px 0;
    }

    .success-desc {
      font-size: 14px;
      color: #666;
      line-height: 1.6;
      // max-width: 600px;
      margin: 0 auto;
    }
  }

  .config-list {
    max-width: 800px;
    margin: 0 auto;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    background: #fff;
    height: 310px;
    overflow: hidden;
    overflow-y: auto;
    padding: 20px;
    padding-top: 10px;

    .config-item {
      background: transparent;
      border: none;
      border-radius: 0;
      padding: 20px 0;
      margin-bottom: 15px;
      border-bottom: 1px solid #f0f0f0;
      padding-top: 10px;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
      }

      .config-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .config-number {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-right: 8px;
        }

        .config-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }
      }

      .config-details {
        .config-row {
          display: flex;
          margin-bottom: 16px;
          align-items: flex-start;
          padding-left: 10px;
          gap: 15px;

          &:last-child {
            margin-bottom: 0;
          }

          .config-label {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            min-width: 80px;
            flex-shrink: 0;
            text-align: right;
          }

          .config-value {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            flex: 1;

            &.link-text {
              color: #4A90E2;
              cursor: pointer;

              &:hover {
                text-decoration: underline;
              }
            }
          }

          .config-steps {
            flex: 1;

            .step-item {
              margin-bottom: 8px;

              &:last-child {
                margin-bottom: 0;
              }

              .step-text {
                font-size: 14px;
                color: #666;
                line-height: 1.5;
              }
            }
          }
        }
      }
    }
  }
}
</style>





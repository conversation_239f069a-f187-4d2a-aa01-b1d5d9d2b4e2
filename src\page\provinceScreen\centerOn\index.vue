<!--
 * @Description: 中上
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2022-07-09 14:41:31
-->
<template>
  <div class="province-center-on-content">
    <div class="header">
      <span class="title">各地市部署情况</span>
      <img src="/img/privinceScreen/titleDirection.png" class="title-direction" alt="" />
      <img src="/img/privinceScreen/line.png" class="left-on-line" alt="" />
    </div>
    <Chart :cdata="cdata" />
    <div class="marquee">
      <MarqueeLeft :sendVal="newItems" />
    </div>
  </div>
</template>

<script>
import Chart from "./chart.vue";
import {
  getMapOverview,
  getMapOverviewUnder,
} from "@/api/privinceScreen/privinceScreen";
import "./index.scss";

import MarqueeLeft from "./components/marquee";
export default {
  data() {
    return {
      newItems: [
        {
          name: "",
          detail:
            "福建联通八闽数村，平台部署量达***个，平台用户数达***人，激活人数达***人，活跃人数达***人。",
        },
      ],
      cdata: [
        {
          // 名字需要与 “common/map/fujian.js” 地图数据文件里面定义的一一对应，不能是 “福州” 或者 “闽” 之类的缩写
          name: "福州市",
          value: 10,
          elseData: {
            // 这里放置地图 tooltip 里想显示的数据
          },
        },
        {
          name: "厦门市",
          value: 9,
        },
        {
          name: "漳州市",
          value: 8,
        },
        {
          name: "泉州市",
          value: 7,
        },
        {
          name: "三明市",
          value: 6,
        },
        {
          name: "莆田市",
          value: 5,
        },
        {
          name: "南平市",
          value: 4,
        },
        {
          name: "龙岩市",
          value: 3,
        },
        {
          name: "宁德市",
          value: 2,
        },
      ],
    };
  },
  components: {
    Chart,
    MarqueeLeft,
  },

  mounted() {
    this.getMapOverview();
    this.getMapOverviewUnder();
  },
  methods: {
    async getMapOverview() {
      const result = await new Promise((resolve) => {
        getMapOverview().then((res) => {
          if (res && res.data.code === 200) {
            resolve(res.data.data);
          }
        });
      });
      this.$nextTick(() => {
        this.cdata = this.convertMapData(result);
      });
    },
    //获取地图滚动数据
    async getMapOverviewUnder() {
      const result = await new Promise((resolve) => {
        getMapOverviewUnder().then((res) => {
          if (res && res.data.code === 200) {
            resolve(res.data.data);
          }
        });
      });
      this.$nextTick(() => {
        this.newItems = this.convertMarqueeLeftData(result);
      });
    },
    //转换地图数据
    convertMapData(data) {
      let resultData = [];
      for (var i = 0; i < data.length; i++) {
        resultData.push({
          name: data[i].cityDimension,
          value: data[i].activeNum,
          elseData: data[i],
        });
      }
      return resultData;
    },
    //转换滚动数据
    convertMarqueeLeftData(data) {
      let resultData = [];
      for (var i = 0; i < data.length; i++) {
        resultData.push({
          name: "",
          detail: `福建联通八闽数村，平台部署量达${data[i].deployNum}个，平台用户数达${data[i].userNum}人，激活人数达${data[i].activeNum}人，活跃人数达${data[i].enlivenNum}人。`,
        });
      }
      return resultData;
    },
  },
};
</script>
<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :permission="permissionList" :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate" @row-save="rowSave" @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger" size="small" icon="el-icon-delete" plain @click="handleDelete">删 除
        </el-button>
      </template>
      <template slot="webMenuIdForm">
        <el-tree :data="menuWebIdTree" v-loading="menuWebIdTreeLoading" show-checkbox node-key="id" ref="menuWebIdTree" :default-checked-keys="menuWebId" :props="{ label: 'title', children: 'children' }" highlight-current style="border: 1px solid #ebeef5; border-radius: 4px; padding: 8px; height: 300px; overflow-y: auto;" @check="menuWebIdTreeChange" />
      </template>
      <template slot="appMenuIdForm">
        <el-tree :data="menuMiniIdTree" v-loading="menuMiniIdTreeLoading" show-checkbox node-key="id" ref="menuMiniIdTree" :default-checked-keys="menuMiniId" :props="{ label: 'title', children: 'children' }" highlight-current style="border: 1px solid #ebeef5; border-radius: 4px; padding: 8px; height: 300px; overflow-y: auto;" @check="menuMiniIdTreeChange" />
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList, getDetail, add, update, remove } from "@/api/system/new_tenantpackage";
import { mapGetters } from "vuex";
import { getMenuTree } from "@/api/system/menu";
const getAllAssociatedMenuIds = (treeData) => {
  const result = new Set(); // 使用 Set 自动去重
  function traverse(node) {
    if (node.associatedMenuIds && node.associatedMenuIds.length > 0) {
      node.associatedMenuIds.forEach(id => result.add(id));
    }
    if (node.hasChildren && node.children.length > 0) {
      node.children.forEach(child => traverse(child));
    }
  }

  // 处理单节点或数组形式的输入
  if (Array.isArray(treeData)) {
    treeData.forEach(node => traverse(node));
  } else {
    traverse(treeData);
  }

  return Array.from(result); // 转为数组返回
}
export default {
  name: "tenantPackage",
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: false,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        // dialogWidth: 800,
        // labelWidth: 140,
        column: [
          {
            label: "推荐包名",
            prop: "name",
            search: true,
            span: 24,
            maxlength: 100,
            rules: [{
              required: true,
              message: "请输入推荐包名称",
              trigger: "blur"
            },
            //过滤纯空格
            {
              pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/,
              message: "请输入推荐包名称",
              trigger: "blur"
            }]
          },
          {
            label: "WEB菜单",
            prop: "webMenuId",
            span: 12,
            hide: true,
            formslot: true,
            dataType: "array",
            rules: [{
              required: true,
              message: "请选择WEB菜单",
              trigger: "change"
            }]
          },
          {
            label: "小程序",
            prop: "appMenuId",
            span: 12,
            hide: true,
            formslot: true,
            dataType: "array",
            rules: [{
              required: true,
              message: "请选择小程序菜单",
              trigger: "change"
            }]
          },
          {
            label: "备注",
            prop: "remark",
            span: 24,
            type:"textarea",
            maxlength: 500,
          },
        ]
      },
      data: [],
      menuWebIdTree: [],
      menuMiniIdTree: [],
      menuWebId: [],
      menuMiniId: [],
      menuWebIdTreeLoading: [],
      menuMiniIdTreeLoading: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: true,
        viewBtn: false,
        delBtn: true,
        editBtn: true
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },

  watch: {

  },
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      this.menuWebIdTreeLoading = true;
      this.menuMiniIdTreeLoading = true;
      getMenuTree(1).then(res => {
        // const column = this.findObject(this.option.column, "menuWebId");
        this.menuWebIdTree = res.data.data;
        this.menuWebIdTreeLoading = false;
        // column.dicData = res.data.data;
      }).finally(() => {
        this.menuWebIdTreeLoading = false;
      });
      getMenuTree(2).then(res => {
        // const column = this.findObject(this.option.column, "menuMiniId");
        // column.dicData = res.data.data;
        this.menuMiniIdTree = res.data.data;
        this.menuMiniIdTreeLoading = false;
      }).finally(() => {
        this.menuMiniIdTreeLoading = false;
      });
    },
    rowSave(row, done, loading) {
      // console.log(row.menuWebId,row.menuMiniId);
      // row.menuId = row.menuWebId.concat(row.menuMiniId).join(",");
      // console.log(row.menuId);
      let params = {
        appMenuId: row.appMenuId.join(","),
        webMenuId: row.webMenuId.join(","),
        name: row.name,
        remark: row.remark
      }
      if (params.menuId == "") {
        this.$message.warning("请选择菜单");
        loading();
        return;
      }
      // console.log(row);
      add(params).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {

      console.log(row,this.form,typeof(row.appMenuId));
      // return
      let params = {
        id: row.id,
        appMenuId: typeof(row.appMenuId) === 'string' ? row.appMenuId :row.appMenuId.join(","),
        webMenuId: typeof(row.webMenuId) === 'string' ? row.webMenuId : row.webMenuId.join(","),
        name: row.name,
        remark: row.remark
      }
      if (params.menuId == "") {
        this.$message.warning("请选择菜单");
        loading();
        return;
      }
      update(params).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.onLoad(this.page);
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.onLoad(this.page);

          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["add", "edit"].includes(type)) {
        // this.initData();
        this.menuWebId = []
        this.menuMiniId = []
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
          this.menuWebId = res.data.data.webMenuIds
          this.menuMiniId = res.data.data.appMenuIds
          this.form.menuWebId = res.data.data.webMenuId
          this.form.menuMiniId = res.data.data.appMenuId
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },

    menuWebIdTreeChange(data) {
      // console.log(data, node);
      let check = !this.form.webMenuId.includes(data.id)
      let associatedMenuIds = []
      associatedMenuIds = getAllAssociatedMenuIds(data)
      // console.log(associatedMenuIds)
      if (check) {
        // this.$refs.menuWebIdTree.setChecked(data.associatedMenuIds, true)

        for (let i = 0; i < associatedMenuIds.length; i++) {
          this.$refs.menuMiniIdTree.setChecked(associatedMenuIds[i], true)
        }
      } else {
        // this.$refs.menuWebIdTree.setChecked(data.associatedMenuIds, false)
        for (let i = 0; i < associatedMenuIds.length; i++) {
          this.$refs.menuMiniIdTree.setChecked(associatedMenuIds[i], false)
        }
      }

      this.$nextTick(() => {
        this.form.webMenuId = this.$refs.menuWebIdTree.getCheckedKeys()
        this.form.appMenuId = this.$refs.menuMiniIdTree.getCheckedKeys()
        this.$refs.crud.validateField("webMenuId");
        this.$refs.crud.validateField("appMenuId");
      })

    },
    menuMiniIdTreeChange(data) {
      let check = !this.form.appMenuId.includes(data.id)
      let associatedMenuIds = []
      associatedMenuIds = getAllAssociatedMenuIds(data)
      // console.log(associatedMenuIds)
      if (check) {
        // this.$refs.menuWebIdTree.setChecked(data.associatedMenuIds, true)
        for (let i = 0; i < associatedMenuIds.length; i++) {
          this.$refs.menuWebIdTree.setChecked(associatedMenuIds[i], true)
        }
      } else {
        // this.$refs.menuWebIdTree.setChecked(data.associatedMenuIds, false)
        for (let i = 0; i < associatedMenuIds.length; i++) {
          this.$refs.menuWebIdTree.setChecked(associatedMenuIds[i], false)
        }
      }

      this.$nextTick(() => {
        this.form.webMenuId = this.$refs.menuWebIdTree.getCheckedKeys()
        this.form.appMenuId = this.$refs.menuMiniIdTree.getCheckedKeys()
        this.$refs.crud.validateField("webMenuId");
        this.$refs.crud.validateField("appMenuId");
      })
    }


  }
};
</script>

<style>
</style>

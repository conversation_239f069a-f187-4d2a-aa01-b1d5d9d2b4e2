<template>
  <div style="display: none;">
    <!-- 该组件无UI，仅提供埋点功能 -->
  </div>
</template>

<script>
import { uploadData } from '@/api/system/track'
import { mapGetters } from 'vuex'

export default {
  name: 'TrackDataComponents',
  props: {
    module: {
      type: String,
      required: true,
      validator(value) {
        return value && value.trim().length > 0
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    userId() {
      return (this.userInfo && this.userInfo.user_id) || (this.userInfo && this.userInfo.userId) || null
    }
  },
  mounted() {
    // 监听页面加载事件，自动上报页面访问埋点
    // console.log(this.userInfo)
    this.trackPageView()
  },
  methods: {
    /**
     * 埋点数据上报方法
     * @param {Object} params - 埋点参数
     * @param {string|null} params.action - 用户行为，字典action
     * @param {number|null} params.trackTime - 上送时间(13位时间戳)，可选，默认为当前时间
     * @param {string|null} params.pageUrl - 页面URL，可选，默认为当前页面URL
     * @param {string|null} params.pageTitle - 页面标题，可选，默认为当前页面标题
     * @returns {Promise} 返回上报结果
     */
    async trackData(params = {}) {
      try {
        // 参数验证
        if (!this.userId) {
          console.warn('埋点上报：userId 为必需参数，请确保用户已登录')
          return Promise.reject(new Error('userId 为必需参数，请确保用户已登录'))
        }

        if (!this.module) {
          console.warn('埋点上报：module 为必需参数，请通过 props 传入')
          return Promise.reject(new Error('module 为必需参数，请通过 props 传入'))
        }

        if (!params.action) {
          console.warn('埋点上报：action 为必需参数')
          return Promise.reject(new Error('action 为必需参数'))
        }

        // 构建上报数据
        const trackData = {
          userId: this.userId,
          module: this.module,
          action: params.action,
          trackTime: params.trackTime || Date.now(), // 默认当前时间戳
          pageUrl: params.pageUrl || window.location.href, // 默认当前页面URL
          pageTitle: params.pageTitle || document.title // 默认当前页面标题
        }

        // 调用上报接口
        const result = await uploadData(trackData)

        // console.log('埋点上报成功:', trackData)
        return result

      } catch (error) {
        // console.error('埋点上报失败:', error)
        throw error
      }
    },

    /**
     * 页面访问埋点 - 便捷方法
     * @param {Object} params - 埋点参数
     */
    trackPageView(params = {}) {
      return this.trackData({
        ...params,
        action: 'page_view' // 固定为页面访问行为
      })
    },

    /**
     * 查看操作埋点 - 便捷方法
     * @param {Object} params - 埋点参数
     */
    trackView(params = {}) {
      return this.trackData({
        ...params,
        action: 'view' // 固定为查看行为
      })
    },

    /**
     * 编辑操作埋点 - 便捷方法
     * @param {Object} params - 埋点参数
     */
    trackEdit(params = {}) {
      return this.trackData({
        ...params,
        action: 'edit' // 固定为编辑行为
      })
    },

    /**
     * 新增操作埋点 - 便捷方法
     * @param {Object} params - 埋点参数
     */
    trackAdd(params = {}) {
      return this.trackData({
        ...params,
        action: 'add' // 固定为新增行为
      })
    },

    /**
     * 下载操作埋点 - 便捷方法
     * @param {Object} params - 埋点参数
     */
    trackDownload(params = {}) {
      return this.trackData({
        ...params,
        action: 'download' // 固定为下载行为
      })
    },

    /**
     * 删除操作埋点 - 便捷方法
     * @param {Object} params - 埋点参数
     */
    trackDelete(params = {}) {
      return this.trackData({
        ...params,
        action: 'delete' // 固定为删除行为
      })
    },

    /**
     * 上传操作埋点 - 便捷方法
     * @param {Object} params - 埋点参数
     */
    trackUpload(params = {}) {
      return this.trackData({
        ...params,
        action: 'upload' // 固定为上传行为
      })
    }
  }
}
</script>

<style scoped>
/* 该组件无样式 */
</style>

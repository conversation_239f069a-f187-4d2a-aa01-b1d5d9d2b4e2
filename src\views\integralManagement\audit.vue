<!--
 * @Author: chenn26
 * @Date: 2023-01-13 09:14:51
 * @LastEditors: linqh21
 * @LastEditTime: 2024-02-04 16:23:03
 * @Description: 通讯合作社
-->
<template>
  <div>
    <el-row>
      <el-col :span="5" v-loading="treeLoading">
        <div class="box">
          <el-scrollbar>
            <basic-container>
              <avue-tree :option="treeOption" :data="treeData" @node-click="nodeClick"
                :filter-node-method="filterNodeMethod" />
            </basic-container>
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="19">
        <basic-container v-loading="loading">
          <avue-tabs :option="tabsOption" @change="handleTabChange"></avue-tabs>
          <UploadPoint v-if="type.prop === '0'" :deptId="deptId" />
          <InputPoint v-if="type.prop === '1'" :deptId="deptId" />

        </basic-container>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getTreeList, } from "@/api/communication";
import { mapGetters, userInfo } from "vuex";
import InputPoint from "./components/audit/inputPoint.vue";
import UploadPoint from "./components/audit/uploadPoint.vue";
export default {
  name: "audit",
  components: {
    InputPoint,
    UploadPoint
  },
  computed: {
    ...mapGetters(["permission"]),
    ids () {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  data () {
    return {
      loading: false,
      treeLoading: false,
      treeData: [],
      treeOption: {
        addBtn: false,
        menu: false,
        size: 'small',
        props: {
          labelText: '标题',
          label: 'title',
          value: 'value',
          children: 'children'
        }
      },
      deptId: '',
      type: {
        prop: ''
      },
      tabsOption: {
        column: [
          {
            label: "上报积分审批",
            prop: "0",
          },
          // {
          //   label: "录入积分审批",
          //   prop: "1",
          // },
        ],
      },
    };
  },
  mounted () {
    this.getDeptTree();
    // console.log(userInfo)
    // console.log(this.$store.state.user.userInfo)
    // if(this.$store.state.user.userInfo.role_name === "administrator" || this.$store.state.user.userInfo.role_name === "admin"){
    //   this.tabsOption.column=[
    //     {
    //       label: "上报积分审批",
    //       prop: "0",
    //     },
    //     {
    //       label: "录入积分审批",
    //       prop: "1",
    //     },
    //   ]
    // }else if(this.$store.state.user.userInfo.role_name === "province_admin"){
    //   this.type.prop='1'
    //   this.tabsOption.column=[
    //     {
    //       label: "录入积分审批",
    //       prop: "1",
    //     },
    //   ]

    // }else if(this.$store.state.user.userInfo.role_name === "city_admin"){
    // 只保留上报积分审批
    this.type.prop = '0'
    this.tabsOption.column = [
      {
        label: "上报积分审批",
        prop: "0",
      },
    ]

    // }
  },
  methods: {
    getDeptTree () {
      this.treeLoading = true;
      getTreeList().then(res => {
        this.treeData = res.data.data
        this.treeLoading = false;
      })
    },
    handleTabChange (column) {
      this.type = column;
    },
    filterNodeMethod(value, data) {
      if (!value) return true;
      return data.title.indexOf(value.trim()) !== -1;
    },
    nodeClick (data) {
      this.deptId = data.id;
    },
  }
}
</script>

<style scoped></style>

<!--
 * @Description: 中上-乡村概况
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenn26
 * @LastEditTime: 2023-01-18 11:57:15
-->
<template>
  <div style="position: relative; display: flex; flex-direction: column; padding: 1vh 0.5vw; box-sizing: border-box; width: 100%; height: 100%;">
    <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column; box-sizing: border-box">
      <img class="img-common" src="/img/bigScreen/mapBg.png" mode="scaleToFill" />
      <div style="flex:1; display: flex; justify-content: center; align-items: center; padding: 7.1vh 10.9vh 4.6vw;">
        <!-- <img style="width: 34.6vw; height: 69vh;" src="/img/bigScreen/map.png" mode="scaleToFill" /> -->
        <img style="width: 100%; height: 100%;" src="/img/bigScreen/map.png" mode="scaleToFill" />
      </div>
    </div>

    <div style="position: absolute; width: 100%; height: 41.76vh; bottom: 4vh; left: 0; right: 0;">
      <CenterFlow></CenterFlow>
    </div>
  </div>
</template>
<script>
import CenterFlow from "../centerFlow/index.vue";
export default {
  components: {
    CenterFlow,
  },

  props: {
    villageDesc: String,
    descImageList: Array,
    villageName: String,
    isShow: Boolean
  },
  data() {
    return {};
  },
  filters: {
    filter_name(value) {
      if (value.indexOf("委会") != -1) {
        return value.slice(0, -2)
      } else {
        return value
      }
    },
  },
};
</script>

<style lang="scss" scoped>

.img-common {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}
</style>

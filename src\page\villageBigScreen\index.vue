<!--
 * @Description: 八闽数村大屏
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2023-12-20 16:35:45
-->
<template>
  <div class="screen1">
    <!-- 背景图片 -->
    <!-- <img src="/img/screen/star-squashed.jpg" class="back-shadow-screen" alt="shadow" /> -->
    <div style="position: relative; display: flex;">
      <!--标题 -->
      <div class="title-header">
        <div class="header_left">
          <img src="/img/bigScreen/headLeft.png" class="header_image" alt="" />
          <div class="main-title text-style-A">锦尚镇数字乡村综合管理平台</div>
        </div>
        <div class="header_center">
          <img :src="tabIdx==0?'/img/bigScreen/headCenterL.png':'/img/bigScreen/headCenterR.png'" class="header_image" alt="" />
          <div :class="{'btn-title-select':tabIdx==0,'btn-title-normal':tabIdx!=0}" style="flex: 1; text-align: center;" @click="tabIdx=0">和美乡村</div>
          <div :class="{'btn-title-select':tabIdx==1,'btn-title-normal':tabIdx!=1}" style="flex: 1; text-align: center;" @click="tabIdx=1">平安乡村</div>
        </div>
        <div class="header_right">
          <img src="/img/bigScreen/headRight.png" class="header_image" alt="" />
        </div>

      </div>
      <!-- <div class="title-back-desc">八闽数村</div> -->
      <!--时间 -->
      <!-- <div class="screen-time">
        <img
          src="/img/screen/header-left.png"
          class="screen-time-back"
          alt=""
        />
        <div class="screen-time-desc">{{time}}</div>
      </div> -->
      <!--地点 -->
      <!-- <div class="screen-local">
        <img
          src="/img/screen/header-left.png"
          class="screen-local-back"
          alt=""
        />
        <div class="screen-local-desc">
          <img
            src="/img/screen/point-bule.png"
            class="screen-local-img"
            alt=""
          /><span class="screen-local-title">&nbsp;&nbsp;{{this.villageName}}</span>
        </div>
      </div> -->
    </div>
    <!--左侧栏 -->
    <div style="width: 100%; height: 100%; display: flex; box-sizing: border-box;">
      <div style="flex: 0 0 28%; display: flex; flex-direction: column; box-sizing: border-box;">
        <div style="flex: 0 0 33%; display: flex; flex-direction: column;">
          <LeftOn
          v-if="tabIdx==0"
            :townLeaderList="townLeaderList"
            :villageLeaderList="villageLeaderList"
            :villageName="villageName"
            :leftName="leftName"
            :rightName="rightName"
          />

          <RightCenter v-else />
        </div>
        <div style="flex: 0 0 33%; display: flex;">
          <LeftCenter v-if="tabIdx==0" :branchDesc="branchDesc" />
          <RightCenter v-else />
        </div>
        <div style="flex: 0 0 34%; display: flex;">
          <LeftBottom v-if="tabIdx==0" :honorList="honorList" />
          <RightCenter v-else />
        </div>
      </div>
      <div style="flex: 0 0 44%;">
        <CenterOn :villageDesc="villageDesc" :descImageList="descImageList"  :villageName ="villageName" :isShow="isShow"/>
      </div>
      <div style="flex: 0 0 28%; display: flex; flex-direction: column;">
        <div style="flex: 0 0 33%; display: flex; flex-direction: column;">
          <RightOn v-if="tabIdx==0" />
          <RightCenter v-else />
        </div>
        <div style="flex: 0 0 33%; display: flex; flex-direction: column;">
          <RightCenter v-if="tabIdx==0" />
          <RightCenter v-else />
        </div>
        <div style="flex: 0 0 34%; display: flex; flex-direction: column;">
          <RightBottom v-if="tabIdx==0"/>
          <RightCenter v-else />
        </div>
      </div>
    </div>
        <!--左侧栏 2-->
        <!-- <div class="left-center">
          <LeftCenter :branchDesc="branchDesc" />
        </div> -->
        <!--左侧栏 3-->
        <!-- <div class="left-bottom">
          <LeftBottom :honorList="honorList" />
        </div>
      </div>
    </div> -->
    <!--中间栏 -->
    <!-- <div class="screen-center pointer-events-auto">
      <img src="/img/screen/left.png" alt="" class="center-back-img" /> -->
      <!--中间栏 1-->
      <!-- <div class="center-on" v-if="isShow" style="height: 20vh;">
        <CenterOn :villageDesc="villageDesc" :descImageList="descImageList"  :villageName ="villageName" :isShow="isShow"/>
      </div>
      <div class="center-on" v-else style="height: 25vh;">
        <CenterOn :villageDesc="villageDesc" :descImageList="descImageList"  :villageName ="villageName" :isShow="isShow"/>
      </div> -->
      <!--中间栏 2-->
      <!-- <div class="center-center" v-if="isShow" style="height: 21vh;top: 20vh;">
        <CenterCenter
          :populationNum="populationNum"
          :farmerNum="farmerNum"
          :partyMemberNum="partyMemberNum"

          :situationData="situationData"
          :areaUnit ="areaUnit"
          :villageArea="villageArea"
          :isShow="isShow"
        />
      </div>
      <div class="center-center" v-else style="height: 30vh;top: 25vh;">
        <CenterCenter
          :populationNum="populationNum"
          :farmerNum="farmerNum"
          :partyMemberNum="partyMemberNum"

          :situationData="situationData"
          :areaUnit ="areaUnit"
          :villageArea="villageArea"
          :isShow="isShow"
        />
      </div> -->
      <!-- 中间栏 3 -->
      <!-- <div class="center-center2" v-if="isShow" style="height: 23vh;top: 40vh;">
        <CenterCenter2 />
      </div>
      <div class="center-center2" v-else style="height: 23vh;top: 40vh;" :hidden="!isShow">
        <CenterCenter2 />
      </div> -->
      <!--中间栏 4-->
      <!-- <div class="center-bottom" v-if="isShow" style="height: 22vh;top: 63vh;">
        <CenterBottom :isShow="isShow"></CenterBottom>
      </div>
      <div class="center-bottom" v-else style="height: 30vh;top: 60vh;">
        <CenterBottom :isShow="isShow"></CenterBottom>
      </div>
    </div> -->
    <!--  -->
    <!--右侧栏 -->
    <!-- <div class="screen-right pointer-events-auto">
      <img src="/img/screen/left.png" alt="" class="right-img" /> -->
      <!--右侧栏 1-->
      <!-- <div class="right-on">
        <RightOn />
      </div> -->
      <!--右侧栏 2-->
      <!-- <div class="right-center">
        <RightCenter />
      </div> -->
      <!--右侧栏 3-->
      <!-- <div class="right-bottom">
        <RightBottom />
      </div>
    </div> -->
  </div>
</template>
<script>
// import { mapGetters } from "vuex";

import LeftOn from "./leftOn";
import LeftCenter from "./leftCenter";
import LeftBottom from "./leftBottom";
import CenterOn from "./centerOn";
import CenterCenter from "./centerCenter";
import CenterCenter2 from "./centerCenter2";
import CenterBottom from "./centerBottom";
import RightOn from "./rightOn";
import RightCenter from "./rightCenter";
import RightBottom from "./rightBottom";

import { getVillage, hasData } from "@/api/screen/screen";
import { getDateAndTime } from '@/util/util'
import "./index.scss";
export default {
  components: {
    LeftOn,
    LeftCenter,
    LeftBottom,
    CenterOn,
    CenterCenter,
    CenterCenter2,
    CenterBottom,
    RightOn,
    RightCenter,
    RightBottom,
  },
  data() {
    return {
      bigScreenDialogVisible: true,
      tabIdx: 0,
      branchDesc: "", //乡村简介
      villageDesc: "", //乡村详情内容
      descImageList: [], //乡村详情列表
      honorList: [], //荣誉列表
      townLeaderList: [], //乡镇党组织列表
      villageLeaderList: [], //村委党组织列表

      populationNum: 0, //总人口
      farmerNum: 0, //总农户
      partyMemberNum: 0, //党员人口
      villageArea:0,
      situationData : [
        {
          name: "耕地面积",
          value: 0,
        },
        {
          name: "林地面积",
          value: 0,
        },
        {
          name: "水域面积",
          value: 0,
        }
      ],
      areaUnit:"单位",
      timeTimer:null,
      time:"",
      villageName:"",
      isShow: false,
    };
  },

  created() {
    this.getVillage();
    this.$router.$avueRouter.setTitle("乡村大屏");
   //一秒刷新一次显示时间
    this.timeTimer = setInterval(() => {
        let date = new Date(); // 修改数据date
        this.time = getDateAndTime(date);
    }, 1000);
    this.hasData();
  },
  destroyed(){
    if(this.timeTimer){
      clearInterval(this.timeTimer)
    }
  },
  computed: {
    // ...mapGetters(["userInfo"]),
  },
  props: [],
  methods: {
    hasData(){
      hasData().then(res=>{
        this.isShow = res.data.data;
      })
    },
    async getVillage() {
      const result = await new Promise((resolve) => {
        getVillage().then((res) => {
          if (res && res.data.code === 200) {
            resolve(res.data.data);
          }
        });
      });
      this.$nextTick(() => {
        this.setAllData(result);
      });
    },
    setAllData(data) {
      this.branchDesc = data.branchDesc || '';
      this.villageDesc = data.villageDesc || '';
      this.descImageList = data.descImageList || [];
      this.honorList = data.honorList || [];
      this.townLeaderList = data.townLeaderList || [];
      this.villageLeaderList = data.villageLeaderList || [];

      this.populationNum = data.populationNum || 0;
      this.farmerNum = data.farmerNum || 0;
      this.partyMemberNum = data.partyMemberNum || 0;

      this.villageName = data.villageName || '';
      this.leftName = data.leftName || '';
      this.rightName = data.rightName || '';
      //中中-民情概况
      this.situationData = [
        {
          name: "耕地面积",
          value: data.farmLandArea||0,
        },
        {
          name: "林地面积",
          value: data.forestLandArea||0,
        },
        {
          name: "水域面积",
          value: data.waterArea||0,
        },
        {
          name: "其他面积",
          value: data.villageArea?(parseFloat(data.villageArea||0)-parseFloat(data.farmLandArea||0)-parseFloat(data.forestLandArea||0)-parseFloat(data.waterArea||0)).toFixed(2):0,
        },
      ];
      console.log(this.situationData);
      this.areaUnit = data.areaUnit?data.areaUnit:"";
      this.villageArea = data.villageArea?data.villageArea:0
    }
    }
};
</script>

<style lang="scss" scoped>
.main-title {
  font-size: 3.7vh;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  // letter-spacing: 4px;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.btn-title-select {
  margin-top: 2vh;
  font-size: 2.2vh;
  font-style: normal;
  font-weight: bold;
  line-height: normal;
  background: linear-gradient(188deg, #FC0 11.62%, #FC0 30.26%, #FF9000 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.btn-title-normal {
  margin-top: 2vh;
  font-size: 2.2vh;
  font-style: normal;
  font-weight: bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.bigscreen-dialog {
  /* 位置控制 */
  margin-top: 5vh !important; /* 距离顶部 */

  /* 尺寸控制 */
  width: 44% !important; /* 宽度 */
  max-width: 1200px; /* 最大宽度 */

  /* 样式美化 */
  background: red;
  border: 2px solid #00a1ff;
  border-radius: 10px;
  box-shadow: 0 0 30px rgba(0, 161, 255, 0.7);
}
</style>

<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :permission="permissionList" :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate" @row-save="rowSave" @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger" size="small" icon="el-icon-delete" plain @click="handleDelete">删 除
        </el-button>
      </template>
      <template slot="menuWebIdForm">
        <el-tree :data="menuWebIdTree" show-checkbox node-key="id" ref="menuWebIdTree" :default-checked-keys="menuWebId" :props="{ label: 'title', children: 'children' }" highlight-current style="border: 1px solid #ebeef5; border-radius: 4px; padding: 8px; height: 300px; overflow-y: auto;" @check="menuWebIdTreeChange" />
      </template>
      <template slot="menuMiniIdForm">
        <el-tree :data="menuMiniIdTree" show-checkbox node-key="id" ref="menuMiniIdTree" :default-checked-keys="menuMiniId" :props="{ label: 'title', children: 'children' }" highlight-current style="border: 1px solid #ebeef5; border-radius: 4px; padding: 8px; height: 300px; overflow-y: auto;" @check="menuMiniIdTreeChange" />
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList, getDetail, add, update, remove } from "@/api/system/tenantpackage";
import { mapGetters } from "vuex";
import { getMenuTree } from "@/api/system/menu";
const getAllAssociatedMenuIds = (treeData)=> {
  const result = new Set(); // 使用 Set 自动去重
  function traverse(node) {
    if (node.associatedMenuIds && node.associatedMenuIds.length > 0) {
      node.associatedMenuIds.forEach(id => result.add(id));
    }
    if (node.hasChildren && node.children.length > 0) {
      node.children.forEach(child => traverse(child));
    }
  }

  // 处理单节点或数组形式的输入
  if (Array.isArray(treeData)) {
    treeData.forEach(node => traverse(node));
  } else {
    traverse(treeData);
  }

  return Array.from(result); // 转为数组返回
}
export default {
  name: "tenantPackage",
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        dialogClickModal: false,
        dialogWidth: 800,
        labelWidth: 140,
        column: [
          {
            label: "产品包名",
            prop: "packageName",
            search: true,
            span: 24,
            maxlength: 100,
            rules: [{
              required: true,
              message: "请输入产品包名称",
              trigger: "blur"
            }]
          },
          {
            label: "web菜单列表",
            prop: "menuWebId",
            span: 12,
            hide: true,
            formslot: true,
            dataType: "array",
          },
          {
            label: "小程序菜单列表",
            prop: "menuMiniId",
            span: 12,
            hide: true,
            formslot: true,
            dataType: "array",
          },
          {
            label: "备注",
            prop: "remark",
            span: 24,
            maxlength: 500,
          },
        ]
      },
      data: [],
      menuWebIdTree: [],
      menuMiniIdTree: [],
      menuWebId: [],
      menuMiniId: [],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: true,
        viewBtn: false,
        delBtn: true,
        editBtn: true
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },

  watch: {

  },
  methods: {
    initData() {
      getMenuTree(1).then(res => {
        // const column = this.findObject(this.option.column, "menuWebId");
        this.menuWebIdTree = res.data.data;
        // column.dicData = res.data.data;
      });
      getMenuTree(2).then(res => {
        // const column = this.findObject(this.option.column, "menuMiniId");
        // column.dicData = res.data.data;
        this.menuMiniIdTree = res.data.data;
      });
    },
    rowSave(row, done, loading) {
      // console.log(row.menuWebId,row.menuMiniId);
      // row.menuId = row.menuWebId.concat(row.menuMiniId).join(",");
      // console.log(row.menuId);
      let params = {
        menuId: row.menuWebId.concat(row.menuMiniId).join(","),
        packageName: row.packageName,
        remark: row.remark
      }
      if(params.menuId==""){
        this.$message.warning("请选择菜单");
        loading();
        return;
      }
      // console.log(row);
      add(params).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, done, loading) {
      let params = {
        id: row.id,
        menuId: row.menuWebId.concat(row.menuMiniId).join(","),
        packageName: row.packageName,
        remark: row.remark
      }
      if(params.menuId==""){
        this.$message.warning("请选择菜单");
        loading();
        return;
      }
      update(params).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["add", "edit"].includes(type)) {
        this.initData();
        this.menuWebId = []
        this.menuMiniId = []
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
          this.menuWebId = res.data.data.webMenuIds
          this.menuMiniId = res.data.data.appMenuIds
          this.form.menuWebId = res.data.data.webMenuIds
          this.form.menuMiniId = res.data.data.appMenuIds
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },

    menuWebIdTreeChange(data, node) {
      // console.log(data, node);
      let check = !this.form.menuWebId.includes(data.id)
      let associatedMenuIds = []
      associatedMenuIds = getAllAssociatedMenuIds(data)
      console.log(associatedMenuIds)
      if(check){
        // this.$refs.menuWebIdTree.setChecked(data.associatedMenuIds, true)

        for (let i = 0; i < associatedMenuIds.length; i++) {
          this.$refs.menuMiniIdTree.setChecked(associatedMenuIds[i], true)
        }
      }else{
        // this.$refs.menuWebIdTree.setChecked(data.associatedMenuIds, false)
        for (let i = 0; i < associatedMenuIds.length; i++) {
          this.$refs.menuMiniIdTree.setChecked(associatedMenuIds[i], false)
        }
      }

      this.$nextTick(() => {
        this.form.menuWebId = this.$refs.menuWebIdTree.getCheckedKeys()
        this.form.menuMiniId = this.$refs.menuMiniIdTree.getCheckedKeys()
      })

    },
    menuMiniIdTreeChange(data, node) {
      let check = !this.form.menuMiniId.includes(data.id)
      let associatedMenuIds = []
      associatedMenuIds = getAllAssociatedMenuIds(data)
      console.log(associatedMenuIds)
      if(check){
        // this.$refs.menuWebIdTree.setChecked(data.associatedMenuIds, true)
        for (let i = 0; i < associatedMenuIds.length; i++) {
          this.$refs.menuWebIdTree.setChecked(associatedMenuIds[i], true)
        }
      }else{
        // this.$refs.menuWebIdTree.setChecked(data.associatedMenuIds, false)
        for (let i = 0; i < associatedMenuIds.length; i++) {
          this.$refs.menuWebIdTree.setChecked(associatedMenuIds[i], false)
        }
      }

      this.$nextTick(() => {
        this.form.menuWebId = this.$refs.menuWebIdTree.getCheckedKeys()
        this.form.menuMiniId = this.$refs.menuMiniIdTree.getCheckedKeys()
      })
    }


  }
};
</script>

<style>
</style>

.town-left-bottom-content {
    position: absolute;
    width: 100%;
    height: 27vh;
    .header {
        position: absolute;
        width: 100%;
        white-space: nowrap;
        top: 3vh;
        left: 50%;
        transform: translate(-50%, 0);
        .left-decorate {
            width: 34%;
        }
        .title {
            letter-spacing: 3px;
            vertical-align: top;
            margin: 0 5%;
            color: #597cff;
            font-size: 2vh;
        }
        .right-decorate {
            width: 34%;
            transform: rotateY(180deg);
        }
    }
    .content {
        position: absolute;
        float: left;
        margin-top: 7vh;
        width: 92%;
        margin-left: 4%;
        .content-carousel {
            position: absolute;
            width: 100%;
            height: 25vh;
            margin-top:3vh;
            .el-carousel__button {
                display: block;
                opacity: 0.48;
                width: 10px;
                height: 2px;
                background-color: #fff;
                border: none;
                outline: 0;
                padding: 0;
                margin: 0;
                cursor: pointer;
                -webkit-transition: 0.3s;
                transition: 0.3s;
            }
            .content-img {
                width: 100%;
                height: 25vh;
            }
        }
    }
}

<!--
 * @Description: 民情管理
 * @Author: linzq33
 * @Date: 2025-07-18 14:21:17
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div>
    <el-row>
      <el-col :span="5">
        <div class="box">
          <el-scrollbar>
            <basic-container>
              <avue-tree :option="treeOption" :data="treeData" @node-click="nodeClick" show-checkbox>
              </avue-tree>
            </basic-container>
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="19">
        <basic-container>
          <el-tabs v-model="activeName">
            <el-tab-pane label="待处理" name="0"/>
            <el-tab-pane label="处理中" name="1"/>
            <el-tab-pane label="已办结" name="2"/>
          </el-tabs>
          <Table :activeName="activeName"  ref="manageTable"/>
        </basic-container>
      </el-col>
    </el-row>
  </div>

</template>

<script>
import { getDeptTree } from "@/api/infoRelease/partyLead"
import Table from './components/table.vue'
export default {
  components: {
    Table
  },
  data() {
    return {
      activeName: '0', // tabs切换

      treeLoading: false,
      treeData: [],
      treeOption: {
        addBtn: false,
        menu: false,
        size: 'small',
        props: {
          labelText: '标题',
          label: 'title',
          value: 'value',
          children: 'children'
        }
      },

    }
  },
  computed: {},
  mounted() {
    this.getDeptTree()
  },
  methods: {
    // 左侧树
    getDeptTree() {
      this.treeLoading = true;
      getDeptTree().then(res => {
        this.treeData = res.data.data
        this.treeLoading = false;
      })
    },
    nodeClick(data) {
      this.$refs.manageTable.deptId = data.id;
      this.$refs.manageTable.onLoad(this.$refs.manageTable.page)
    },
  }
}
</script>

<style>
.none-border {
  border: 0;
  background-color: transparent !important;
}
</style>

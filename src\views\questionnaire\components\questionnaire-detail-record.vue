<template>
    <div style="padding-top: 8px">
        <avue-crud
            ref="crud"
            :option="option"
            :table-loading="loading"
            :data="data"
            :page.sync="page"
            @selection-change="selectionChange"
            @current-change="currentChange"
            @size-change="sizeChange"
            @refresh-change="refreshChange"
            @on-load="onLoad">
            <template slot-scope="{ type, size, row }" slot="menu">
                <el-button
                    :type="type"
                    icon="el-icon-view"
                    :size="size"
                    @click.stop="preview(row)"
                >查看
                </el-button>
            </template>
            <template slot="menuLeft">
                <el-button 
                    type="primary" 
                    @click="exportData" 
                    icon="el-icon-download" 
                    :disabled="disabledBtn" 
                    size="small"
                    v-if="getPermission().quest_export">导出</el-button>
            </template>
        </avue-crud>
        <preview :showPreviewDrawer.sync="showPreview" :detailId="detailId"  v-if="showPreview" />
    </div>
</template>
<script>
import preview from './questionnaire-preview.vue'
import { getStatisticsList } from '@/api/questionnaire/survey'
import { handleDownload } from "@/util/download";
export default {
    // props: {
    //     questionnaireId: {
    //         type: Number,
    //         required: true
    //     }
    // },
    inject: ['getQuestionnaireId', 'getPermission'],
    components: {
        preview
    },
    computed: {
        ids() {
            let ids = [];
            this.selectionList.forEach((ele) => {
                ids.push(ele.id);
            });
            return ids.join(",");
        },
    },
    data() {
        return {
            detailId: '',
            showPreview: false,
            data: [],
            loading: true,
            selectionList: [],
            disabledBtn: false,
            page: {
                currentPage: 1,
                pageSize: 10,
                total: 0
            },
            option: {
                height: 'auto',
                calcHeight: 30,
                tip: false,
                border: false,
                index: true,
                viewBtn: false,
                editBtn: false,
                delBtn: false,
                addBtn: false,
                selection: true,
                menuWidth: 300,
                labelWidth: 100,
                column: [
                    {
                        label: '提交人',
                        prop: 'userName',
                    },
                    {
                        label: '提交时间',
                        prop: 'finishTime',
                    },
                    {
                        label: '填写次数',
                        prop: 'writeNum'
                    }
                ]
            }
        }
    },
    methods: {
         onLoad(page) {
            this.loading = true
            getStatisticsList(page.currentPage, page.pageSize, this.getQuestionnaireId())
                .then(res => {
                    const data = res.data.data
                    this.page.total = data.total
                    this.data = data.records
                    this.disabledBtn = this.data.length === 0 ? true : false
                    this.loading = false
                    this.selectionClear();
                })
         },
         selectionChange(list) {
            this.selectionList = list;
         },
         selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
         },
         currentChange(currentPage) {
            this.page.currentChange = currentPage
         },
         sizeChange(pageSize) {
            this.page.pageSize = pageSize
         },
         preview(row) {
            this.detailId = row.id
            this.showPreview = true
         },
         exportData() {
            this.$confirm('是否导出数据?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async() => {
                this.disabledBtn = true
                const url = `/api/admin/survey/export?id=${this.getQuestionnaireId()}&surveyUserIds=${this.ids}`;
                const result = await handleDownload(url)
                if(result !== null) {
                    this.disabledBtn = false
                }
            })
         },
         refreshChange() {
            this.onLoad(this.page);
        },
         
    }
}
</script>

export default {
  height: 'auto',
  calcHeight: 30,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  viewBtn: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  grid: true,
  selection: false,
  labelWidth: 120,
  menuWidth: 200,
  dialogClickModal: false,
  column: [
    {
      label: '消息标题',
      prop: 'title',
      search: true,
      searchSpan: 6,
      width: 200,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请输入消息标题',
          trigger: 'blur'
        }
      ]
    },
    {
      label: '消息内容',
      prop: 'content',
      type: 'textarea',
      search: true,
      searchSpan: 6,
      width: 300,
      overHidden: true,
      span: 24,
      rules: [
        {
          required: true,
          message: '请输入消息内容',
          trigger: 'blur'
        }
      ]
    },
    {
      label: '消息类型',
      prop: 'type',
      type: 'select',
      search: true,
      searchSpan: 6,
      width: 120,
      dicData: [
        {
          label: '系统通知',
          value: 1
        },
        {
          label: '工作提醒',
          value: 2
        },
        {
          label: '预警信息',
          value: 3
        },
        {
          label: '其他',
          value: 4
        }
      ],
      rules: [
        {
          required: true,
          message: '请选择消息类型',
          trigger: 'change'
        }
      ]
    },
    {
      label: '发送人',
      prop: 'senderName',
      search: true,
      searchSpan: 6,
      width: 120,
      addDisplay: false,
      editDisplay: false
    },
    {
      label: '接收人',
      prop: 'receiverName',
      search: true,
      searchSpan: 6,
      width: 120,
      addDisplay: false,
      editDisplay: false
    },
    {
      label: '是否已读',
      prop: 'isRead',
      type: 'select',
      search: true,
      searchSpan: 6,
      width: 100,
      align: 'center',
      slot: true,
      dicData: [
        {
          label: '未读',
          value: 0
        },
        {
          label: '已读',
          value: 1
        }
      ],
      addDisplay: false,
      editDisplay: false
    },
    {
      label: '优先级',
      prop: 'priority',
      type: 'select',
      search: true,
      searchSpan: 6,
      width: 100,
      align: 'center',
      slot: true,
      dicData: [
        {
          label: '低',
          value: 1
        },
        {
          label: '中',
          value: 2
        },
        {
          label: '高',
          value: 3
        },
        {
          label: '紧急',
          value: 4
        }
      ],
      rules: [
        {
          required: true,
          message: '请选择优先级',
          trigger: 'change'
        }
      ]
    },
    {
      label: '发送时间',
      prop: 'sendTime',
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm:ss',
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      search: true,
      searchRange: true,
      searchSpan: 12,
      width: 160,
      addDisplay: false,
      editDisplay: false
    },
    {
      label: '创建时间',
      prop: 'createTime',
      type: 'datetime',
      format: 'yyyy-MM-dd HH:mm:ss',
      valueFormat: 'yyyy-MM-dd HH:mm:ss',
      search: true,
      searchRange: true,
      searchSpan: 12,
      width: 160,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true
    }
  ]
}

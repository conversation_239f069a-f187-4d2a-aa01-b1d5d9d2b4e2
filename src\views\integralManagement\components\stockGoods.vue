<!--
 * @Description:积分商城-库存
 * @Author: wangyy553
 * @Date: 2021-12-21 17:27:30
 * @LastEditors: wangyy553
 * @LastEditTime: 2022-01-19 14:37:40
-->
<template>
  <el-dialog
    :fullscreen="isFullscreen"
    :visible.sync="dialogVisible"
    append-to-body="true"
    :close-on-click-modal="false"
    top="100px"
    width="60%"
    @close="close()"
  >
    <div slot="title" class="header">
      <div class="avue-crud__dialog__header">
        <span class="el-dialog__title">{{ title }}</span>
        <div class="avue-crud__dialog__menu">
          <i
            @click="isFullScreen"
            class="el-dialog__close el-icon-full-screen"
          ></i>
        </div>
      </div>
      <button type="button" aria-label="Close" class="el-dialog__headerbtn">
        <i class="el-dialog__close el-icon el-icon-close"></i>
      </button>
    </div>
    <div class="tip-container">
      <div style="font-size: 15px; font-weight: 600">库存管理提示</div>
      <div>1、添加后，即可生效，请谨慎修改。</div>
    </div>
    <avue-form
      :option="option2"
      v-model="form"
      ref="form"
      @submit="submit"
    ></avue-form>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      ref="crud"
      @refresh-change="refreshChange"
      @current-change="currentChange"
      @size-change="sizeChange"
    >
      <template slot="user" slot-scope="scope">
        <span>{{ scope.row.userRealName + "--" + scope.row.userId }}</span>
      </template>
      <template slot="stock" slot-scope="scope">
        <span v-if="scope.row.status == 2">{{ "-" + scope.row.stock }}</span>
        <span v-else>{{ scope.row.stock }}</span>
      </template>
    </avue-crud>
  </el-dialog>
</template>

<script>
import * as api from "@/api/integralManagement/mall";
export default {
  props: ["visible", "id"],
  data() {
    return {
      isFullscreen: false,
      title: "库存管理",
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        index: true,
        indexLabel: "序号",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: false,
        searchMenuSpan: 6,
        border: true,
        selection: false,
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: false,
        dicFlag: true,
        menu: false,
        column: [
          {
            label: "出入库",
            prop: "status",
            type: "select",
            dicData: [
              {
                label: "上架入库",
                value: 0,
              },
              {
                label: "入库",
                value: 1,
              },
              {
                label: "出库",
                value: 2,
              },
            ],
          },
          {
            label: "出入库数",
            prop: "stock",
            slot: true,
            formslot: true,
          },
          {
            label: "创建人",
            prop: "user",
            slot: true,
          },
          {
            label: "创建时间",
            prop: "createTime",
          },
        ],
      },
      data: [],
      option2: {
        labelWidth: 120,
        column: [
          {
            label: "出入库",
            prop: "status",
            type: "tree",
            filter: false,
            props: {
              label: "name",
              value: "value",
            },
            dicData: [
              {
                name: "入库",
                value: 1,
              },
              {
                name: "出库",
                value: 2,
              },
            ],
            rules: [
              {
                required: true,
                message: "请选择出入库",
                trigger: "[blur,change]",
              },
            ],
          },
          {
            label: "出入库数量",
            type: "number",
            prop: "stock",
            max: 999999999,
            min: 1,
            precision: 0, //精度
            rules: [
              {
                required: true,
                message: "请输入出入库数量",
                trigger: "[blur,change]",
              },
            ],
          },
        ],
      },
      form: {},
    };
  },
  methods: {
    close() {
      this.emptyForm();
      this.page.currentPage = 1;
      this.page.pageSize = 10;

      this.$emit("updateTable");
      this.dialogVisible = false;
    },
    isFullScreen() {
      this.isFullscreen = !this.isFullscreen;
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.initData();
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.initData();
    },
    refreshChange() {
      this.initData();
    },
    initData() {
      this.loading = true;
      api
        .getGoodsStock(this.page.currentPage, this.page.pageSize, this.id)
        .then((res) => {
          this.data = res.data.data.records;
          this.page.total = res.data.data.total;
          this.loading = false;
        });
    },
    emptyForm() {
      this.$refs.form.resetForm();
    },
    submit() {
      this.$refs.form.validate((valid, done) => {
        if (valid) {
          var obj = Object.assign({}, this.form);
          obj.id = this.id;
          delete obj.$status;
          api
            .submitStock(obj)
            .then((res) => {
              if (res.data.success) {
                this.$message({
                  type: "success",
                  message: res.data.msg,
                });
              } else {
                this.$message({
                  type: "warning",
                  message: res.data.msg,
                });
              }
              done();
              this.emptyForm();
              this.initData();
            })
            .catch(() => {
              done();
            });
        }
      });
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
};
</script>

<style lang="scss" scoped>
.tip-container {
  background: rgb(235, 245, 255);
  padding: 10px 30px;
  height: 40px;
  border: 1px solid rgb(140, 197, 255);
  border-radius: 5px;
  color: #909399;
  margin-bottom: 20px;
}
</style>

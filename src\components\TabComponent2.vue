<!--
 * @Date: 2025-08-04
 * @Description: Tab切换子组件2
-->
<template>
  <div class="tab-component">
    <div class="component-header">
      <h2>组件2</h2>
    </div>
    <div class="component-content">
      <p>这是第二个子组件的内容</p>
      <div class="info-cards">
        <div class="info-card">
          <h4>信息卡片1</h4>
          <p>这里是一些详细信息的描述内容</p>
        </div>
        <div class="info-card">
          <h4>信息卡片2</h4>
          <p>这里是另一些详细信息的描述内容</p>
        </div>
      </div>
      <div class="data-section">
        <h3>数据展示</h3>
        <table class="data-table">
          <thead>
            <tr>
              <th>项目</th>
              <th>状态</th>
              <th>进度</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>项目A</td>
              <td>进行中</td>
              <td>75%</td>
            </tr>
            <tr>
              <td>项目B</td>
              <td>已完成</td>
              <td>100%</td>
            </tr>
            <tr>
              <td>项目C</td>
              <td>待开始</td>
              <td>0%</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TabComponent2',
  data() {
    return {
      // 组件2的数据
    }
  },
  methods: {
    // 组件2的方法
  }
}
</script>

<style scoped>
.tab-component {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  min-height: 400px;
}

.component-header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #28a745;
}

.component-header h2 {
  color: #28a745;
  margin: 0;
}

.component-content {
  line-height: 1.6;
}

.info-cards {
  display: flex;
  gap: 15px;
  margin: 20px 0;
}

.info-card {
  flex: 1;
  background: white;
  padding: 15px;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.info-card h4 {
  color: #28a745;
  margin-top: 0;
  margin-bottom: 10px;
}

.data-section {
  margin-top: 25px;
}

.data-section h3 {
  color: #333;
  margin-bottom: 15px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.data-table th,
.data-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.data-table th {
  background: #28a745;
  color: white;
  font-weight: 600;
}

.data-table tbody tr:hover {
  background: #f8f9fa;
}

.data-table tbody tr:last-child td {
  border-bottom: none;
}
</style>

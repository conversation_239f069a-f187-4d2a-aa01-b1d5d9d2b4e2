{"name": "saber-admin", "version": "4.5.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "analyz": "npm_config_report=true npm run build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@jiaminghi/data-view": "^2.10.0", "avue-plugin-ueditor": "^0.2.3", "axios": "^1.7.2", "babel-polyfill": "^6.26.0", "classlist-polyfill": "^1.2.0", "crypto-js": "^4.0.0", "echarts": "^5.6.0", "element-ui": "^2.15.6", "js-base64": "^2.5.1", "js-cookie": "^2.2.0", "js-md5": "^0.7.3", "node-gyp": "^5.0.6", "nprogress": "^0.2.0", "portfinder": "^1.0.23", "script-loader": "^0.7.2", "sm-crypto": "^0.3.13", "vue": "^2.6.10", "vue-axios": "^2.1.2", "vue-count-to": "^1.0.13", "vue-i18n": "^8.7.0", "vue-router": "^3.0.1", "vuedraggable": "^2.24.3", "vuex": "^3.1.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.1.1", "@vue/cli-plugin-eslint": "^3.1.5", "@vue/cli-service": "^3.1.4", "chai": "^4.1.2", "sass": "^1.77.6", "sass-loader": "^10.0.5", "vue-img-cutter": "^2.2.5", "vue-template-compiler": "^2.5.17", "webpack-bundle-analyzer": "^3.0.3"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}}
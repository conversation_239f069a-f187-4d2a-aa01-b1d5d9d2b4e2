<template>
  <div>
    <CommonTypeTwo :moduleName="moduleName" :moduleKey="moduleKey" :moduleDic="moduleDic" :funObj="funObj" ></CommonTypeTwo>
  </div>
</template>

<script>
import CommonTypeTwo from '@/views/components/CommonTypeTwo'
import * as funList from "@/api/infoRelease/info";
export default {
  components: {
    CommonTypeTwo
  },
  data() {
    return {
      moduleName: '三务公开',
      moduleKey: 'threeaffairs',
      moduleDic: 'threeAffairsType',
      funObj:funList
    }
  },
  created() {
    // this.$nextTick(() => {
    //   this.$refs.commonTypeOne.initData()
    // })
  }
}
</script>

<style>

</style>

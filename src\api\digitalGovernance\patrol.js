/*
 * @Date: 2025-02-13 21:46:49
 * @LastEditors: linqh21
 * @LastEditTime: 2025-02-13 21:50:36
 * @Description:
 * @FilePath: \src\api\digitalGovernance\patrol.js
 */
import request from '@/router/axios';


/**
 * @description: 请求列表
 * @param {object} params
 * @author:
 */
export const getList = (params) => {
  return request({
    url: '/api/admin/patrol/page',
    method: 'get',
    params,
  })
}

/**
 * @description: 新增
 * @param {object} data
 * @author:
 */
export const save = (data) => {
  return request({
    url: '/api/admin/patrol/save',
    method: 'post',
    data,
  })
}

/**
 * @description: 查看详情
 * @param {object} params
 * @author:
 */
export const detail = (params) => {
  return request({
    url: '/api/admin/patrol/detail',
    method: 'get',
    params,
  })
}

/**
 * @description: 修改
 * @param {object} data
 * @author:
 */
export const update = (data) => {
  return request({
    url: '/api/admin/patrol/update',
    method: 'post',
    data,
  })
}

/**
 * @description: 删除
 * @param {object} params
 * @author:
 */
export const remove = (params) => {
  return request({
    url: '/api/admin/patrol/remove',
    method: 'post',
    params,
  })
}

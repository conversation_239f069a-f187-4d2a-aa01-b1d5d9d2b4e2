<!--
 * @Date: 2025-07-11 10:17:56
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-11 16:06:59
 * @Description:
 * @FilePath: \src\views\test\index.vue
-->
<template>
  <div>url:{{url}}</div>
</template>

<script>
export default {
  data() {
    return {
      url:""
    }
  },
  created() {
  },
  watch: {
    $route(to, from) {
      console.log(to, "to")
      console.log(from, "from")
      if (to.fullPath !== from.fullPath) {
         this.url = to.params.id;
         //修改路由name名称
        //  let tag = this.$store.getters.tagList
        // tag[tag.length-1].label = this.url
        // this.$store.commit('SET_TAG_LIST', tag)
        //  this.$router.$avueRouter.setTitle("sss")
        //  this.$route.name = "test";
      }
    },
  },
  mounted() {
    //获取url上id参数
    this.url = this.$route.params.id;
    console.log("初始化",this.$store.getters.tagList)
    // let tag = this.$store.getters.tagList
    // tag[tag.length-1].label = this.url
    // this.$store.commit('SET_TAG_LIST', tag)
  },
  methods: {
  }
}
</script>

<style>

</style>

<template>
  <div>
    <div
      id="mapContent"
      style="height:600px"
      v-loading="mapLoading"
      element-loading-text="正在加载中..."
    ></div>
    <div v-if="!readOnly" style="text-align: right; margin-top: 10px">
      <el-button type="success" :disabled="saveFlag" @click="saveData">保存</el-button>
      <el-button type="danger" @click="clear">清除</el-button>
    </div>
  </div>
</template>

<script>
import AMapLoader from "@amap/amap-jsapi-loader";

export default {
  name: "GdMap",
  props: {
    // 传入的经纬度
    lnglat: {
      type: Array,
      default: () => [], // 默认中心点
    },
    readOnly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      AMap: null,
      map: null, // 地图实例
      marker: null, // 标记点
      mapLoading: false, // 地图加载状态
      geoCoder: null, // 地理编码实例

      dataSave: null,
      saveFlag: true,
    };
  },
  mounted() {
    this.initMap();
  },
  methods: {
    // 初始化地图
    initMap() {
      this.mapLoading = true;

      window._AMapSecurityConfig = {
        securityJsCode: "d747acb84a39e989d49e8d9aca53b573", // 安全密钥
      };

      AMapLoader.load({
        key: "b3aa6f7ba8d5a0fc1a51a0ab420ddf7b", // 申请好的Web端开发者Key
        version: "2.0", // 指定要加载的 JSAPI 的版本
        plugins: [
          "AMap.ToolBar",
          "AMap.Scale",
          "AMap.OverView",
          "AMap.MapType",
          "AMap.Geolocation",
          "AMap.Geocoder",
        ], // 需要使用的插件列表
      })
        .then((AMap) => {
          this.AMap = AMap;
          const container = document.querySelector("#mapContent");
          this.map = new AMap.Map(container, {
            center: [117.402167, 26.33612978], // 初始化地图中心点位置
            viewMode: "2D", // 是否为3D地图模式
            zoom: 16, // 初始化地图级别
          });

          // 添加工具条控件、比例尺控件和地图类型控件
          this.map.addControl(new AMap.ToolBar());
          this.map.addControl(new AMap.Scale());
          this.map.addControl(
            new AMap.MapType({
              defaultType: 1,
              showRoad: true,
            })
          );

          if (this.lnglat && this.lnglat.length > 0) {
            this.marker = new AMap.Marker({
              map: this.map,
              position: this.lnglat,
            });
            this.saveFlag = false
          }
          // 初始化标记点

          // 初始化地理编码实例
          this.geoCoder = new AMap.Geocoder({
            city: "350", // 城市设为北京，默认：“全国”
            radius: 1000, // 范围，默认：500
          });
          if (!this.readOnly) {
            this.map.on("click", (e) => {
              this.updateMarker(e.lnglat);
              this.getAddressAndCode(e.lnglat);
            });
          }
          // 添加地图点击事件

          this.mapLoading = false;
        })
        .catch((e) => {
          console.error("地图加载失败", e);
          this.mapLoading = false;
        });
    },

    // 更新标记点
    updateMarker(lnglat) {
      if (this.marker) {
        this.marker.setMap(null); // 清除旧标记点
      }
      this.marker = new this.AMap.Marker({
        map: this.map,
        position: lnglat,
      });
      this.saveFlag = false
    },

    clear() {
      if (this.marker) {
        this.marker.setMap(null); // 清除旧标记点
      }
      this.saveFlag = true
      this.$emit("address-change", {
        lnglat: null,
        address: null,
      });
      // this.$message({
      //   message: "清除成功",
      //   type: "success",
      // });
    },

    saveData() {
      this.$message({
        message: "坐标点保存成功",
        type: "success",
      });
      this.$emit("address-change", {
        lnglat:this.dataSave.lnglat,
        address: this.dataSave.address,
      });
    },

    // 获取地址和编码信息
    getAddressAndCode(lnglat) {
      this.geoCoder.getAddress(lnglat, (status, result) => {
        if (status === "complete" && result.regeocode) {
          // 将结果通过事件传递给父组件
          this.dataSave={lnglat:lnglat, address:result.regeocode.formattedAddress};
        } else {
          console.error("根据经纬度查询地址失败");
        }
      });
    },
  },
};
</script>

<style scoped>
#mapContent {
  width: 100%;
  height: 100%;
}
</style>

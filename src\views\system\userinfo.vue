<template>
  <div>
    <basic-container>
      <!-- <avue-form :option="option" ref="form" v-model="form" @tab-click="handleTabClick" :upload-before="uploadBefore" :upload-after="uploadAfter" :upload-delete="uploadDelete" @submit="handleSubmit">
        <template slot-scope="{size}" slot="menuForm">
            <el-button type="primary" icon="el-icon-check" :size="size" @click="handleChange">提 交</el-button>
            <el-button  :size="size" icon="el-icon-delete" @click="$refs.form.resetForm()">清 空</el-button>
        </template>
      </avue-form> -->
      <avue-tabs :option="optionTab" @change="handleChange"></avue-tabs>
      <avue-form v-if="index" :option="option1" ref="form" v-model="form" :upload-before="uploadBefore" :upload-after="uploadAfter" :upload-delete="uploadDelete" @submit="handleSubmit">
        <!-- <template slot-scope="{size}" slot="menuForm">
          <el-button type="primary" icon="el-icon-check" :size="size" @click="handleChange">提 交</el-button>
          <el-button :size="size" icon="el-icon-delete" @click="$refs.form.resetForm()">清 空</el-button>
        </template> -->
      </avue-form>
      <avue-form v-else :option="option2" ref="form" v-model="form" :upload-before="uploadBefore" :upload-after="uploadAfter" :upload-delete="uploadDelete" @submit="handleSubmit">
        <!-- <template slot-scope="{size}" slot="menuForm">
          <el-button type="primary" icon="el-icon-check" :size="size" @click="handleChange">提 交</el-button>
          <el-button :size="size" icon="el-icon-delete" @click="$refs.form.resetForm()">清 空</el-button>
        </template> -->
      </avue-form>
    </basic-container>
  </div>
</template>

<script>
// import option from "@/option/user/info";
import { getUserInfo, updateInfo, updatePassword } from "@/api/system/user";
import md5 from 'js-md5';
// import func from "@/util/func";
import { sensitive } from '@/util/sensitive';
import { noSpace, checkChinese, isKeyBoardContinuousChar, passWordLimit } from '@/util/validate'


export default {
  data() {
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入密码'))
      } else {
        const keyBoard = isKeyBoardContinuousChar(value)
        const passWord = passWordLimit(value)
        const chinese = checkChinese(value)
        const isNoSpace = noSpace(value)
        // console.log(keyBoard, passWord, chinese)
        if (keyBoard === true) {
          callback(new Error('密码不能含有键盘排序'))
        } else if (passWord === false) {
          callback(new Error('至少包含大写字母、小写字母、数字、特殊字符中的三类字符'))
        } else if (isNoSpace === false) {
          callback(new Error('密码不能含有空格'))
        } else if (chinese === true) {
          callback(new Error('密码不能含有中文'))
        } else {
          callback()
        }
      }
    }

    const validatePass2 = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.form.newPassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      optionTab: {
        // stretch:true,
        // beforeLeave: (active) => {
        //   console.log(active)
        // },
        // position: 'left',//left|right|top|bottom
        column: [{
          label: '个人信息',
          prop: 0,
        }, {
          label: '修改密码',
          prop: 1,
        }]
      },
      index: 0,
      option2: {
        tabs: false,
        tabsActive: 1,
        submitBtn: true,
        emptyBtn: true,
        column: [{
          label: '头像',
          type: 'upload',
          listType: 'picture-img',
          propsHttp: {
            res: 'data',
            url: 'link',
          },
          canvasOption: {
            text: ' ',
            ratio: 0.1
          },
          action: '/api/blade-resource/oss/endpoint/put-file-attach',
          tip: '只能上传jpg/png用户头像，且不超过1M',
          span: 12,
          row: true,
          prop: 'avatar'
        }, {
          label: '姓名',
          span: 12,
          row: true,
          prop: 'realName',
          rules: [{
            required: true,
            message: '请输入姓名',
            trigger: 'blur'
          }]
        }, {
          label: '用户名',
          span: 12,
          row: true,
          prop: 'name'
        }, {
          label: '手机号',
          span: 12,
          row: true,
          disabled: true,
          prop: 'phone'
        }],
      },
      option1: {
        tabs: false,
        tabsActive: 1,
        submitBtn: true,
        emptyBtn: true,
        column: [{
          label: '原密码',
          span: 12,
          row: true,
          display: true,
          type: 'password',
          prop: 'oldPassword',
          rules: [
            { required: true, message: '请输入原密码', trigger: 'blur' },
          ],
        }, {
          label: '新密码',
          span: 12,
          row: true,
          display: true,
          type: 'password',
          prop: 'newPassword',
          rules: [
            { min: 8, max: 16, message: '密码长度在8到16个字符' },
            { required: true, validator: validatePassword, trigger: "change" },
          ],
        }, {
          label: '确认密码',
          span: 12,
          row: true,
          display: true,
          type: 'password',
          prop: 'newPassword1',
          rules: [
            { required: true, validator: validatePass2, trigger: "change" },
          ],
        }]
      },
      form: {},
      avatarId: '',
      sensitiveManager: null
    };
  },
  created() {
    this.handleWitch();
  },

  methods: {
    handleChange(column) {
      console.log(column, "column");
      this.index = column.prop
      this.$refs.form.$refs.form.clearValidate()
      // this.$message.success(JSON.stringify(column))
    },
    handleSubmit(form, done) {
      console.log(form, "form");
      if (this.index === 0) {
        this.form.avatarId = this.avatarId
        // const submitData = this.sensitiveManager.getSubmitData(form);
        // console.log(submitData);
        let submitData = {
          avatarId: this.avatarId,
          name: form.name,
          realName: form.realName,
        }
        updateInfo(submitData).then(res => {
          if (res.data.success) {
            this.$message({
              type: "success",
              message: "修改信息成功!"
            });
          } else {
            this.$message({
              type: "error",
              message: res.data.msg
            });
          }
          done();
        }, error => {
          window.console.log(error);
          done();
        })
      } else {
        updatePassword(md5(form.oldPassword), md5(form.newPassword), md5(form.newPassword1)).then(res => {
          if (res.data.success) {
            this.$message({
              type: "success",
              message: "修改密码成功!"
            });
          } else {
            this.$message({
              type: "error",
              message: res.data.msg
            });
          }
          done();
        }, error => {
          window.console.log(error);
          done();
        })
      }
    },
    handleWitch() {
      // 创建脱敏工具实例
      this.sensitiveManager = sensitive.create({
        fields: ['phone', 'email'], // 配置需要脱敏的字段
      });
      if (this.index === 0) {
        getUserInfo().then(res => {
          // console.log(res,"ssss");
          const user = res.data.data;
          this.form = {
            id: user.id,
            avatar: user.avatar,
            name: user.name,
            realName: user.realName,
            phone: user.phone,
            email: user.email,
          };
          // 保存初始脱敏数据
          this.sensitiveManager.saveInitialData(this.form);
        });
      }
    },
    uploadBefore(file, done, loading) {
      console.log(file)
      const isJPGorPNG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt1M = file.size / 1024 / 1024 < 5;
      if (!isJPGorPNG) {
        this.$message.error('只能上传JPEG/PNG格式的图片!');
        loading()
      }
      else if (!isLt1M) {
        this.$message.error('上传图片大小不能超过1M!');
        loading()
      }
      else {
        done()
      }
    },
    uploadAfter(res, done) {
      console.log(res)
      // this.form.avatar = res.link
      this.avatarId = res.attachId
      done()
    },
    uploadDelete(file, column) {
      // console.log(file,column,this.form)
      this.form.avatar = ""
      this.avatarId = ''
      return Promise.resolve()
    },
    handleTabClick(tabs) {
      this.index = 0;
      this.handleWitch();
    }
  }
};
</script>

<style>
</style>

<template>
  <basic-container>
    <avue-crud :option="option" :defaults.sync="defaults" :table-loading="loading" :data="data" :search.sync="query" ref="crud" v-model="form" :permission="permissionList" :before-open="beforeOpen" :before-close="beforeClose" @row-del="rowDel" @row-update="rowUpdate" @row-save="rowSave" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="primary" size="small" icon="el-icon-plus" v-if="permission.menu_add" @click="handleAddLevel">新 增</el-button>
        <el-button type="danger" size="small" icon="el-icon-delete" v-if="permission.menu_delete" @click="handleDelete">删 除</el-button>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button type="text" icon="el-icon-circle-plus-outline" size="small" @click.stop="handleAdd(scope.row,scope.index)" v-if="scope.row.parentId === 0">新增子项</el-button>
      </template>
      <template slot-scope="{type}" slot="iconIdForm">
        <el-upload class="avatar-uploader" :disabled="type=='view'" :http-request="uploadFile" :on-remove="handleElUploadRemove" :show-file-list="true" :on-success="handleElUploadSuccess" :before-upload="beforeAvatarUpload" :on-exceed="handleExceed" :file-list="imageFileList" list-type="picture-card" :limit="1" accept="image/jpeg,image/png">
          <i class="el-icon-plus"></i>
        </el-upload>
        <span v-if="type!='view'">请上传大小不超过 1MB，格式为JPEG/PNG的图片</span>
      </template>
      <template slot-scope="{row}" slot="iconId">
        <div style="text-align:center">
          <el-image v-if="row.iconLink" :src="row.iconLink" :preview-src-list="[row.iconLink]" style="width: 40px; height: 40px; cursor: pointer;" fit="cover" />
          <i v-else :class="row.iconId" />
        </div>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList, remove, update, save, detail, informationColumnSelect } from "@/api/infoRelease/manage";
import { mapGetters } from "vuex";
import iconList from "@/config/iconList";
import request from '@/router/axios';
export default {
  data() {
    return {
      form: {},
      query: {},
      defaults: {},
      loading: true,
      selectionList: [],
      parentId: 0,
      option: {
        height: 'auto',
        calcHeight: 30,
        lazy: false,
        tip: false,
        simplePage: true,
        searchShow: true,
        searchMenuSpan: 6,
        dialogWidth: "60%",
        tree: true,
        border: false,
        index: false,
        selection: true,
        viewBtn: true,
        menuWidth: 300,
        dialogClickModal: false,
        addBtn: false,
        labelWidth: 120,
        column: [
          {
            label: "上级栏目",
            prop: "parentId",
            type: "tree",
            dicData: [],
            hide: true,
            addDisabled: false,
            props: {
              label: "name",
              value: "id",
              // da
            },
            nodeClick: (data) => {
              // console.log(data)
              const column = this.findObject(this.option.column, "iconId");
              if (data.type == 2) {
                column.display = false;
              } else {
                column.display = true;
              }
            },
            rules: [
              {
                required: false,
                message: "请选择上级栏目",
                trigger: "click"
              }
            ],
          },
          {
            label: "栏目名称",
            prop: "name",
            search: true,
            maxlength: 100,
            rules: [
              {
                required: true,
                message: "请输入栏目名称",
                trigger: "blur"
              }
            ]
          },
          {
            label: "栏目类型",
            prop: "type",
            type: "select",
            dataType: "string",
            dicUrl: "/api/blade-system/dict/dictionary?code=column_type",
            dicFlag: true,
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            hide: false,
            display: true,
            rules: [
              { required: true, message: "请选择栏目类型", trigger: "change" }
            ]
          },


          {
            label: "栏目图标",
            prop: "iconId",
            formslot: true,
            display: true,
            span: 24,
            rules: [
              {
                required: true,
                message: '请上传图标',
                trigger: 'change'
              }
            ]
          }
        ]
      },
      data: [],
      imageFileList: [],
      iconList: iconList,
      maps: new Map()
    };
  },
  watch: {
    "form.parentId"() {
      // if(!this.form.parentId){
      this.$refs.crud.option.column.filter(item => {
        if (item.prop === "type") {
          item.display = this.form.parentId == ""
        }
      });
      // }
    },
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    // permissionList() {
    //   return {
    //     addBtn: this.vaildData(this.permission.manage_add, false),
    //     viewBtn: this.vaildData(this.permission.manage_view, false),
    //     delBtn: this.vaildData(this.permission.manage_delete, false),
    //     editBtn: this.vaildData(this.permission.manage_edit, false)
    //   };
    // },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    // vaildData(val, def) {
    //   return typeof val !== "undefined" && val !== null ? val : def;
    // },
    findObject(list, prop) {
      return list.find(item => item.prop === prop);
    },
    initData() {
      informationColumnSelect().then(res => {
        const column = this.findObject(this.option.column, "parentId");
        column.dicData = res.data.data;
      });
    },
    handleAddLevel() {
      this.$refs.crud.rowAdd();
    },
    handleAdd(row) {
      console.log(row, 'row');
      this.form.parentId = row.id;
      const column = this.findObject(this.option.column, "parentId");
      column.addDisabled = true;
      const column1 = this.findObject(this.option.column, "type");
      column1.display = false;
      const column2 = this.findObject(this.option.column, "iconId");
      if (row.type == 2) {
        column2.display = false;
      } else {
        column2.display = true;
      }
      this.$refs.crud.rowAdd();

    },
    rowSave(row, done, loading) {
      console.log(row, 'row');
      let param = {
        parentId: row.parentId,
        name: row.name,
        type: row.type,
        iconId: row.iconId,
      }

      save(param).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        // window.location.reload()
        this.onLoad();
        this.$store.dispatch("GetMenu")
        done();
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowUpdate(row, index, done, loading) {
      let param = {
        id: row.id,
        parentId: row.parentId,
        name: row.name,
        type: row.type,
        iconId: row.iconId,
      }
      update(param).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!"
        });

        this.onLoad();
        this.$store.dispatch("GetMenu")
        // window.location.reload()
        done();
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowDel(row, index, done) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id, 2)
        })
        .then(() => {
          this.$store.dispatch("GetMenu")
          // window.location.reload()
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done(row);
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids, 2);
        })
        .then(() => {
          this.data = [];
          this.parentId = 0;
          this.$refs.crud.refreshTable();
          this.$refs.crud.toggleSelection();
          this.onLoad();
          this.$store.dispatch("GetMenu")
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          window.location.reload()
        });
    },
    searchReset() {
      this.query = {};
      this.parentId = "";
      this.onLoad();
    },
    searchChange(params, done) {
      this.query = params;
      this.parentId = '';
      this.onLoad();
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    beforeOpen(done, type) {
      this.imageFileList = [];
      if (["add", "edit", "view"].includes(type)) {
        this.initData();
      }
      if (["edit", "view"].includes(type)) {
        console.log(this.form);
        // const column = this.findObject(this.option.column, "iconId");
        // if (this.form.type == 2) {
        //   column.display = false;
        // } else {
        //   column.display = true;
        // }
        detail(this.form.id).then(res => {
          let data = res.data.data
          if (data.iconLink) {
            this.imageFileList = [{
              url: data.iconLink,
              status: 'done',
            }];
          }
          this.form = data;
        });
      }
      done();
    },

    beforeClose(done) {
      const column = this.findObject(this.option.column, "parentId");
      column.addDisabled = false;
      const column1 = this.findObject(this.option.column, "type");
      column1.display = true;
      const column2 = this.findObject(this.option.column, "iconId");
      column2.display = true;
      done();
    },



    refreshChange() {
      this.onLoad();
      this.$refs.crud.refreshTable();
    },
    onLoad() {
      this.loading = true;
      // console.log(Object.assign({}, params, this.query), params,this.query,"ssss")
      let params = {
        name: this.query.name
      };
      getList(params).then(res => {
        this.data = res.data.data;
        this.loading = false;
        this.selectionClear();
      });
    },

    customUpload(options) {
      const formData = new FormData();
      formData.append('file', options.file);
      this.$http.post('/api/blade-resource/oss/endpoint/put-file-attach', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        if (res.data.success) {
          options.onSuccess(res.data);
        } else {
          options.onError(new Error(res.data.msg || '上传失败'));
        }
      }).catch(err => {
        options.onError(err);
      });
    },
    async uploadFile(param) {
      // param.file就是上传文件本身
      // console.log(param, "param");
      const formData = new FormData()
      formData.append('file', param.file)
      //  console.log(this.imageFileList);
      this.imageFileList = [{
        url: "",
        name: param.file.name,
        percentage: 0,
        status: "uploading",
        size: param.file.size,
        type: param.file.type,
        uid: param.file.uid,
      }];
      // 发起请求
      request({
        method: 'post',
        // 上传地址，因为我这里的request请求是自己封装过的，所以就只需要填写接口后面的地址即可
        url: '/api/blade-resource/oss/endpoint/put-file-attach',
        data: formData,
        // 重点一：complete就是处理后的上传进度数值1-100
        onUploadProgress: progressEvent => {
          // console.log(progressEvent)
          const complete = parseInt(
            ((progressEvent.loaded / progressEvent.total) * 100) | 0,
            10
          )
          // 重点二：onProgress()方法需要以上方接收的形参来调用
          // 这个方法有一个参数"percent"，给他进度值 complete 即可
          param.onProgress({ percent: complete })
        },
        // onSuccess: function (response) {
        // // 成功响应时触发，处理on-success事件
        // console.log('上传成功：', response);
        // },
      }).then(res => {
        // console.log(res, "res");
        if (res.data.success) {
          this.$message.success("上传成功");
          this.form.iconId = res.data.data.attachId;
          this.$refs.crud.validateField("iconId");
          this.imageFileList[0].status = "done"
          this.imageFileList[0].url = res.data.data.link;
          // console.log(this.imageFileList);
          // this.imageFileList = [file];
          // param.onSuccess(res.data);
          // this.imageFileList=[]
        } else {
          // param.onError(new Error(res.data.msg || '上传失败'));
        }
      }).catch(err => {
        param.onError(new Error(err.data.msg || '上传失败'));
      });
    },
    // handleElUploadSuccess(res, file) {
    //   console.log(res,file,"res file");
    //   if (res && res.data && res.data.attachId) {
    //     this.form.source = res.data.attachId;
    //     this.$refs.crud.validateField("source");
    //     this.imageFileList = [{
    //       url: (res.data.link || file.url),
    //       status: 'done',
    //     }];
    //   } else {
    //     this.form.source = ''
    //     this.imageFileList = [];
    //   }
    // },
    handleElUploadRemove() {
      this.form.iconId = ''
      this.imageFileList = [];
    },
    beforeAvatarUpload(file) {
      // console.log(file)
      const isJPGorPNG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (!isJPGorPNG) {
        this.$message.error('只能上传JPEG/PNG格式的图片!');
        return false;
      }
      if (!isLt1M) {
        this.$message.error('上传图片大小不能超过1M!');
        return false;
      }
      return true;
      // return isJPGorPNG && isLt1M;
    },
    handleExceed() {
      this.$message.warning('最多只能上传1张图片');
    },
  }
};
</script>

<style>
</style>

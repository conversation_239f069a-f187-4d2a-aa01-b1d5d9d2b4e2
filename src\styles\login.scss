// 登录页面样式
.login-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

// 背景图片
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/img/login/bgimg.png');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 1;

  // 添加一个半透明遮罩层，让表单更突出
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0);
    z-index: 1;
  }
}

// Logo和标题容器 - 左上角
.login-header {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  z-index: 3;
}

// Logo样式
.login-logo {
  img {
    width: 48px;
    height: auto;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  }
}

// 标题图片样式
.login-title-img {
  img {
    width: 200px;
    height: auto;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  }
}

// 浮动的登录表单卡片 - 中间靠右
.login-form-card {
  position: absolute;
  top: 50%;
  right: 10%;
  transform: translateY(-50%);
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;

  // 响应式调整
  @media (max-width: 1200px) {
    right: 5%;
  }

  @media (max-width: 768px) {
    right: 50%;
    transform: translate(50%, -50%);
  }
}

// 登录表单容器
.login-form-container {
  width: 420px;
  min-height: 480px;
  height: auto;
  max-width: 90vw;
  max-height: 90vh;
  padding: 45px 60px;
  background-image: url('/img/login/form_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  // border-radius: 20px;
  // box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 3;
  overflow-y: auto;



  // 响应式设计
  @media (max-width: 768px) {
    width: 85vw;
    max-width: 380px;
    height: auto;
    min-height: 420px;
    padding: 50px 40px;
    // border-radius: 16px;
  }

  @media (max-width: 480px) {
    width: 90vw;
    height: auto;
    min-height: 380px;
    padding: 40px 30px;
    // border-radius: 12px;
  }
}

// 表单标题
.form-title {
  font-size: 40px;
  font-weight: 600;
  color: #012455;
  text-align: left;
  margin-bottom: 40px;
  letter-spacing: 1px;

  @media (max-width: 480px) {
    font-size: 24px;
    margin-bottom: 32px;
  }
}

// 登录表单
.login-form {
  .el-form-item {
    margin-bottom: 28px;

    .el-form-item__label {
      font-size: 16px;
      color: #1a365d;
      font-weight: 500;
      margin-bottom: 8px;
      line-height: 1.5;
    }
  }

  // 普通输入框样式
  .login-input {
    .el-input__inner {
      height: 40px;
      line-height: 40px;
      border: 1px solid #e2e8f0;
      border-radius: 6px;
      padding: 0 20px;
      font-size: 20px;
      background: #f7fafc;
      transition: all 0.3s ease;
      color: #1a365d;

      &:focus {
        border-color: #4299e1;
        background: #fff;
        box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
      }

      &::placeholder {
        color: #a0aec0;
        font-size: 14px;
      }
    }

    // 密码显示/隐藏图标样式
    .el-input__suffix {
      .el-input__suffix-inner {
        .el-input__icon {
          color: #a0aec0;
          font-size: 18px;

          &:hover {
            color: #4299e1;
          }
        }
      }
    }
  }
}

// 验证码表单项
.code-form-item {
  // 强制垂直布局
  display: flex !important;
  flex-direction: column !important;

  .el-form-item__label {
    margin-bottom: 8px !important;
    text-align: left !important;
  }

  .el-form-item__content {
    margin-left: 0 !important;
  }

  // 验证码输入容器
  .code-input-container {
    display: flex;
    gap: 16px;
    align-items: center;
  }
}

// 验证码输入框
.code-input {
  flex: 1;

  .el-input__inner {
    height: 40px;
    line-height: 40px;
    border: 1px solid #e2e8f0;
    border-radius: 5px;
    padding: 0 20px;
    font-size: 16px;
    background: #f7fafc;
    transition: all 0.3s ease;
    color: #2d3748;

    &:focus {
      border-color: #4299e1;
      background: #fff;
      box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
    }

    &::placeholder {
      color: #a0aec0;
      font-size: 14px;
    }

    &::placeholder {
      color: #c0c4cc;
    }
  }
}

// 发送验证码按钮
.send-code-btn {
  height: 38px;
  padding: 0 20px;
  font-size: 14px;
  border-radius: 6px;
  white-space: nowrap;
  min-width: 120px;

  &.is-disabled {
    background-color: #f5f5f5 !important;
    border-color: #e4e7ed !important;
    color: #c0c4cc !important;
    cursor: not-allowed !important;
  }
}

// 验证码容器
.captcha-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 120px;
}

// 验证码图片
.captcha-image {
  width: 120px;
  height: 38px;
  border: 1px solid #e2e8f0;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #f7fafc;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #a0aec0;

  &:hover {
    border-color: #4299e1;
    background: #fff;
  }
}

// 刷新验证码文字
.refresh-text {
  font-size: 12px;
  color: #4A90E2;
  cursor: pointer;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

// 登录按钮
.login-btn {
  width: 100%;
  height: 52px;
  background: linear-gradient(135deg, #419EFF 0%, #419EFF 100%);
  border: none;
  border-radius: 5px;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 1px;
  transition: background 0.3s ease-in-out, transform 0.3s ease, box-shadow 0.3s ease;
  margin-top: 16px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  will-change: transform, box-shadow;

  &:hover {
    background: linear-gradient(135deg, #3182ce 0%, rgb(13, 118, 246) 100%);
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(115, 175, 223, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}

// 忘记密码链接
.forgot-password {
  text-align: right;
  margin-top: 16px;

  a {
    color: #4299e1;
    font-size: 14px;
    text-decoration: none;
    transition: color 0.3s ease;

    &:hover {
      color: #357ABD;
      text-decoration: underline;
    }
  }
}

// 短信验证码提示
.sms-tip {
  margin-bottom: 24px;
  padding: 16px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;

  p {
    margin: 0;
    color: #0369a1;
    font-size: 14px;
    line-height: 1.5;
    text-align: center;
  }
}

// 重新发送按钮样式（当在同一行时，复用发送验证码按钮样式）
.resend-btn {
  width: 100%;
  height: 40px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  background: transparent !important;
  border: 1px solid #409eff !important;
  color: #409eff !important;
  margin-bottom: 16px;
  transition: all 0.3s ease;

  &:hover:not(.is-disabled) {
    background: #ecf5ff !important;
    border-color: #66b1ff !important;
    color: #66b1ff !important;
  }

  &.is-disabled {
    background: #f5f7fa !important;
    border-color: #e4e7ed !important;
    color: #c0c4cc !important;
    cursor: not-allowed !important;
  }
}


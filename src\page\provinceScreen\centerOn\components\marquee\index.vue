<!--
 * @Description: 左右文字滚动-公共组件
 * @Author: chenz76
 * @Date: 2021-12-30 10:57:10
 * @LastEditors: chenz76
 * @LastEditTime: 2022-01-11 16:42:07
-->
<template>
  <div class="my-outbox">
    <div class="my-inbox" ref="box">
      <div
        class="my-list"
        v-for="(item, index) in sendVal"
        :key="index"
        ref="list"
      >
        <span class="my-uname">{{ item.detail }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "my-marquee-left",
  props: {
    sendVal: Array,
  },
  data() {
    return {
      // 定时器标识
      nowTime: null,
      // 每一个内容的宽度
      disArr: [],
    };
  },
  mounted() {
    // var that = this
    var item = this.$refs.list;
    var len = this.sendVal.length;
    var arr = [];
    // 因为设置的margin值一样，所以取第一个就行。
    var margin = this.getMargin(item[0]);
    for (var i = 0; i < len; i++) {
      arr.push(item[i].clientWidth + margin); // 把宽度和 margin 加起来就是每一个元素需要移动的距离
    }
    this.disArr = arr;
    this.moveLeft();
  },
  beforeDestroy() {
    // 页面关闭清除定时器
    clearInterval(this.nowTime);
    // 清除定时器标识
    this.nowTime = null;
  },
  methods: {
    // 获取margin属性
    getMargin(obj) {
      var marg = window.getComputedStyle(obj, null)["margin-right"];
      marg = marg.replace("px", "");
      return Number(marg); // 强制转化成数字
    },
    // 移动的方法
    moveLeft() {
      var that = this;
      var outbox = this.$refs.box;
      // 初始位置
      var startDis = 0;
      this.nowTime = setInterval(function () {
        startDis -= 0.5;
        if (Math.abs(startDis) > Math.abs(that.disArr[0])) {
          // 每次移动完一个元素的距离，就把这个元素的宽度
          that.disArr.push(that.disArr.shift());
          // 每次移动完一个元素的距离，就把列表数据的第一项放到最后一项
          that.sendVal.push(that.sendVal.shift());
          startDis = 0;
        }
        outbox.style = `transform: translateX(${startDis}px)`;
      }, 1000 / 60);
    },
  },
};
</script>

<style lang="scss" scoped>
.my-outbox {
  overflow: hidden;
  color: #ffffff;
  height: 35px;
  .my-inbox {
    white-space: nowrap;
    .my-list {
      margin-right: 25px;
      display: inline-block;
      font-size: 18px;
      height: 40px;
      line-height: 40px;
      .my-uname {
        color: #ffffff;
      }
    }
  }
}
</style>
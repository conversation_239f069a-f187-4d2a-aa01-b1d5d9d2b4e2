import request from '@/router/axios';

export const uploadData = (data) => {
  return request({
    url: '/api/trackData/track',
    method: 'post',
    data: {
      ...data,
      platform: 1
    }
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/api/trackData/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/trackData/detail',
    method: 'get',
    params: {
      id,
    }
  })
}
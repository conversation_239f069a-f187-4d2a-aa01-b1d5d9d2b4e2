/*
 * @Date: 2025-07-23 16:55:12
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-23 17:08:49
 * @Description:
 * @FilePath: \src\api\infoRelease\manage.js
 */
import request from '@/router/axios';

/**
 * @description: 请求列表
 * @param {object} params
 * @author:
 */
export const getList = (params) => {
  return request({
    url: '/api/admin/informationColumn/tree',
    method: 'get',
    params: {
      ...params
    }
  })
}

export const informationColumnSelect = (params) => {
  return request({
    url: '/api/admin/informationColumn/select',
    method: 'get',
    params: {
      ...params
    }
  })
}

/**
 * @description: 新增
 * @param {object} data
 * @author:
 */
export const save = (data) => {
  return request({
    url: '/api/admin/informationColumn/save',
    method: 'post',
    data,
  })
}

/**
 * @description: 查看详情
 * @param {object} params
 * @author:
 */
export const detail = (id) => {
  return request({
    url: '/api/admin/informationColumn/detail',
    method: 'get',
    params:{
      id
    },
  })
}

/**
 * @description: 修改
 * @param {object} data
 * @author:
 */
export const update = (data) => {
  return request({
    url: '/api/admin/informationColumn/update',
    method: 'post',
    data,
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/admin/informationColumn/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

/***********common**************/
.font-size-styleA {
  font-size: 2.5vh;
  color: #b6d7ff;
}

.font-size-styleB {
  font-size: 1.8vh;
  color: #b6d7ff;
}

.font-size-styleC {
  font-size: 2.5vh;
}

.pointer-events-auto {
  pointer-events: auto;
}

/***********common**************/

.province-screen {
  position: fixed;
  min-width: 1200px;
  width: 100%;
  height: 100vh;
  // background-color: #05072C !important;
  background-image: url("/img/privinceScreen/pageBg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  //头
  .title-header {
    position: absolute;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    width: 80%;
    left: 45%;
    transform: translate(-45%, 0);

    .title-back {
      position: absolute;
      width: 80%;
      left: 11%;
      height: 12vh;
      margin-top: -0.5vh;
    }
  }

  .title-back-desc {
    position: absolute;
    width: 50%;
    z-index: 500;
    left: 50.5%;
    transform: translate(-50.5%, 0);
    text-align: center;
    margin-top: 1vh;
    font-size: 3.2vh;
    color: #fff;
    height: 4.5vh;
    font-weight: bold;
    letter-spacing: 10px;
    word-wrap: nowrap;
  }

  //左边时间
  .screen-time {
    position: absolute;
    width: 23%;
    left: 1%;
    margin-top: 3vh;
    .screen-time-back {
      position: absolute;
      width: 100%;
      height: 6vh;
    }

    .screen-time-desc {
      position: absolute;
      width: 60%;
      text-align: center;
      font-size: 1.8vh;
      margin-top: 1.5vh;
      font-weight: bold;
      margin-left: 2%;
      color: #1da9d7;
      height: 5vh;
      letter-spacing: 2px;
    }
  }
  //右边地点
  .screen-local {
    position: absolute;
    width: 23%;
    right: 1%;
    margin-top: 3vh;
    .screen-local-back {
      position: absolute;
      width: 100%;
      height: 6vh;
      transform: rotateY(180deg);
    }
    .screen-local-desc {
      position: absolute;
      width: 60%;
      text-align: center;
      margin-top: 1.5vh;
      margin-left: 40%;
      height: 5vh;
      .screen-local-img {
        width: 10%;
        margin-top: -0.3vh;
      }
      .screen-local-title {
        font-weight: bold;
        font-size: 2vh;

        letter-spacing: 2px;
        color: #1da9d7;
        vertical-align: top;
      }
    }
  }

  .screen-left {
    position: absolute;
    left: 1%;
    width: 26%;
    top: 8vh;

    .screen-left-all {
      position: absolute;
      width: 100%;
      height: 91vh;
      background-color: rgba(1, 7, 41, 0.7);
      border: 1px #235fa7 solid;
      .left-on-back {
        position: absolute;
        left: 0%;
        width: 100%;
        height: 86vh;
      }
      //左上
      .left-on {
        position: absolute;
        width: 92%;
        left: 4%;
      }
      //左中
      .left-center {
        position: absolute;
        top: 29vh;
        width: 92%;
        left: 4%;
      }
      //左下
      .left-bottom {
        position: absolute;
        top: 59vh;
        width: 92%;
        left: 4%;
      }
    }
  }

  .screen-center {
    position: absolute;
    width: 48%;
    transform: translate(-50%, 0);
    left: 50%;
    top: 9vh;
    //中上
    .center-on {
      position: absolute;
      width: 90%;
      left: 5%;
      height: 55vh;
    }
    //中下
    .center-bottom {
      position: absolute;
      border: 1px #235fa7 solid;
      background-color: rgba(1, 7, 41, 0.7);
      top: 61vh;
      height: 29vh;
      width: 92%;
      left: 3%;
      padding: 0 1%;
    }
  }

  .screen-right {
    position: absolute;
    right: 1%;
    width: 26%;
    top: 8vh;
    height: 91vh;
    border: 1px #235fa7 solid;
    background-color: rgba(1, 7, 41, 0.7);
    .right-img {
      position: absolute;
      left: 0%;
      width: 100%;
      height: 86vh;
    }

    //右上
    .right-on {
      position: absolute;
      left: 5%;
      width: 90%;
    }
    //右中
    .right-center {
      position: absolute;
      top: 29vh;
      left: 5%;
      width: 90%;
    }
    //右下
    .right-bottom {
      position: absolute;
      top: 59vh;
      left: 5%;
      width: 90%;
    }
  }
}

<!--
 * @Description: 左上-领导班子
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenn26
 * @LastEditTime: 2023-06-05 10:16:51
-->
<template>
  <div class="town-left-on-content">
    <div class="header">
      <img src="/img/screen/title-decorate-left.png" class="left-decorate" alt="" />
      <span class="title">党组织风采</span>
      <img src="/img/screen/title-decorate-left.png" class="right-decorate" alt="" />
    </div>
    <div class="content" @mouseenter="stopTimer" @mouseleave="startTimer">
      <div class="content-value">
        <el-carousel :interval="5000" type="card" height="18vh" indicator-position="none">
          <el-carousel-item v-for="(item, index) in showList" :key="index">
            <img :src="item.avatar" class="content-img" alt="" />
            <div class="content-info">
              <span class="content-name" :title="item.name">{{
                item.name
              }}</span>
              &nbsp;&nbsp;&nbsp;&nbsp;
              <span class="content-post" :title="item.position">{{
                item.position
              }}</span>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="content-desc">
        <img src="/img/townScreen/descbox.png" alt="left" width="100%" height="100%" />
        <p class="desc">{{ townBrachDesc }}</p>
      </div>
    </div>
  </div>
</template>
<script>
import "./index.scss";
export default {
  props: {
    townLeaderList:{
      type: Object,
      default:()=>{
        return {}
      }
    },
    townBrachDesc: String
  },
  data() {
    return {
      selectStatus: 0,
      showList: [],
      timer: null,
      flag: false,
    };
  },
  watch:{
    'townLeaderList'(val){
      if(val!==''){
        this.showList = this.townLeaderList;
      }
    }
  },
  destroyed() {
    this.stopTimer();
  },
  methods: {
    carouselRun() {
      this.showList = this.townLeaderList;
    },
    stopTimer() {
      if (this.timer) clearInterval(this.timer);
    },
    startTimer() {
      this.timer = setInterval(() => {
        this.carouselRun();
      }, 5 * 1000);
    },
  },
};
</script>

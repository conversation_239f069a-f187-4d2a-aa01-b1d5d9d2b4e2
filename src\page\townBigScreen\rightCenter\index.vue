<!--
 * @Author: chenn26
 * @Date: 2023-05-10 16:23:14
 * @LastEditors: chenn26
 * @LastEditTime: 2023-05-22 20:23:37
 * @Description: 右中-乡镇风采
-->

<template>
  <div class="town-right-center-content">
    <div class="header">
      <img
        src="/img/screen/title-decorate-left.png"
        class="left-decorate"
        alt=""
      />
      <span class="title">乡镇风采</span>
      <img
        src="/img/screen/title-decorate-left.png"
        class="right-decorate"
        alt=""
      />
    </div>
    <div class="content">
      <div class="content-value">
        <el-carousel :interval="5000" type="card" height="15vh" indicator-position="none" @change="carouselChange">
          <el-carousel-item v-for="(item, index) in scenicList" :key="index">
            <img :src="item.imageList[0].link" class="content-img" alt="" width="100%" height="100%"/>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="content-desc">
        <img src="/img/townScreen/descbox.png" width="100%" height="100%" class="left-decorate" alt="" />
        <p class="desc">
          {{ desc }}
        </p>
      </div>
    </div>
  </div>
</template>
<script>
import { getVillageScenic } from "@/api/screen/townScreen";
import "./index.scss";
export default {
  data() {
    return {
      scenicList:[],
      desc:''
    };
  },
  mounted() {
    this.getVillageScenic();
  },
  created() {
    
  },
  destroyed() {

  },
  methods: {
    getVillageScenic(){
      let params = {
        size:999,
        current:1,
        status:1
      }
      getVillageScenic(params).then(res=>{
        this.scenicList = res.data.data.records;
        this.desc = res.data.data.records[0].description;
      })
    },
    carouselChange(now){
      this.desc = this.scenicList[now].description;
    }
  }
};
</script>
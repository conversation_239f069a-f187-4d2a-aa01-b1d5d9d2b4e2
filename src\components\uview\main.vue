<!--
 * @Description: iframe嵌入uview
 * @Author: chenz76
 * @Date: 2022-03-24 10:16:29
 * @LastEditors: linqh21
 * @LastEditTime: 2025-02-08 11:07:27
-->
<template>
  <Dialog v-bind="$attrs">
    <div class="uview-iframe" v-if="loading"></div>
    <iframe :src="src" class="uview-iframe" frameborder="0" ref="myIframe"
      sandbox="allow-scripts allow-same-origin allow-modals"></iframe>
  </Dialog>
</template>
<script>
import website from '@/config/website';
import Dialog from "./dialog.vue";
import { debounce } from "lodash";
export default {
  name: "uviewIframe",
  components: { Dialog },
  props: ["propsFromData"],
  data () {
    return {
      src: website.uviewSrc,
    };
  },
  created () { },
  mounted () { },
  watch: {
    propsFromData: function () {
      this.fromData = this.propsFromData;
    },
  },
  computed: {},
  methods: {
    sendMessage: debounce(function (title = "", content = "") {
      console.log('43');

      const myIframe = this.$refs.myIframe;
      var postData = {
        eventType: "rich-text", // 所要调用ThingJS页面里的函数名
        title: title,
        content: content,
      };
      myIframe.contentWindow.postMessage(postData, this.src);
    }, 1200),
  },
};
</script>

<style lang="scss">
.uview-iframe {
  width: 350px;
  max-width: 350px;
  height: 712px;
  margin-left: 5px;
  padding: 40px 13px 12px 13px;
  border-radius: 0 0 50px 50px;
  border: 0;
  overflow: hidden;
  box-sizing: border-box;
  background-image: url("/img/iPhone13.png");
  background-repeat: no-repeat;
  background-size: 100%;
}
</style>

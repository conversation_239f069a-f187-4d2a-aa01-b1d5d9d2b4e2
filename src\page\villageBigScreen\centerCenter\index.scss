.center-center-content1 {
  position: absolute;
  width: 100%;
  height: 100%;

  .header {
    position: absolute;
    width: 100%;
    white-space: nowrap;
    top: 1vh;
    left: 50%;
    transform: translate(-50%, 0);

    .left-decorate {
      width: 40%;
    }

    .title {
      letter-spacing: 3px;
      vertical-align: top;
      margin: 0 5%;
      color: #5bffdc;
      font-size: 2vh;
    }

    .right-decorate {
      width: 40%;
      transform: rotateY(180deg);
    }
  }

  .content {
    position: absolute;
    float: left;
    width: 40%;
    margin-top: 4vh;

    .statistics {
      color: white;
      font-size: 1.3vh;
      margin-left: 2%;

      .statistics-people {
        position: absolute;
        margin-left: 16%;
        width: 80%;
        white-space: nowrap;
        .statistics-people-title {
          width: 35%;
        }

        .statistics-people-data {
          width: 60%;
          margin-left: 13%;
          text-align: center;
          letter-spacing: 1px;
          font-size: 2.2vh;
          color: #5bfedc;
        }
      }

      .statistics-peasant {
        position: absolute;
        margin-left: 16%;
        width: 80%;
        white-space: nowrap;
        .statistics-peasant-title {
          width: 35%;
        }

        .statistics-peasant-data {
          width: 60%;
          margin-left: 13%;
          text-align: center;
          letter-spacing: 1px;
          font-size: 2.2vh;
          color: #48a1ff;
        }
      }

      .statistics-party {
        position: absolute;
        margin-left: 16%;
        width: 80%;
        white-space: nowrap;
        .statistics-party-title {
          width: 35%;
        }

        .statistics-party-data {
          width: 60%;
          margin-left: 7%;
          text-align: center;
          letter-spacing: 1px;
          font-size: 2.2vh;
          color: #ff9534;
        }
      }

      //下划线
      .divider {
        position: relative;
        padding-bottom: 3vh;
        top: 4vh;
        height: 3vh;
      }

      .div-transparent:before {
        content: "";
        position: absolute;
        top: 0;
        left: 10%;
        width: 80%;
        height: 2px;
        background-image: linear-gradient(to right, transparent, rgb(45, 48, 247), transparent);
      }

      //下划线 end
    }
  }

  //echarts
  .echarts {
    position: absolute;
    top: 4vh;
    left: 43%;
    width: 60%;
  }
}

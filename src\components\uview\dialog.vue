<!--
 * @Description: 自定义模态框
 * @Author: chenz76
 * @Date: 2021-07-08 11:00:48
 * @LastEditors: chenn26
 * @LastEditTime: 2023-05-04 16:36:22
-->
<template>
  <!-- 弹窗部分 -->
    <el-dialog
      class="uview-dialog"
      custom-class="dialog-total"
      :visible.sync="dialogVisible"
      style="border-bottom: 1px solid #b4b8c1"
      append-to-body
      :before-close="beforeClose"
      :show-close="false"
      :close-on-press-escape="true"
      width="400px"
      height="500px"
      top="50px"
      :destroy-on-close="false"
    >
      <slot></slot>
    </el-dialog>
</template>

<script>
export default {
  props: ["dialogVisible","changeVisible"],
  data() {
    return {
    };
  },
  methods: {
    beforeClose(done) {
     this.changeVisible();
      done();
    },
  },
};
</script>

<style lang="scss">
.dialog-total {
  .el-dialog__body {
    padding: 30px 20px;
    width: 400px !important;
    overflow: hidden;
  }
}
.uview-dialog .el-dialog {
  background-color: transparent !important;
  position: relative;
  margin: 0 auto 50px;
  -webkit-box-shadow: 0 0 0 rgba(0, 0, 0, 0.3);
  box-shadow: 0 0 0 rgba(0, 0, 0, 0.3);
  box-sizing: border-box;
  width: 50%;
}
@media screen and (max-width: 992px) {
  .uview-dialog .el-dialog,
  .el-message-box {
    width: 400px !important;
  }
}
</style>
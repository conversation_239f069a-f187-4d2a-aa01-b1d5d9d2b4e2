/***********common**************/
.font-size-styleA {
  font-size: 2.5vh;
  color: #b6d7ff;
}

.font-size-styleB {
  font-size: 1.8vh;
  color: #b6d7ff;
}

.font-size-styleC {
  font-size: 2.5vh;
}

.pointer-events-auto {
  pointer-events: auto;
}

/***********common**************/

.screenTown {
  position: fixed;
  min-width: 1200px;
  width: 100%;
  height: 100vh;
  background-color: #05072c !important;
  background-image: url("../../../public/img/townScreen/background.png");

  //头
  .title-header {
    position: absolute;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    width: 100%;

    .title-back {
      position: absolute;
      width: 100%;
      height: 8vh;
      margin-top: 0.5vh;
    }
  }

  .title-back-desc {
    position: absolute;
    width: 40%;
    z-index: 500;
    left: 50.5%;
    transform: translate(-50.5%, 0);
    text-align: center;
    margin-top: 2vh;
    font-size: 3vh;
    color: #fff;
    height: 4.5vh;
    font-weight: bold;
    letter-spacing: 5px;
  }

  //左边时间
  .screen-time {
    position: absolute;
    width: 30%;
    left: 1%;
    margin-top: 2.5vh;

    .screen-time-back {
      position: absolute;
      width: 100%;
      height: 6vh;
    }

    .screen-time-desc {
      position: absolute;
      width: 60%;
      text-align: center;
      font-size: 2vh;
      margin-top: 0vh;
      font-weight: bold;
      margin-left: 10%;
      color: #fff;
      height: 5vh;
      letter-spacing: 2px;
    }
  }

  //右边地点
  .screen-local {
    position: absolute;
    width: 23%;
    right: 1%;
    margin-top: 2vh;

    .screen-local-back {
      position: absolute;
      width: 100%;
      height: 6vh;
      transform: rotateY(180deg);
    }

    .screen-local-desc {
      position: absolute;
      width: 60%;
      text-align: center;
      margin-top: 0vh;
      margin-left: 40%;
      height: 5vh;

      .screen-local-title {
        font-weight: bold;
        font-size: 2vh;

        letter-spacing: 2px;
        background: #05072c;
        color: #fff;
        vertical-align: top;
        height:2vh;
        width:10vw;
      }
    }
  }

  .screen-content {
    position: absolute;
    width: 100%;
    height: 89vh;
    // background: blue;
    top: 11vh;

    .outside-on-back {
      position: absolute;
      width: 100%;
      height: 100%;
    }

    .screen-left {
      position: absolute;
      left: 1%;
      width: 25%;
      // top: 11vh;

      .screen-left-all {
        position: absolute;
        width: 100%;
        height: 85vh;

        .left-on-back {
          position: absolute;
          left: 2%;
          width: 100%;
          height: 45vh;
          top: 1vh;
        }

        .left-bottom-back {
          position: absolute;
          left: 2%;
          width: 100%;
          height: 39vh;
          top: 47vh;
        }

        //左上
        .left-on {
          position: absolute;
          width: 95%;
          left: 5%;
          top: 1vh;
          height: 44vh;
        }

        //左下
        .left-bottom {
          position: absolute;
          top: 47vh;
          width: 95%;
          left: 5%;
          height: 39vh;
        }
      }
    }

    .screen-center {
      position: absolute;
      left: 50%;
      width: 45%;
      transform: translate(-50%, 0);

      .center-on-back {
        position: absolute;
        left: 2%;
        width: 100%;
        height: 45vh;
        top: 1vh;
      }

      .center-bottom-back {
        position: absolute;
        left: 2%;
        width: 100%;
        height: 39vh;
        top: 47vh;
      }

      //中上
      .center-on {
        position: absolute;
        width: 95%;
        left: 5%;
        top: 1vh;
        height: 44vh;
      }

      //中下
      .center-bottom {
        position: absolute;
        top: 47vh;
        width: 95%;
        left: 5%;
        height: 39vh;
      }
    }

    .screen-right {
      position: absolute;
      right: 1%;
      width: 25%;
      height: 86vh;

      .right-on-back {
        position: absolute;
        left: 2%;
        width: 100%;
        height: 21vh;
        top: 1vh;
      }

      .right-center-back{
        position: absolute;
        left: 2%;
        width: 100%;
        height: 23vh;
        top: 23vh;
      }

      .right-bottom-back{
        position: absolute;
        left: 2%;
        width: 100%;
        height: 39vh;
        top: 47vh;
      }

      //右上
      .right-on {
        position: absolute;
        width: 95%;
        left: 5%;
        top: 1vh;
        height: 21vh;
      }

      //右中
      .right-center {
        position: absolute;
        width: 95%;
        left: 5%;
        top: 23vh;
        height: 23vh;
      }

      //右下
      .right-bottom {
        position: absolute;
        top: 47vh;
        width: 95%;
        left: 5%;
        height: 39vh;
      }
    }
  }
}
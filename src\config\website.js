/*
 * @Date: 2025-06-24 09:38:27
 * @LastEditors: linqh21
 * @LastEditTime: 2025-06-24 14:39:50
 * @Description:
 * @FilePath: \src\config\website.js
 */
/**
 * 全局配置文件
 */
export default {
  title: "数村智慧小脑",
  logo: "DC",
  key: 'zhxn',//配置主键,目前用于存储
  indexTitle: '数村智慧小脑',
  clientId: 'sczhxn', // 客户端id
  clientSecret: 'THxcG8v53zp7UszK', // 客户端密钥
  tenantMode: false, // 是否开启租户模式
  tenantId: "000000", // 管理组租户编号
  captchaMode: true, // 是否开启验证码模式
  switchMode: false, // 是否开启登录切换角色部门
  lockPage: '/lock',
  tokenTime: 3000,
  tokenHeader: 'Blade-Auth',
  //http的status默认放行列表
  statusWhiteList: [],
  //配置首页不可关闭
  isFirstPage: false,
  fistPage: {
    label: "首页",
    value: "/wel/index",
    params: {},
    query: {},
    meta: {
      i18n: 'dashboard'
    },
    group: [],
    close: false
  },
  //配置菜单的属性
  menu: {
    iconDefault: 'iconfont icon-caidan',
    props: {
      label: 'name',
      path: 'path',
      icon: 'source',
      children: 'children'
    }
  },

  uviewSrc: "http://**************:15702/h5-render/#/",

  //oauth2配置
  oauth2: {
    // 是否开启注册功能
    registerMode: true,
    // 使用后端工程 @org.springblade.test.Sm2KeyGenerator 获取
    publicKey:'04b9dad65321570d349e51b1dcaedcbf1df3c7e2a2aeea74edcb0dfad8b9dc75e19c1f57b41063b43113d95d00d675b2973654cecdbed98c33d870a5de26442277',
    // 第三方系统授权地址
    authUrl: 'http://localhost/blade-auth/oauth/render',
    // 单点登录系统认证
    ssoMode: false, // 是否开启单点登录功能
    ssoBaseUrl: 'http://localhost:8100', // 单点登录系统地址(cloud端口为8100,boot端口为80)
    ssoAuthUrl: '/oauth/authorize?client_id=saber&response_type=code&redirect_uri=', // 单点登录授权地址
    ssoLogoutUrl: '/oauth/authorize/logout?redirect_uri=', // 单点登录退出地址
    redirectUri: 'http://localhost:1888', // 单点登录回调地址(Saber服务的登录界面地址)
  },
  design: {
    // 报表设计器地址(cloud端口为8108,boot端口为80)
    reportUrl: 'http://localhost:8108/ureport',
  },
}

.province-center-on-content {
    position: absolute;
    width: 100%;
    height: 55vh;
    left: 50%;
    transform: translate(-40%, 0);
    .header {
        position: absolute;
        width: 25%;
        white-space: nowrap;
        top: 1vh;
        z-index: 10;
        left: -10%;
        .title {
            position: absolute;
            letter-spacing: 3px;
            color: #5bffdc;
            width: 100%;
            font-size: 1.7vh;
            left: 0;
        }
        .title-direction {
            position: absolute;
            width: 35px;
            top: 0.6vh;
            right: 0;
            height: auto;
        }
        .left-on-line {
            position: absolute;
            width: 100%;
            top: 3.6vh;
        }
    }
    .marquee {
        position: absolute;
        width: 90%;
        left: -5%;
        bottom: -6vh;
    }
}

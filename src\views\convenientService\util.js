/*
 * @Descrip:
 * @Author: 林玥
 * @Date: 2021-06-24 14:42:07
 * @LastEditors: linqh21
 * @LastEditTime: 2025-02-14 16:56:36
 */

/**
 * @Descrip: 防抖函数(防止重复点击按钮)
 * @Author: 林玥
 * @Date: 2021-06-24 14:42:43
 * @params {function}callback (必传)
 * @params {Number}interval
 * @return {function}
 */
//防抖
export const debounce = function (callback, interval, immediate) {
  if (typeof callback !== 'function') throw new TypeError('func must be an function');
  if (typeof interval === "boolean") {
    immediate = interval;
    interval = 300;
  }
  if (typeof interval !== 'number') interval = 300;
  if (typeof immediate !== 'boolean') immediate = false;
  let timer = null;
  return function Proxy(...params) {
    let self = this;
    if (immediate && !timer) callback.call(self, ...params) //第一次马上执行
    if (timer) {
      clearTimeout(timer)
      timer = null
    }
    timer = setTimeout(() => { //immediate马上执行则定时器结束时不执行的，没有才执行
      if (!immediate) callback.call(self, ...params)
      if (timer) {
        clearTimeout(timer)
        timer = null
      }
    }, interval);
  }

}


/**
 * @Descrip:节流(频率) 点击一下再点击一下的时候要间隔interval
 * @Author: 林玥
 * @Date: 2021-06-22 20:47:08
 * @return {*} null
 * @param {*} callback
 * @param {*} interval
 */
export const throttle = function (callback, interval) {
  if (typeof callback !== "function") throw new TypeError('func must be an function');
  if (typeof interval !== "number") interval = 300;
  let timer = null,
    previousTime = 0;
  return function Proxy(...parms) {
    let now = +new Date(),
      remaining = interval - (now - previousTime),
      self = this;
    if (remaining <= 0) { //第一次进入或者不在间隔时间内(立即执行)
      console.log(55, callback, self)
      callback.call(self, ...parms)
      previousTime = now;
    } else if (!timer) { //第二次即以后执行，没有定时器的时候才执行，有定时器的时候直接忽略点击
      timer = setTimeout(() => {
        callback.call(self, ...parms)
        previousTime = now
        if (timer) {
          clearTimeout(timer)
          timer = null
        }
      }, remaining);
    }
  }
}


// 校验规则

/* 合法uri*/
export const validateHttpURL = /^(https?):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
// 电话号码
export const validatePhone = /^(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9]|16[0-9]|17[0-9])\d{8}$/
//正数
export const validatePlus = /^[^-]/
// 文件上传白名单(任意类型的文件)
export const fileWhiteList = [/image\/./, /audio\/./, /video\/./, /.pdf/,
  // /text\/plain/,//微信小程序无法自动跳转下载地址
  /application\/vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet/, /application\/vnd\.ms-excel/, //excel
  /application\/vnd\.openxmlformats-officedocument\.wordprocessingml\.document/, /application\/msword/, //word
  /application\/vnd\.openxmlformats-officedocument\.presentationml\.presentation/, /application\/vnd\.ms-powerpoint/ //ppt
]

export const allFileList = [/image\/./, /audio\/./, /video\/./, /.pdf/,
  // /text\/plain/,//微信小程序无法自动跳转下载地址
  /application\/vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet/, /application\/vnd\.ms-excel/, //excel
  /application\/vnd\.openxmlformats-officedocument\.wordprocessingml\.document/, /application\/msword/, //word
  /application\/vnd\.openxmlformats-officedocument\.presentationml\.presentation/, /application\/vnd\.ms-powerpoint/, //ppt
  /text\/plain/ // txt
]

export const fileWhiteListTwo = [/image\/./, /.pdf/,
  // /text\/plain/,//微信小程序无法自动跳转下载地址
  /application\/vnd\.openxmlformats-officedocument\.wordprocessingml\.document/, /application\/msword/, //word
]

// 校验是图片文件
export const validateImage = /image\/./
// 校验是excel
export const validateExel =  [/application\/vnd\.openxmlformats-officedocument\.spreadsheetml\.sheet/, /application\/vnd\.ms-excel/]
// 校验是中文名
export const validateChineseName = /^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,20}$/;//中文姓名（支持带点）


//video 
export const validateVideo = [ /video\/./,]

/**
 * @Descrip: 文件上传uploadBefore的校验规则
 * @Author: 林玥
 * @Date: 2021-06-27 17:50:00
 * @return {boolean} 单个文件是否符合规则
 * @param {string} type 规定上传的文件类型
 * @param {number} size 规定上传的文件大小
 * @param {object} file 将要上传的文件
 */
export const validateFile = function (type, size, file) {
  let regType = fileWhiteList;
  if(type==="TypeIWP") regType = fileWhiteListTwo
  if (type === 'onlyImage') regType = validateImage
  if (type === 'excel') regType = validateExel
  if (type === 'all') regType = allFileList
  if(type==='video')  regType= validateVideo
  let isType;
  if (type === 'onlyImage') isType = regType.test(file.type); //有传值,不是数组时
  else {
    for (let i = 0; i < regType.length; i++) {
      if (regType[i].test(file.type)) {
        isType = true;
        break;
      }
    }
    if (!isType) isType = false;
  }
  if (!isType) {
    this.$message.error("文件上传格式错误");
    return false //只要一个判断错误就返回错误，否则可能会出先提示变粗
  }
  const isSize = file.size / 1024 / 1024 < size;
  if (!isSize) {
    this.$message.error(`文件上传大小不能超过 ${size}MB!`);
    return false
  }
  return true
}


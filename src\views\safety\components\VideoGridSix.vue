<template>
  <div class="video-grid-box">
    <!-- 未选择部门提示 -->
    <div v-if="!loadingDevices && !deptId" class="no-devices">
      <i class="el-icon-folder-opened"></i>
      <p>请选择监控区域</p>
      <p class="tip">点击左侧部门树选择要查看的监控区域</p>
    </div>

    <!-- 无设备提示 -->
    <div v-else-if="!loadingDevices && deviceList.length === 0 && deptId" class="no-devices">
      <i class="el-icon-monitor"></i>
      <p>该部门下暂无视频设备</p>
      <p class="tip">请选择其他部门或联系管理员添加设备</p>
    </div>

    <!-- 设备加载中状态 -->
    <div v-else-if="loadingDevices" class="loading-devices">
      <div class="loading-spinner"></div>
      <p>正在加载设备列表...</p>
    </div>

    <!-- 有设备时的六宫格布局 -->
    <div v-else class="video-grid-container">
      <div class="video-grid">
        <div v-for="item in displayGridItems" :key="item.idx" :class="['grid-item', `pos-${item.idx + 1}`]">
          <template v-if="!item.data.__empty">
            <MultiSourcePlayer
              :key="(item.data.id || 'empty') + '-' + item.idx + '-' + (item.data.refreshKey || '')"
              :device-name="item.data.deviceName || '未命名设备'"
              :sources="item.data.srcList || {}"
              :initial-source-type="getDefaultSourceType(item.data)"
              :device-status="item.data.state || ''"
              @request-source="() => fetchDeviceSource(item.data)"
              @error="handlePlayerError"
              :is-expanded="false"
              @dblclick.native="item.idx > 0 && item.data.state !== 3 ? swapWithMain(item.idx) : null"
            />
          </template>
          <template v-else>
            <div class="empty-cell">
              <div class="empty-cell-content">
                <i class="el-icon-video-camera"></i>
                <span>空闲位置</span>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 分页控制 -->
      <div class="pagination-control">
        <el-button size="mini" :disabled="currentPage === 1 || loadingDevices" @click="prevPage" icon="el-icon-arrow-left" circle></el-button>
        <div class="page-info">
          <span class="page-text">第 {{ currentPage }} / {{ totalPages }} 页</span>
          <span class="record-info">共 {{ totalRecords }} 个设备</span>
        </div>
        <el-button size="mini" :disabled="currentPage === totalPages || loadingDevices" @click="nextPage" icon="el-icon-arrow-right" circle></el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getList, getVideoDetail } from "@/api/system/video";
import MultiSourcePlayer from "@/components/video-player/MultiSourcePlayer.vue";

export default {
  name: "VideoGridSix",
  components: {
    MultiSourcePlayer
  },
  props: {
    deptId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      deviceList: [],
      currentPage: 1,
      pageSize: 6,
      totalPages: 0,
      totalRecords: 0,
      loadingDevices: false
    };
  },
  computed: {
    displayGridItems() {
      // 后端分页：直接使用返回的设备列表，不需要前端再次分页
      const filled = [...this.deviceList];

      // 确保每页固定显示6个位置，不足时用空位置补全
      while (filled.length < 6) {
        filled.push({ __empty: true });
      }

      return filled.map((data, idx) => ({ idx, data }));
    }
  },
  watch: {
    deptId: {
      handler(newDeptId) {
        if (newDeptId) {
          this.currentPage = 1;
          this.loadDevices();
        } else {
          this.deviceList = [];
          this.totalPages = 0;
          this.totalRecords = 0;
        }
      },
      immediate: true
    }
  },
  methods: {
    loadDevices() {
      if (!this.deptId) return;

      this.loadingDevices = true;
      getList(this.currentPage, this.pageSize, { deptId: this.deptId }).then(res => {
        console.log(res)
        const responseData = res.data.data;

        this.deviceList = responseData.records.map(device => ({
          ...device,
          srcList: {},
          refreshKey: Date.now() + '-' + Math.random()
        }));

        this.totalPages = responseData.pages;
        this.totalRecords = responseData.total || 0;
      }).catch(error => {
        console.error('Failed to load video devices:', error);

        // 根据错误类型显示不同的消息
        let errorMessage = '获取设备列表失败';
        if (error && error.response && error.response.status === 404) {
          errorMessage = '该部门下暂无设备';
        } else if (error && error.response && error.response.status === 403) {
          errorMessage = '没有权限访问该部门的设备';
        } else if (error && error.code === 'NETWORK_ERROR') {
          errorMessage = '网络连接失败，请检查网络状态';
        }

        this.$message({
          message: errorMessage,
          type: 'error',
          duration: 4000,
          showClose: true
        });

        // 清空设备列表
        this.deviceList = [];
        this.totalPages = 0;
        this.totalRecords = 0;
      }).finally(() => {
        this.loadingDevices = false;
      });
    },
    getDefaultSourceType(device) {
      if (!device.srcList) return 'flv';
      const priority = ['flv', 'ws_flv', 'fmp4', 'ws_fmp4', 'hls'];
      return priority.find(type => device.srcList[type]) || Object.keys(device.srcList)[0];
    },
    async fetchDeviceSource(device, forceRefresh = false) {
      if (device.state !== 2) {
        this.$message.warning('设备已离线，无法获取视频源');
        return;
      }

      if (forceRefresh) {
        this.$set(device, 'srcList', {});
      } else if (Object.keys(device.srcList).length > 0) {
        return;
      }

      this.$set(device, 'loadingSource', true);
      try {
        const res = await getVideoDetail(device.id);
        this.$set(device, 'srcList', res.data.data.playOnRes);
      } catch (error) {
        console.error('Failed to fetch device source:', error);

        // 根据错误类型显示不同的消息
        let errorMessage = '获取视频源失败';
        if (error && error.response && error.response.status === 404) {
          errorMessage = '设备视频源不存在';
        } else if (error && error.response && error.response.status === 500) {
          errorMessage = '服务器内部错误，请稍后重试';
        } else if (error && error.code === 'TIMEOUT') {
          errorMessage = '获取视频源超时，请重试';
        }

        this.$message({
          message: `${device.deviceName}: ${errorMessage}`,
          type: 'warning',
          duration: 4000
        });

        throw error;
      } finally {
        this.$set(device, 'loadingSource', false);
      }
    },
    handlePlayerError(error) {
      console.error('Player error:', error);

      // 显示更友好的错误消息
      const friendlyMessage = (error && error.friendlyMessage) || '视频播放出错';
      this.$message({
        message: friendlyMessage,
        type: 'error',
        duration: 3000,
        showClose: true
      });

      // 如果是网络错误，建议用户检查网络
      if (error && error.message && (error.message.includes('Network') || error.message.includes('Timeout'))) {
        this.$message({
          message: '建议检查网络连接后重试',
          type: 'info',
          duration: 5000
        });
      }
    },
    prevPage() {
      if (this.currentPage > 1 && !this.loadingDevices) {
        this.currentPage--;
        this.loadDevices();
      }
    },
    nextPage() {
      if (this.currentPage < this.totalPages && !this.loadingDevices) {
        this.currentPage++;
        this.loadDevices();
      }
    },
    async swapWithMain(idx) {
      // idx 是当前点击的小格子索引 (1-5)
      if (idx === 0) return; // 0是大格子，不能与自己交换

      // 直接使用索引，因为现在 deviceList 就是当前页的数据
      const targetDevice = this.deviceList[idx];
      const mainDevice = this.deviceList[0];

      // 检查目标设备是否存在且在线
      if (!targetDevice || targetDevice.__empty || targetDevice.state !== 2) {
        this.$message.warning('只有在线设备可以交换到主画面');
        return;
      }

      try {
        // 显示加载状态
        this.$set(targetDevice, 'swapping', true);
        if (mainDevice && !mainDevice.__empty) {
          this.$set(mainDevice, 'swapping', true);
        }

        // 保存原始视频源
        const mainSrcList = mainDevice && !mainDevice.__empty
          ? { ...mainDevice.srcList }
          : {};
        const targetSrcList = { ...targetDevice.srcList };

        // 交换设备数据
        const newMainDevice = {
          ...targetDevice,
          srcList: mainSrcList,
          refreshKey: Date.now() + '-' + Math.random()
        };

        const newTargetDevice = mainDevice && !mainDevice.__empty
          ? {
            ...mainDevice,
            refreshKey: Date.now() + '-' + Math.random(),
            // 如果是离线设备，清空srcList避免尝试播放
            srcList: mainDevice.state !== 2 ? {} : targetSrcList
          }
          : { __empty: true };

        // 更新设备列表（使用正确的索引）
        this.$set(this.deviceList, 0, newMainDevice);  // 主画面固定在索引0
        this.$set(this.deviceList, idx, newTargetDevice); // 目标位置

        // 只重新加载主画面的视频流
        await this.fetchDeviceSource(newMainDevice, true);

        // 如果交换到小格子的是在线设备才重新加载
        if (newTargetDevice.state === 2 && newTargetDevice.id) {
          await this.fetchDeviceSource(newTargetDevice, true);
        }

      } catch (error) {
        console.error('交换设备失败:', error);
        this.$message.error('设备交换失败');
      } finally {
        // 清除加载状态
        if (targetDevice) this.$set(targetDevice, 'swapping', false);
        if (mainDevice && !mainDevice.__empty) {
          this.$set(mainDevice, 'swapping', false);
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.video-grid-box {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .no-devices, .loading-devices {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f5f7fa;
    border-radius: 8px;
    border: 1px dashed #dcdfe6;

    i {
      font-size: 48px;
      color: #c0c4cc;
      margin-bottom: 16px;
    }

    p {
      margin: 0;
      color: #909399;
      font-size: 16px;

      &.tip {
        font-size: 14px;
        margin-top: 8px;
        color: #c0c4cc;
      }
    }
  }

  .loading-devices {
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e4e7ed;
      border-top: 4px solid #409eff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  }

  .video-grid-container {
    flex: 1;
    display: flex;
    flex-direction: column;

    .video-grid {
      flex: 1;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(3, 1fr);
      gap: 8px;

      .grid-item {
        position: relative;
        background: #000;
        border-radius: 4px;
        overflow: hidden;

        &.pos-1 {
          grid-column: 1 / span 2;
          grid-row: 1 / span 2;
        }

        &.pos-2 {
          grid-column: 3;
          grid-row: 1;
        }
        &.pos-3 {
          grid-column: 3;
          grid-row: 2;
        }
        &.pos-4 {
          grid-column: 1;
          grid-row: 3;
        }
        &.pos-5 {
          grid-column: 2;
          grid-row: 3;
        }
        &.pos-6 {
          grid-column: 3;
          grid-row: 3;
        }
      }

      .empty-cell {
        width: 100%;
        height: 100%;
        background: #f5f7fa;
        border: 1px dashed #dcdfe6;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;

        .empty-cell-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          color: #c0c4cc;

          i {
            font-size: 24px;
            margin-bottom: 8px;
          }

          span {
            font-size: 12px;
          }
        }
      }
    }

    .pagination-control {
      padding: 12px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 16px;

      .page-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;

        .page-text {
          font-size: 14px;
          color: #606266;
          font-weight: 500;
        }

        .record-info {
          font-size: 12px;
          color: #909399;
        }
      }
    }
  }
}
</style>

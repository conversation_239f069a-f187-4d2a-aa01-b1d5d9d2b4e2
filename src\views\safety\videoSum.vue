<template>
  <el-row class="video-sum-container">
    <!-- 左侧树形菜单 -->
    <el-col :span="5">
      <div class="box">
        <el-scrollbar>
          <basic-container>
            <avue-tree ref="tree" :option="treeOption" :data="treeData" @node-click="nodeClick" node-key="id" :current-node-key="treeGridId" />
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>

    <!-- 右侧视频区域 -->
    <el-col :span="19">
      <div class="video-content">
        <!-- 视图切换器 -->
        <div class="view-switcher">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="grid">
              <i class="el-icon-s-grid"></i> 网格视图
            </el-radio-button>
            <el-radio-button label="list">
              <i class="el-icon-s-order"></i> 列表视图
            </el-radio-button>
          </el-radio-group>
        </div>

        <!-- 动态组件 -->
        <component
          :is="currentViewComponent"
          :dept-id="treeGridId"
          :key="treeGridId + '-' + viewMode"
          @fullscreen-view="handleFullscreenView"
        />
      </div>
    </el-col>
  </el-row>
</template>

<script>
import { getDeptTree } from "@/api/system/dept";
import { mapGetters } from "vuex";
import VideoGridSix from "./components/VideoGridSix.vue";
import VideoList from "./components/VideoList.vue";

export default {
  name: "VideoSum",
  components: {
    VideoGridSix,
    VideoList
  },
  data() {
    return {
      treeGridId: "",
      treeData: [],
      viewMode: 'grid', // 'grid' | 'list'
      treeOption: {
        nodeKey: "id",
        defaultExpandAll: false,
        addBtn: false,
        menu: false,
        size: "small",
        props: {
          label: "title",
          value: "id",
          children: "children",
        },
      }
    };
  },
  mounted() {
    this.initData();
  },
  computed: {
    ...mapGetters(["userInfo"]),
    currentViewComponent() {
      return this.viewMode === 'grid' ? 'VideoGridSix' : 'VideoList';
    }
  },
  methods: {
    initData() {
      getDeptTree(true).then(res => {
        this.treeData = res.data.data;
      }).catch(error => {
        console.error('Failed to load department tree:', error);
      });
    },
    nodeClick(data) {
      this.treeGridId = data.id;
      this.$refs.tree.setCurrentKey(data.id);
    },
    handleFullscreenView(device) {
      // 处理全屏查看逻辑
      console.log('全屏查看设备:', device);
      // 可以在这里实现全屏查看功能，比如打开新窗口或弹窗
      this.$message.info(`即将全屏查看设备: ${device.deviceName}`);
    }
  }
};
</script>

<style lang="scss" scoped>
.video-sum-container {
  height: calc(97vh - 100px);
  display: flex;

  .box {
    height: 100%;
    // background: #fff;
    // border-right: 1px solid #eee;
  }

  .video-content {
    height: 100%;
    display: flex;
    flex-direction: column;

    .view-switcher {
      padding: 16px;
      background: #fff;
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .el-radio-group {
        .el-radio-button {
          .el-radio-button__inner {
            padding: 8px 16px;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 6px;

            i {
              font-size: 14px;
            }
          }

          &.is-active {
            .el-radio-button__inner {
              background-color: #409eff;
              border-color: #409eff;
              color: #fff;
            }
          }
        }
      }
    }

    // 动态组件容器
    > :last-child {
      flex: 1;
      overflow: hidden;
    }
  }
}
</style>

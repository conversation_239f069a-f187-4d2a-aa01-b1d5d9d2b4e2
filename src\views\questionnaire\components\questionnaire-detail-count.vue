<template>
    <div  style="padding-top: 8px">
        <div v-for="(item, index ) in questionCountList" :key="index" style="margin-bottom: 10px">
            <el-table
                :data="[item]"
                :border="true"
                v-if="item.type === 3"
            >
                <el-table-column
                    :label="index + 1 + '.'  + item.title">
                </el-table-column>
                <el-table-column
                    prop="answerNum"
                    label="回复数">
                </el-table-column>
            </el-table>
            <el-table
                :border="true"
                :data="item.options"
                v-else
            >
                <el-table-column
                    prop="title"
                    :label="index + 1 + '.'  + item.title">
                    <template slot-scope="scope">
                        <span>{{scope.row.title}}</span><el-image v-if="scope.row.attachLink" :src="scope.row.attachLink" style="width: 100px; height: 100px" fit="container" ></el-image>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="answerNum"
                    label="选择人数">
                </el-table-column>
                <el-table-column
                    prop="ratio"
                    label="比例"
                    v-if="item.type === 1">
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>
<script>
import { statistics } from '@/api/questionnaire/survey'
export default {
    // props: {
    //     questionnaireId: {
    //         type: Number,
    //         required: true
    //     }
    // },
    inject: ['getQuestionnaireId'],
    data() {
        return {
            questionCountList: [],
        }    
    },
    mounted() {
        this.getStatistics()
    },
    methods: {
        getStatistics() {
            statistics(this.getQuestionnaireId())
                .then(res => {
                    this.questionCountList = res.data.data.subjectList
                })
        }
    },
    
}
</script>

<template>
    <basic-container>
        <avue-crud
            ref="questionnaireTable"
            :defaults.sync="defaults"
            :option="option"
            :search.sync="search"
            :table-loading="loading"
            :data="data"
            :page.sync="page"
            :permission="permissionList"
            v-model="form"
            :before-open="beforeOpen"
            @search-change="searchChange"
            @search-reset="searchReset"
            @selection-change="selectionChange"
            @current-change="currentChange"
            @size-change="sizeChange"
            @refresh-change="refreshChange"
            @on-load="onLoad"
        >
            <template slot="menuLeft">
                <el-button
                    v-if="activeName==='0'&&permission.quest_add"
                    size="small" 
                    icon="el-icon-plus"
                    type="primary"
                    @click="showSelectTemplate=true"
                > 新建问卷
                </el-button>
                <el-button
                    v-if="activeName==='0'"
                    size="small" 
                    icon="el-icon-plus"
                    type="primary"
                    @click="$emit('changeTab', '2')"
                > 模版管理
                </el-button>
                <el-button
                    v-if="activeName==='2'&&permission.quest_add"
                    size="small" 
                    icon="el-icon-plus"
                    type="primary"
                    @click="addTemplate()"
                > 新建模版
                </el-button>
                <el-button
                    v-if="permission.quest_delete"
                    size="small" 
                    icon="el-icon-delete"
                    type="danger"
                    @click="handleDelete()"
                > 批量删除
                </el-button>
            </template>
            <template slot-scope="{ type, size, row }" slot="menu">
                <el-button
                    :type="type"
                    icon="el-icon-view"
                    :size="size"
                    @click.stop="detail(row)"
                    v-if="activeName === '0'"
                >详情
                </el-button>
                <el-button
                    :type="type"
                    icon="el-icon-view"
                    :size="size"
                    @click.stop="preview(row)"
                    v-if="activeName === '0'|| activeName =='2'"
                >预览
                </el-button>
                <el-button
                    :type="type"
                    icon="el-icon-edit"
                    :size="size"
                    @click="rowEdit(row)"
                    v-if="(activeName === '1'|| activeName =='2') && permission.quest_add"

                >编辑
                </el-button>
                <el-button
                    :type="type"
                    icon="el-icon-delete"
                    :size="size"
                    @click="rowDel(row)"
                    v-if="(row.status === 2 || activeName=== '1' || activeName =='2' )&&permission.quest_delete"
                >删除
                </el-button>
                <el-button
                    :type="type"
                    icon="el-icon-delete"
                    :size="size"
                    @click="rowEnd(row)"
                    v-if="activeName === '0' && row.status === 1 && permission.quest_end"
                >结束
                </el-button>
            </template>
            <template slot="title" slot-scope="{row}">
                <div class="title_style">{{row.title}}</div>
                <span>{{row.description}}</span>
            </template>
            <template slot="writeNum" slot-scope="{row}">
                <span>{{row.writeNum + '/' + row.totalNum}}</span>
            </template>
            <template slot="status" slot-scope="{row}">
                <span>{{row.statusText}}</span>
            </template>
            <template slot="realFinishTime" slot-scope="{row}">
                <span>{{row.status===1?row.finishTime:row.status===2?row.realFinishTime:''}}</span>
            </template>
        </avue-crud>
        <add :showAddDialog.sync="showDialog" :pageType="pageType" :draftDetail="draftDetail" v-if="showDialog"/>
        <detail :showDetailDialog.sync="showDetail" :questionnaireNum="questionnaireNum" :questionnaireTitle="questionnaireTitle"  v-if="showDetail" />
        <preview :showPreviewDrawer.sync="showPreview" :activeName="activeName" :questionnaireId="questionnaireId"  v-if="showPreview" />
        <select-template :showSelectDialog.sync="showSelectTemplate" @confirmSelect="confirmSelect"></select-template>
    </basic-container>
</template>
<script>
import add from './questionnaire-add.vue'
import detail from './questionnaire-detail.vue'
import preview from './questionnaire-preview.vue'
import selectTemplate from './selectTemplate.vue'
import { getList, 
         getDraftList, 
         remove,
         removeDraft,
         end,
         getDraftDetail,
         getDetail,
         getTemplateList,
         removeTemplate,
         getTemplateDetail
} from '@/api/questionnaire/survey'
import { mapGetters } from 'vuex'
export default {
    props: {
        activeName: {
            type: String,
            default: '0'
        }
    },
    components: {
        add,
        detail,
        preview,
        selectTemplate
    },
    provide() {
        return {
            getQuestionnaireId: () => { return this.questionnaireId},
            getPage: () => { return this.page},
            getPermission: () => { return this.permission },
            onLoad: this.onLoad
        }
    },
    inject: ['getDraftNum'],
    data() {
        return {
            questionnaireId: '',
            questionnaireTitle: '',
            questionnaireNum: '',
            showDialog: false,
            pageType: 1, // 1添加 2编辑,3模版新建问卷，4新建模版,5编辑模版
            draftDetail: {},
            showDetail: false,
            showPreview: false,
            loading: true,
            defaults: {},
            data: [],
            query: {},
            form: {},
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            option: {
                height: 'auto',
                calcHeight: 30,
                tip: false,
                searchShow: true,
                searchSpan: 5,
                searchMenuSpan: 8,
                border: true,
                index: false,
                viewBtn: false,
                editBtn: false,
                delBtn: false,
                addBtn: false,
                menuPosition: 'center',
                menuWidth: 300,
                labelWidth: 100,
                dialogWidth: 880,
                dialogClickModal: false,
                selection: true,
                selectable: (row,index) => row.status ? row.status==2 : true,
                column: [
                    {
                        label: '问卷内容',
                        prop: 'title',
                        search: true,
                        searchSpan: 8,
                        overHidden: true,
                        hide: false,
                        maxlength:50,
                    },
                    {
                        label: '发布时间',
                        prop: 'releaseTime',
                        display: false,
                        hide: false,
                        type: 'datetime',
                        format: 'yyyy-MM-dd HH:mm:ss',
                        valueFormat: 'yyyy-MM-dd HH:mm:ss',
                    },
                    {
                        label: '已填写/总人数',
                        prop: 'writeNum',
                        display: false,
                        hide: false,
                    },
                    {
                        label: '问卷状态',
                        prop: 'status',
                        search: true,
                        searchSpan: 5,
                        type: 'select',
                        dicUrl: "/api/blade-system/dict/dictionary?code=survey_status",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        display: false,
                        hide: false,
                    },
                    {
                        label: '结束时间',
                        prop: 'realFinishTime',
                        display: false,
                        hide: false,
                        type: 'datetime',
                        format: 'yyyy-MM-dd HH:mm:ss',
                        valueFormat: 'yyyy-MM-dd HH:mm:ss',
                    },
                    {
                        label: '上次修改时间',
                        prop: 'updateTime',
                        display: false,
                        hide: true,
                        type: 'datetime',
                        format: 'yyyy-MM-dd HH:mm:ss',
                        valueFormat: 'yyyy-MM-dd HH:mm:ss',
                    },
                    {
                        label: '创建时间',
                        prop: 'createTime',
                        display: false,
                        hide: true,
                        type: 'datetime',
                        format: 'yyyy-MM-dd HH:mm:ss',
                        valueFormat: 'yyyy-MM-dd HH:mm:ss',
                    }
                ]
            },
            selectionList: [],
            showSelectTemplate:false

        }
    },
    watch: {
        activeName(newV) {
            this.$refs.questionnaireTable.searchReset()
            if(newV === '0') {
                this.defaults.writeNum.hide = false
                this.defaults.status.hide = false
                this.defaults.status.search = true
                this.defaults.realFinishTime.hide = false
                this.defaults.updateTime.hide = true
                this.defaults.createTime.hide=true
            }else if(newV==='1') {
                this.defaults.releaseTime.hide = true
                this.defaults.writeNum.hide = true
                this.defaults.status.hide = true
                this.defaults.status.search = false
                this.defaults.realFinishTime.hide = true
                this.defaults.updateTime.hide = false
                this.defaults.createTime.hide=true
            }else{
                this.defaults.releaseTime.hide = true
                this.defaults.writeNum.hide = true
                this.defaults.status.hide = true
                this.defaults.status.search = false
                this.defaults.realFinishTime.hide = true
                this.defaults.updateTime.hide = true
                this.defaults.createTime.hide=false
            }
            this.page.currentPage = 1
            this.onLoad(this.page)
        }
    },
    computed: {
        ...mapGetters(['userInfo', 'permission']),
        permissionList() {
            return {
                addBtn: this.vaildData(this.permission.quest_add, false),

            }
        },
        ids () {
            let ids = []
            this.selectionList.forEach((ele) => {
                ids.push(ele.id)
            })
            return ids.join(",")
        },
    },
    methods: {
        addTemplate(){
            this.pageType = 4
            this.showDialog = true
        },
        // 选择模版
       async confirmSelect(id){
        console.log(id,'id',this.showSelectTemplate)
            if(!id){
                this.rowAdd()
                this.showSelectTemplate=false
                return 
            }
            this.templateId = id
            this.pageType=3
            let res = await getTemplateDetail(id)
            this.draftDetail = res.data.data
            this.showSelectTemplate=false
            this.showDialog=true
        },
        searchReset() {
            this.query = {}
            this.onLoad(this.page)
        },
        onLoad(page, params = {}) {
            this.loading = true
            const p = Object.assign(params, this.query)
            let apiName = this.activeName==='0'?getList:this.activeName==='1'?getDraftList:getTemplateList
            // switch(this.activeName) {
            //     case '0':
                    apiName(page.currentPage, page.pageSize, p)
                        .then(res => {
                            const data = res.data.data
                            this.page.total = data.total
                            this.data = data.records
                            if(page.currentPage!==1 && data.records.length==0){
                                this.page.currentPage--
                                this.onLoad(this.page)
                            }
                            this.loading = false
                            this.selectionClear()
                        })
                //     break;
                // case '1': 
                //     getDraftList(page.currentPage, page.pageSize, p)
                //         .then(res => {
                //             const data = res.data.data
                //             this.page.total = data.total
                //             this.data = data.records
                //             this.loading = false
                //             this.selectionClear()
                //         })
                //     break;
                // case '2': 
                //     getTemplateList(page.currentPage, page.pageSize, p)
                //         .then(res => {
                //             const data = res.data.data
                //             this.page.total = data.total
                //             this.data = data.records
                //             this.loading = false
                //             this.selectionClear()
                //         })
                //     break;
                // default:
                //     break;
                
            // }
        },
        rowAdd() {
            this.pageType = 1
            this.showDialog = true
        },
        async rowEdit(row) {
            switch(this.activeName){
                case '0':
                case '1':
                    {
                        const result = await new Promise((resolve) => {
                            getDraftDetail(row.id).then(res => {
                                if (res && res.data.code === 200) {
                                    resolve(res.data.data);
                                }
                            })
                        })
                        const _self = this
                        this.$nextTick(() => {
                            _self.draftDetail = result
                            _self.pageType = 2
                            _self.showDialog = true
                        })
                    }
                break;
                case '2':
                    {
                        const result = await new Promise((resolve) => {
                            getTemplateDetail(row.id).then(res => {
                                if (res && res.data.code === 200) {
                                    resolve(res.data.data);
                                }
                            })
                        })
                        const _self = this
                        this.$nextTick(() => {
                            _self.draftDetail = result
                            _self.pageType = 5
                            _self.showDialog = true
                        })
                    }
                break;
                default:
                    break
            }
            
            
        },
        rowDel(row) {
            this.$confirm(`确定删除该${this.activeName==='0'?'问卷':this.activeName==='1'? '草稿':'模版'}？`, '提示',{
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    switch(this.activeName) {
                        case '0':
                            remove(row.id).then(res => {
                                if (res.data.code === 200) {
                                    this.$message({
                                    type: 'success',
                                    message: '操作成功'
                                })
                                this.onLoad(this.page)
                                } else {
                                    this.$message.error('操作失败!' + res.data.msg)
                                }
                            })
                            .catch(()=> {})
                            break;
                        case '1':
                            removeDraft(row.id).then(res => {
                                if (res.data.code === 200) {
                                    this.$message({
                                    type: 'success',
                                    message: '操作成功'
                                })
                                this.getDraftNum()
                                this.onLoad(this.page)
                                } else {
                                    this.$message.error('操作失败!' + res.data.msg)
                                }
                            })
                            .catch(()=> {})
                            break;
                        case '2':
                            removeTemplate(row.id).then(res => {
                                if (res.data.code === 200) {
                                    this.$message({
                                    type: 'success',
                                    message: '操作成功'
                                })
                                this.onLoad(this.page)
                                } else {
                                    this.$message.error('操作失败!' + res.data.msg)
                                }
                            })
                            .catch(()=> {})
                        break;
                        default:
                            break;
                    }
                    
                })
                
        },
        // 勾选
        selectionChange (list) {
            this.selectionList = list
        },
        // 重置勾选
        selectionClear () {
            this.selectionList = []
            this.$refs.questionnaireTable.toggleSelection()
        },
        handleDelete () {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据")
                return
            }
            var removeApi =this.activeName=='0'?remove:this.activeName=='1'?removeDraft:removeTemplate
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
            .then(() => {
                return removeApi(this.ids)
                })
            .then(() => {
                this.onLoad(this.page, this.query)
                if(this.activeName==='1'){
                    this.getDraftNum()
                }
                this.$message({
                    type: "success",
                    message: "操作成功!",
                })
                this.$refs.questionnaireTable.toggleSelection()
            })
        },
        rowEnd(row) {
            this.$confirm('确定提前结束该问卷？', '提示',{
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            })
                .then(() => {
                    end(row.id).then(res => {
                        if (res.data.code === 200) {
                            this.$message({
                            type: 'success',
                            message: '操作成功'
                        })
                        this.onLoad(this.page)
                        } else {
                            this.$message.error('操作失败!' + res.data.msg)
                        }
                    })
                })
                .catch(()=> {})
        },
        detail(row) {
            console.log(row)
            this.questionnaireId = row.id
            this.questionnaireNum = row.writeNum
            this.questionnaireTitle = row.title
            this.showDetail = true
        },
        preview(row) {
            this.questionnaireId = row.id
            this.showPreview = true
        },
        getShowPreview(val) {
            this.showPreview = val
        },
        searchChange(params, done) {
            this.page.currentPage = 1
            this.query = params
            this.onLoad(this.page, params)
            done()
        },
        refreshChange() {
            this.onLoad(this.page, this.query);
        },
    }
} 
</script>
<style scoped>
.title_style {
    font-weight: bold;
}
</style>
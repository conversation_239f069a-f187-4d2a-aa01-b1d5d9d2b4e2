<!--
 * @Author: chenn26
 * @Date: 2023-01-16 16:37:15
 * @LastEditors: chenn26
 * @LastEditTime: 2023-06-05 15:55:24
 * @Description: 
-->
<template>
    <div class="dialogDetail">
        <div class="content">
            <span class="el-icon-error closeTag" @click="closeDetail"></span>
            <div class="showPanel">
                <div class="box">
                    <div class="box-panel">
                        <div class="panel_left">
                            <span style="text-align: right;">服务家庭数：</span>
                            <CountTo :endVal="data.families || 0" :duration="3000" /><span>(户)</span>
                        </div>
                        <div class="panel_right">
                            <span style="text-align: right;">渗透率：</span>
                            <CountTo :endVal="data.familyInfiltration || 0" :duration="3000" decimals="2" />
                            <span>%</span>
                        </div>
                    </div>
                </div>
                <div class="box">
                    <div class="box-panel">
                        <div class="panel_left">
                            <span style="text-align: right;">服务村民数：</span>
                            <CountTo :endVal="data.villagers || 0" :duration="3000" /><span>(人)</span>
                        </div>
                        <div class="panel_right">
                            <span style="text-align: right;">渗透率：</span>
                            <CountTo :endVal="data.villageInfiltration || 0" :duration="3000" decimals="2" />
                            <span>%</span>
                        </div>
                    </div>
                </div>
                <div class="box">
                    <div class="box-panel">
                        <div class="panel_desc">
                            <span style="text-align: right;">村集体收入：</span>
                        </div>
                        <div class="panel_count">
                            <span class="count">累计</span>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[0] !== null" :endVal="income[0]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[1] !== null" :endVal="income[1]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[2] !== null" :endVal="income[2]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[3] !== null" :endVal="income[3]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    {{ income[4] }}
                                    <!-- <CountTo v-if="income[4]!==null" :endVal="income[4]" :duration="3000" /> -->
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[5] !== null" :endVal="income[5]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[6] !== null" :endVal="income[6]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[7] !== null" :endVal="income[7]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[8] !== null" :endVal="income[8]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[9] !== null" :endVal="income[9]" :duration="3000" />
                                </span>
                            </div>
                            <span class="unit">（元）</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { getCommunicateDetail } from "@/api/screen/townScreen"
import "./detail.scss"
import CountTo from "vue-count-to";

export default {
    components: {
        CountTo
    },
    props:{
        detailId:{
            type:String,
            default:''
        }
    },
    mounted() {
        this.getData();
    },
    data() {
        return {
            income: [null, null, null, null, null, null, null, null, null, null],
            data: {},
        };
    },
    methods: {
        getData() {
            this.income = [null, null, null, null, null, null, null, null, null, null];
            let param = {
                id: this.detailId
            }
            getCommunicateDetail(param).then(res => {
                this.data = res.data.data;
                const num = this.data.income.toString();
                for (var i = num.length - 1; i >= 0; i--) {
                    this.income[i] = Number(num.split("").reverse()[i]);
                }
                this.income = this.income.reverse();
            })
        },
        closeDetail(){
            this.$parent.handleClose();
        }
    }
}
</script>

<style lang="scss" scoped>
::v-deep .el-input__prefix {
    right: -70% !important;
    left: 0 !important;
    color: #000 !important;
}

::v-deep .el-input__inner {
    background: transparent;
    color: #fff;
    border: 1px solid #496daa !important;
}

::v-deep .el-input--prefix .el-input__inner {
    padding-left: 10px !important;
}

::v-deep .el-input--small .el-input__icon {
    font-size: 16px;
    color: #496daa;
}


</style>
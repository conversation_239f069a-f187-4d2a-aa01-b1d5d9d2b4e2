<template>
    <!-- <basic-container> -->
         <el-dialog
            :visible="showDetailDialog"
            :modal="false"
            :modal-append-to-body="false"
            :close-on-click-modal="false"
            top="100px"
            width="60%"
            title="详情"
            @close="handleClose">
            <div class="title_background">
                <div>
                    <h2 class="ml-10px">{{questionnaireTitle}}
                    </h2>
                    <span class="ml-10px">共提交问卷{{questionnaireNum}}份</span>
                </div>
            </div>
            <div>
                <el-radio-group v-model="currentView">
                    <el-radio-button label="count">数据统计</el-radio-button>
                    <el-radio-button label="record">详细数据</el-radio-button>
                </el-radio-group>
                <!-- <el-button type="primary" size="small" @click="exportData" class="ml-10px" v-if="currentView === 'record'">导出</el-button> -->
            </div>
            <count v-if="currentView==='count'" />
            <record v-if="currentView === 'record'"/>
        </el-dialog>
    <!-- </basic-container> -->
</template>
<script>
import count from './questionnaire-detail-count.vue'
import record from './questionnaire-detail-record.vue'

export default {
    props: {
        questionnaireTitle: {
            type: String,
            required: true
        },
        questionnaireNum: {
            type: Number,
            required: true,
            default: 0
        },
        showDetailDialog: {
            type: Boolean,
            default: false
        }
    },
    components: {
        count,
        record
    },
    inject: ['getQuestionnaireId'],
    data() {
        return {
            currentView: 'count'
        }
    },
    mounted() {
        console.log(this.questionnaireTitle)
    },
    methods: {
        handleClose() {
            this.$emit('update:showDetailDialog', false)
        },
        
    }
}
</script>
<style scoped>
.ml-10px {
    margin-left: 10px;
}
.title_background {
    background-color:gainsboro;
    border-radius: 2px;
    padding: 10px;
    margin-bottom: 10px;
}

</style>

.province-center-bottom-content {
  position: absolute;
  width: 100%;
  height: 30vh;
  .center-bottom-header {
    position: absolute;
    width: 100%;
    white-space: nowrap;
    top: 1vh;
    left: 50%;
    transform: translate(-50%, 0);
    .title {
      position: absolute;
      letter-spacing: 3px;
      color: #5bffdc;
      font-size: 1.7vh;
      left: 0;
    }
    .title-direction {
      position: absolute;
      width: 35px;
      top: 0.8vh;
      right: 3%;
      height: auto;
    }
    .center-bottom-line {
      position: absolute;
      width: 98%;
      top: 3.6vh;
    }
  }
  .content {
    position: absolute;
    float: left;
    margin-top: 5vh;
    width: 100%;
    color: #fff;

    .select-1 {
      display: inline-block;
      width: 33%;
      height: 3vh;
      font-size: 2vh;
      text-align: center;
      cursor: pointer;
    }

    .select-2 {
      width: 34%;
      font-size: 2vh;
      height: 3vh;
      cursor: pointer;
      display: inline-block;
      text-align: center;
    }
    .select-3 {
      width: 33%;
      font-size: 2vh;
      height: 3vh;
      cursor: pointer;
      display: inline-block;
      text-align: center;
    }

    .title-select-back {
      position: absolute;
      width: 33%;
      height: 3vh;
    }

    .content-value {
      margin-top: 1vh;
      height: 22vh;
      .header-item {
        text-align: center;
        font-size: 16px;
        height: 35px;
        color: white;
      }
      .rows {
        .row-item {
          .ceil {
            text-align: center;
          }
        }
      }
    }
  }
}

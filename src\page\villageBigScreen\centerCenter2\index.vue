<!--
 * @Author: chenn26
 * @Date: 2023-01-16 16:37:15
 * @LastEditors: chenz76
 * @LastEditTime: 2023-12-20 15:55:37
 * @Description: 
-->
<template>
    <div class="center-center2-content1">
        <div class="header">
            <img src="/img/screen/title-decorate-center.png" class="left-decorate" alt="" />
            <span class="title">通信合作社</span>
            <img src="/img/screen/title-decorate-center.png" class="right-decorate" alt="" />
        </div>
        <div class="content">
            <div class="month-select">
                <span>月份：</span>
                <el-cascader :clearable="false" v-model="month" :options="options" :props="{ expandTrigger: 'hover' }"
                    @change="getData" size="small" style="width:80%;height:100%;"></el-cascader>
                <!-- <el-date-picker :clearable="false" prefix-icon="" format="yyyy年MM月" value-format="yyyy-MM"
                    v-model="month" size="small" type="month" placeholder="选择月" style="width:80%;height:100%;"
                    @change="getData">
                </el-date-picker> -->
            </div>
            <div class="showPanel">
                <div class="box" :style="this.showIncome === false ? 'margin-top:1.6vh' : 'margin-top:0'">
                    <div class="box-panel">
                        <div class="panel_left">
                            <span style="text-align: right;">服务家庭数：</span>
                            <CountTo :endVal="data.families || 0" :duration="3000" /><span>(户)</span>
                        </div>
                        <div class="panel_right">
                            <span style="text-align: right;">渗透率：</span>
                            <CountTo :endVal="data.familyInfiltration || 0" :duration="3000" decimals="2" />
                            <span>%</span>
                        </div>
                    </div>
                </div>
                <div class="box" :style="this.showIncome === false ? 'margin-top:1.6vh' : 'margin-top:0'">
                    <div class="box-panel">
                        <div class="panel_left">
                            <span style="text-align: right;">服务村民数：</span>
                            <CountTo :endVal="data.villagers || 0" :duration="3000" /><span>(人)</span>
                        </div>
                        <div class="panel_right">
                            <span style="text-align: right;">渗透率：</span>
                            <CountTo :endVal="data.villageInfiltration || 0" :duration="3000" decimals="2" />
                            <span>%</span>
                        </div>
                    </div>
                </div>
                <div class="box" :style="this.showIncome === false ? 'display:none' : 'display:block'" >
                    <div class="box-panel">
                        <div class="panel_desc">
                            <span style="text-align: right;">村集体收入：</span>
                        </div>
                        <div class="panel_count">
                            <span class="count">累计</span>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[0]!==null" :endVal="income[0]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[1]!==null" :endVal="income[1]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[2]!==null" :endVal="income[2]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[3]!==null" :endVal="income[3]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    {{ income[4] }}
                                    <!-- <CountTo v-if="income[4]!==null" :endVal="income[4]" :duration="3000" /> -->
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[5]!==null" :endVal="income[5]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[6]!==null" :endVal="income[6]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[7]!==null" :endVal="income[7]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[8]!==null" :endVal="income[8]" :duration="3000" />
                                </span>
                            </div>
                            <div class="number-decorate">
                                <img src="/img/screen/center-number.png" width="100%" height="100%" alt=""
                                    style="float:left;">
                                <span class="number">
                                    <CountTo v-if="income[9]!==null" :endVal="income[9]" :duration="3000" />
                                </span>
                            </div>
                            <span class="unit">（元）</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { getCommunicationData, getDateList } from "@/api/screen/screen"
import "./index.scss"
import CountTo from "vue-count-to";

export default {
    components: {
        CountTo
    },
    mounted() {
        this.getDateList();
    },
    data() {
        return {
            month: '',
            income: [null, null, null, null, null, null, null, null, null, null],
            data: {},
            options: [],
            showIncome:false
        };
    },
    methods: {
        getDateList() {
            getDateList().then(res => {
                var arr = [];
                this.month = [res.data.data[0].key, res.data.data[0].value[0]];
                res.data.data.forEach(item => {
                    var obj = {
                        "label": item.key + '年',
                        "value": item.key,
                        "children": []
                    }
                    item.value.forEach(item1 => {
                        var obj2 = {
                            "label": item1.replace(/^[0]+/, '') + '月',
                            "value": item1,
                        }
                        obj.children.push(obj2);
                    })
                    arr.push(obj)
                })
                this.options = arr;
                this.getData();
            })
        },
        getData(val) {
            if (val) {
                this.month = [val[0], val[1]];
            }
            this.income = [null, null, null, null, null, null, null, null, null, null];
            var params = {
                date: this.month.join('-')
            }
            getCommunicationData(params).then(res => {
                this.data = res.data.data;
                console.log(this.data)
                this.showIncome = this.data.showIncome;
                const num = this.data.income.toString();
                for (var i = num.length - 1; i >= 0; i--) {
                    this.income[i] = Number(num.split("").reverse()[i]);
                }
                this.income = this.income.reverse();
            })
        }
    }
}
</script>

<style scoped>
::v-deep .el-input__prefix {
    right: -70% !important;
    left: 0 !important;
    color: #000 !important;
}

::v-deep .el-input__inner {
    background: transparent;
    color: #fff;
    border: 1px solid #496daa !important;
}

::v-deep .el-input--prefix .el-input__inner {
    padding-left: 10px !important;
}

::v-deep .el-input--small .el-input__icon {
    font-size: 16px;
    color: #496daa;
}
</style>
<!--
 * @Date: 2025-02-07 15:12:09
 * @LastEditors: linqh21
 * @LastEditTime: 2025-02-07 16:48:07
 * @Description:
 * @FilePath: \src\views\infoRelease\countrynews.vue
-->
<template>
  <div>
    <CommonTypeOne :moduleName="moduleName" :moduleKey="moduleKey" :moduleDic="moduleDic" :funObj="funObj" ></CommonTypeOne>
  </div>
</template>

<script>
import CommonTypeOne from '@/views/components/CommonTypeOne'
import * as funList from "@/api/infoRelease/info";
export default {
  components: {
    CommonTypeOne
  },
  data() {
    return {
      moduleName: '村镇动态',
      moduleKey: 'countrynews',
      moduleDic: 'villageInfoType',
      funObj:funList
    }
  },
  mounted() {
    // console.log(funList.getList,"ssssssssssss")
    // // this.$nextTick(() => {
    // //   this.$refs.commonTypeOne.initData()
    // // })
  }
}
</script>

<style>

</style>

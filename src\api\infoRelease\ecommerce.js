import request from '@/router/axios';


/**
 * @description: 请求列表
 * @param {object} params
 * @author:
 */
export const getList = (params) => {
  return request({
    url: '/api/ecommercetraining/page',
    method: 'get',
    params,
  })
}

/**
 * @description: 新增
 * @param {object} data
 * @author:
 */
export const save = (data) => {
  return request({
    url: '/api/ecommercetraining/save',
    method: 'post',
    data,
  })
}

/**
 * @description: 查看详情
 * @param {object} params
 * @author:
 */
export const detail = (params) => {
  return request({
    url: '/api/ecommercetraining/detail',
    method: 'get',
    params,
  })
}

/**
 * @description: 修改
 * @param {object} data
 * @author:
 */
export const update = (data) => {
  return request({
    url: '/api/ecommercetraining/update',
    method: 'post',
    data,
  })
}



/**
 * @description: 删除
 * @param {object} params
 * @author:
 */
export const remove = (params) => {
  return request({
    url: '/api/ecommercetraining/delete',
    method: 'post',
    params,
  })
}


/**
 * @description: 发布
 * @param {object} params
 * @author:
 */
 export const release = (params) => {
  return request({
    url: '/api/ecommercetraining/release',
    method: 'post',
    params,
  })
}

/**
 * @description: 取消发布
 * @param {object} params
 * @author:
 */
export const unRelease = (params) => {
  return request({
    url: '/api/ecommercetraining/unRelease',
    method: 'post',
    params,
  })
}

/**
 * @description: 学习记录
 * @param {object} params
 * @author:
 */
export const studyHistory = (params) => {
  return request({
    url: '/api/ecommercetraining/study-history',
    method: 'get',
    params,
  })
}

/**
 * @description: 学习统计
 * @param {object} params
 * @author:
 */
export const studyStatic = (params) => {
  return request({
    url: '/api/ecommercetraining/study-statistic',
    method: 'get',
    params,
  })
}

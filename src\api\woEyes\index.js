/*
 * @Description: 沃家神眼
 * @Author: weixw14
 * @Date: 2021-09-15 14:48:14
 * @LastEditors: chenz76
 * @LastEditTime: 2023-02-03 08:59:01
 */
import request from '@/router/axios';
/**
 * @description: 分页查询
 * @param {number} parentId
 * @param {object} params
 * @author: weixw14
 */
export const getList = (params) => {
  return request({
    url: '/api/camera/dept/page',
    method: 'get',
    params: {
      ...params,

    }
  })
}

/**
 * @description: 关联组织机构
 * @param {object} data
 * @author: weixw14
 */
export const relateOrg = (data) => {
  return request({
    url: '/api/camera/dept',
    method: 'post',
    data,
  })
}

/**
 * @description: 修改组织机构关联
 * @param {object} data
 * @author: weixw14
 */
 export const editOrg = (data) => {
  return request({
    url: '/api/camera/dept',
    method: 'put',
    data,
  })
}

/**
 * @description: 逻辑删除
 * @param {object} params
 * @author: weixw14
 */
 export const delOrg = (params) => {
  return request({
    url: '/api/camera/dept',
    method: 'delete',
    params,
  })
}

/**
 * @description: 乡村机构与神眼机构关联列表
 * @param {number} deptId
 * @param {object} params
 * @author: weixw14
 */
export const relateList = (deptId, params) => {
  return request({
    url: '/api/camera/dept/list',
    method: 'get',
    params: {
      ...params,
      deptId
    }
  })
}

/**
 * @description: 当前组织机构匹配信息
 * @param {number} deptId
 * @param {object} params
 * @author: weixw14
 */
 export const relation = (deptId, params) => {
  return request({
    url: '/api/camera/dept/relation',
    method: 'get',
    params: {
      ...params,
      deptId
    }
  })
}

/**
 * @description: 五级树形结构
 * @param {number} branchId
 * @param {object} params
 * @author: weixw14
 */
export const treeFive = (tenantId, params) => {
  return request({
    url: '/api/blade-system/dept/treeFive',
    method: 'get',
    params: {
      ...params,
      tenantId
    }
  })
}

/**
 * @description: 查询神眼下级组织列表
 * @param {number} branchId
 * @param {object} params
 * @author: weixw14
 */
export const branchList = (branchId, params) => {
  return request({
    url: '/api/camera/dept/branchList',
    method: 'get',
    params: {
      ...params,
      branchId
    }
  })
}



/**
 *  干部端获取组织树
 * @param {*} params 
 * @returns 
 */
export const getOrganizeList = (params) => {
  return request({
    url: '/api/camera/admin/organize-list',
    method: 'get',
    params: {
      ...params
    }
  })
}
/**
 *  干部端获取监控列表
 * @param {*} params 
 */
export const getCameraList = (params) => {
  return request({
    url: '/api/camera/admin/list',
    method: 'get',
    params: {
      ...params
    }
  })
}
/**
 *  干部端获取摄像头播放地址
 * @param {*} id 设备id 
 * @param {*} type  (0:直播 1:回放)
 * @param {*} startTime 
 * @param {*} endTime 
 */
export const getPlayUrl = (params) => {
  return request({
    url: '/api/camera/admin/play-url',
    method: 'get',
    params: {
      ...params
    }
  })
}

/**
 *  干部端查询设备套餐信息
 * @param {*} id 设备id
 * @param {*} type  (0:直播 1:回放)
 * @param {*} startTime 
 * @param {*} endTime 
 */
export const hasRecall = (params) => {
  return request({
    url: '/api/camera/admin/pkg',
    method: 'get',
    params: {
      ...params
    }
  })
}

/**
 *  干部端获取村下所有摄像头
 *  @author: chenz76
 */
export const getCameraAdminAll = (params) => {
  return request({
    url: '/api/camera/admin/all',
    method: 'get',
    params:params
  })
}


/**
 *  获取部门树形结构,限制五级（含摄像头信息）
 * @author: chenz76
 */
export const getCameraAdminTreeFive = () => {
  return request({
    url: '/api/camera/admin/treeFive',
    method: 'get'
  })
}


/**
 *  根据部门查询摄像头列表
 * @author: chenz76
 */
export const getCameraAdminDeviceList = (params) => {
  return request({
    url: '/api/camera/admin/device-list',
    method: 'get',
    params: {
      ...params
    }
  })
}

<template>
    <basic-container>
        <el-drawer
            title="预览"
            :visible.sync="showPreviewDrawer"
            direction="rtl"
            :modal="true"
            :modal-append-to-body="false"
            :before-close="handleClose"
            :wrapperClosable="false">
            <div style="margin: 20px">
                <div style="height:37px;width:100%;display:flex; justify-content: flex-end;margin-bottom:10px; " v-if="activeName==='0'">
                    <el-button type="primary" size="small" :loading='setTemplateLoading' @click="setTemplate"> 设为模版</el-button>
                </div>
                <el-image :src="questionnaireLink" class="m-b-10px"></el-image>
                <div class="preview-title_style">{{questionnaireTitle}}</div>
                <el-divider></el-divider>
                <div class="preview-des_style m-b-10px">{{questionnaireDesciption}}</div>
                <div v-for="(item, num) in questionList" :key="num" class="m-b-10px">
                    <el-card>
                        <div :class="item.isRequired===1?'question-title': ''">
                            <span class="word_break">{{num + 1 +'.' + item.title}}</span>
                            <el-tag
                                v-if="item.type !== 3"
                                :type="item.type===1? '' : 'warning'"
                                effect="plain"
                                size="mini">
                                {{item.type===1 ? '单选': '多选'}}
                            </el-tag>
                        </div>
                        <div v-if="item.type === 1">
                            <div v-for="(option, index) in item.options" :key="index">
                                <el-radio v-model="option.selected" :label="true" disabled>
                                    <span class="word_break p-r-20px">{{option.title}}</span>
                                    <el-image v-if="option.attachLink" :src="option.attachLink" style="width: 100px; height: 100px" fit="container" ></el-image>
                                </el-radio>
                            </div>
                        </div>
                        <div v-else-if="item.type === 2 ">
                            <div v-for="(option, index) in item.options" :key="index">
                                <el-checkbox v-model="option.selected" disabled>
                                    <span class="word_break">{{option.title}}</span>
                                    <el-image v-if="option.attachLink" :src="option.attachLink" style="width: 100px; height: 100px" fit="container"></el-image>
                                </el-checkbox>
                            </div>
                        </div>
                        <div v-else>
                            <el-input type="textarea" :placeholder="item.prompt" :value="item.answerContent" disabled></el-input>
                        </div>
                    </el-card>
                </div>
            </div>
        </el-drawer>
    </basic-container>
</template>
<script>
import {getDetail, statisticsDetail, setTemplate, getTemplateDetail} from '@/api/questionnaire/survey'
export default {
    props: {
        showPreviewDrawer: {
            type: Boolean,
            required: true,
            default: false,
        },
        questionnaireId: {
            type: Number,
            
        },
        detailId: {
            type: Number,
        },
        activeName:{
            type: String,
        }
    },
    data() {
        return {
            questionnaireTitle: '',
            questionnaireDesciption: '',
            questionnaireLink: '',
            questionList: {},
            setTemplateLoading:false
        }
    },
    mounted() {
        this.getQustionList()
    },
    methods: {
        handleClose(done) {
            this.$emit('update:showPreviewDrawer', false)
            this.questionList = {}
            done()
        },
        setTemplate(){
            //设置模板状态 接口调用 add
            this.setTemplateLoading=true
            setTemplate(this.questionnaireId).then(res=>{
                this.$message.success('设置成功，请前往模版管理查看！')
            }).finally(() => {
                this.setTemplateLoading=false
            })
        },
        getQustionList() {
            if(this.activeName==='2'){
                //模版预览
                getTemplateDetail(this.questionnaireId)
                    .then(res => {
                        this.questionnaireLink = res.data.data.attachLink
                        this.questionList = res.data.data.subjectList
                        this.questionnaireDesciption = res.data.data.description
                        this.questionnaireTitle = res.data.data.title
                    })
                return
            }
            if(this.questionnaireId) {
                getDetail(this.questionnaireId)
                    .then(res => {
                        this.questionnaireLink = res.data.data.attachLink
                        this.questionList = res.data.data.subjectList
                        this.questionnaireDesciption = res.data.data.description
                        this.questionnaireTitle = res.data.data.title
                    })
            }
            if(this.detailId) {
                statisticsDetail(this.detailId)
                    .then(res => {
                        this.questionnaireLink = res.data.data.attachLink
                        this.questionList = res.data.data.subjectList
                        this.questionnaireDesciption = res.data.data.description
                        this.questionnaireTitle = res.data.data.title
                    })
            }
        }
    },
    
}
</script>
<style scoped>
::v-deep .el-drawer__body {
    overflow: auto;
}
.preview-title_style {
    font-weight: bold;
}
.preview-des_style {
    color: #606266;
    font-size: 14px;
}
.m-b-10px {
    margin-bottom: 10px;
}
.question-title::before {
    content: '*';
    color: #F56C6C;
    margin-right: 4px;
}
.word_break {
    white-space: normal;
    word-break: break-all;
    display: inline-block;
    vertical-align: middle;
}
.p-r-20px {
    padding-right: 20px;
}
</style>

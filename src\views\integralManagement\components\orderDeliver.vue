<!--
 * @Description:订单管理&获奖订单-发货
 * @Author: wangyy553
 * @Date: 2022-01-13 14:56:40
 * @LastEditors: wangyy553
 * @LastEditTime: 2022-01-20 15:14:19
-->
<template>
  <el-dialog
    :fullscreen="isFullscreen"
    :visible.sync="dialogVisible"
    append-to-body="true"
    :close-on-click-modal="false"
    top="100px"
    width="60%"
    @close="close()"
  >
    <div slot="title" class="header">
      <div class="avue-crud__dialog__header">
        <span class="el-dialog__title">{{ title }}</span>
        <div class="avue-crud__dialog__menu">
          <i
            @click="isFullScreen"
            class="el-dialog__close el-icon-full-screen"
          ></i>
        </div>
      </div>
      <button type="button" aria-label="Close" class="el-dialog__headerbtn">
        <i class="el-dialog__close el-icon el-icon-close"></i>
      </button>
    </div>
    <div class="tip-container">
      <div style="font-size: 15px; font-weight: 600">发货提示</div>
      <div>
        1、添加后，即可生效，请谨慎修改。2、发货后（首次保存物流单号的创建时间），7天内可变更物流单号；3、发货后，21天订单状态自动变更为已完成；
      </div>
    </div>
    <avue-form
      :option="formoption"
      v-model="form"
      ref="form"
      @submit="submit"
    ></avue-form>
    <avue-crud
      :option="moduleOption"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      ref="crud"
      @refresh-change="refreshChange"
      @current-change="currentChange"
      @size-change="sizeChange"
    >
    </avue-crud>
  </el-dialog>
</template>

<script>
import * as manageApi from "@/api/integralManagement/ordersManage";
import * as ordersApi from "@/api/integralManagement/orders";

export default {
  props: {
    visible: {
      type: Boolean,
      require: true,
    },
    id: {
      require: true,
    },
    MODULE: {
      type: String,
      default: "",
    },
    allowWriteOddNum: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isFullscreen: false,
      title: "发货",
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      manageOption: {
        index: true,
        indexLabel: "序号",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: false,
        searchMenuSpan: 6,
        border: true,
        selection: false,
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: false,
        dicFlag: true,
        menu: false,
        column: [
          {
            label: "创建人",
            prop: "realName",
            type: "select",
          },
          {
            label: "物流单号",
            prop: "oddNum",
          },
          {
            label: "创建时间",
            prop: "createTime",
          },
        ],
      },
      prizesOption: {
        index: true,
        indexLabel: "序号",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: false,
        searchMenuSpan: 6,
        border: true,
        selection: false,
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: false,
        dicFlag: true,
        menu: false,
        column: [
          {
            label: "创建人",
            prop: "realName",
          },
          {
            label: "创建账号",
            prop: "account",
          },
          {
            label: "物流单号",
            prop: "oddNum",
          },
          {
            label: "创建时间",
            prop: "createTime",
          },
        ],
      },
      data: [],
      formoption: {
        disabled: false,
        menuBtn: true,
        column: [
          {
            label: "物流单号",
            prop: "oddNum",
            type: "input",
            maxlength: 30,
            showWordLimit: true,
            rules: [
              {
                whitespace: true,
                required: true,
                message: "请输入物流单号",
                trigger: "[blur,change]",
              },
            ],
          },
        ],
      },
      form: {},
    };
  },
  watch: {
    allowWriteOddNum(val) {
      if (val) {
        this.formoption.disabled = false;
        this.formoption.menuBtn = true;
      } else {
        this.formoption.disabled = true;
        this.formoption.menuBtn = false;
      }
    },
  },
  methods: {
    close() {
      this.emptyForm();
      this.page.currentPage = 1;
      this.page.pageSize = 10;
      this.$emit("updateTable");
      this.dialogVisible = false;
    },
    isFullScreen() {
      this.isFullscreen = !this.isFullscreen;
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.initData();
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.initData();
    },
    refreshChange() {
      this.initData();
    },
    initData() {
      this.loading = true;
      this.api
        .getDeliverData(this.page.currentPage, this.page.pageSize, this.id)
        .then((res) => {
          this.data = res.data.data.records;
          this.page.total = res.data.data.total;
          this.loading = false;
        });
    },
    emptyForm() {
      this.$refs.form.resetForm();
    },
    submit() {
      this.$refs.form.validate((valid, done) => {
        if (valid) {
          var obj = Object.assign({}, this.form);
          obj.id = this.id;
          this.api
            .submitDeliver(obj)
            .then((res) => {
              if (res.data.success) {
                this.$message({
                  type: "success",
                  message: res.data.msg,
                });
              } else {
                this.$message({
                  type: "warning",
                  message: res.data.msg,
                });
              }
              done();
              this.emptyForm();
              this.initData();
            })
            .catch(() => {
              done();
            });
        }
      });
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    moduleOption() {
      return this.MODULE === "ordersManage"
        ? this.manageOption
        : this.prizesOption;
    },
    api() {
      return this.MODULE === "ordersManage" ? manageApi : ordersApi;
    },
  },
};
</script>

<style lang='scss' scoped>
.tip-container {
  background: rgb(235, 245, 255);
  padding: 10px 30px;
  // height: 50px;
  border: 1px solid rgb(140, 197, 255);
  border-radius: 5px;
  color: #909399;
  margin-bottom: 20px;
}
</style>

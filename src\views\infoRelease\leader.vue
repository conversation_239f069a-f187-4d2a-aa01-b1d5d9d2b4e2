<!--
 * @Date: 2025-02-07 14:41:09
 * @LastEditors: linqh21
 * @LastEditTime: 2025-02-07 16:55:24
 * @Description:
 * @FilePath: \src\views\infoRelease\leader.vue
-->
<template>
  <div>
   <CommonTypeTwo :moduleName="moduleName" :moduleKey="moduleKey" :moduleDic="moduleDic" :funObj="funObj" ></CommonTypeTwo>
  </div>
</template>

<script>
import CommonTypeTwo from '@/views/components/CommonTypeTwo'
import * as funList from "@/api/infoRelease/info";
export default {
  components: {
    CommonTypeTwo
  },
  data() {
    return {
      moduleName: '领导班子',
      moduleKey: 'leader',
      moduleDic: 'leadingGroupType',
      funObj:funList
    }
  },
  created() {
    // this.$nextTick(() => {
    //   this.$refs.commonTypeOne.initData()
    // })
  }
}
</script>

<style>

</style>

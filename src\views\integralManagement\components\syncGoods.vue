<!--
 * @Description:积分商城-同步
 * @Author: wangyy553
 * @Date: 2021-12-21 17:20:18
 * @LastEditors: wangyy553
 * @LastEditTime: 2022-01-17 15:42:59
-->
<template>
  <el-dialog
    :fullscreen="isFullscreen"
    :visible.sync="dialogVisible"
    append-to-body="true"
    :close-on-click-modal="false"
    top="100px"
    width="60%"
    @close="close()"
  >
    <div slot="title" class="header">
      <div class="avue-crud__dialog__header">
        <span class="el-dialog__title">{{ title }}</span>
        <div class="avue-crud__dialog__menu">
          <i
            @click="isFullScreen"
            class="el-dialog__close el-icon-full-screen"
          ></i>
        </div>
      </div>
      <button type="button" aria-label="Close" class="el-dialog__headerbtn">
        <i class="el-dialog__close el-icon el-icon-close"></i>
      </button>
    </div>

    <div class="tip-container">
      <div style="font-size: 15px; font-weight: 600">商品同步提示</div>
      <div>
        对该商品进行同步后，会把该商品信息自动同步到所选同步地区，提交后不可撤回；已同步组织不可再次同步。
      </div>
    </div>
    <avue-form :option="option" v-model="form" ref="form" @submit="submit">
      <template slot="syncOrg">
        <div style="color: #909399" v-if="isFirstTime">您还未同步</div>
        <div v-else>
          <span
            :key="index"
            v-for="(item, index) in syncOrg"
            class="namelist-container"
            >{{ item.name }}</span
          >
        </div>
      </template>
    </avue-form>
  </el-dialog>
</template>

<script>
import * as api from "@/api/integralManagement/mall";
import { mapGetters } from "vuex";

export default {
  props: ["visible", "syncOrg", "id"],
  data() {
    return {
      isFullscreen: false,
      title: "商品同步",
      form: {},
      isFirstTime: false,
      option: {
        labelWidth: 150,
        emptyBtn: false,
        column: [
          {
            label: "已同步组织范围",
            prop: "syncOrg",
            slot: true,
            formslot: true,
            span: 24,
          },
          {
            label: "关联同步组织范围",
            prop: "deptIds",
            span: 24,
            filter: true,
            type: "tree",
            checkStrictly: true,
            multiple: true,
            dicUrl: "/api/blade-system/dept/tree",
            dicMethod: "get",
            dicQuery: {
              tenantId: '',
            },
            props: {
              label: "title",
              value: "value",
            },
            expandOnClickNode: false,
            rules: [
              {
                required: true,
                message: "请选择关联同步组织范围",
                type: "array",
                trigger: "[blur,change]",
              },
            ],
          },
        ],
      },
    };
  },
  created() {
    // 赋值租户id
    this.option.column[1].dicQuery.tenantId = this.userInfo.tenant_id
  },
  watch: {
    syncOrg(val) {
      if (val.length == 0) {
        this.form.syncOrg = [];
        this.isFirstTime = true;
      } else {
        this.form.syncOrg = val;
        this.isFirstTime = false;
      }
    },
  },
  methods: {
    submit() {
      this.$refs.form.validate((valid, done) => {
        if (valid) {
          var obj = Object.assign({}, this.form);
          obj.id = this.id;
          delete obj.$deptIds;
          delete obj.syncOrg;
          api
            .submitSync(obj)
            .then((res) => {
              if (res.data.success) {
                this.$message({
                  type: "success",
                  message: res.data.msg,
                });
              } else {
                this.$message({
                  type: "warning",
                  message: res.data.msg,
                });
              }
              done();
              this.close();
              this.$emit("updateTable");
            })
            .catch(() => {
              done();
            });
        }
      });
    },

    close() {
      this.$refs.form.resetForm();
      this.dialogVisible = false;
    },
    isFullScreen() {
      this.isFullscreen = !this.isFullscreen;
    },
  },
  computed: {
    ...mapGetters(["userInfo"]),
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
};
</script>

<style  lang="scss" scoped>
.tip-container {
  background: rgb(235, 245, 255);
  padding: 10px 30px;
  // height: 50px;
  border: 1px solid rgb(140, 197, 255);
  border-radius: 5px;
  color: #909399;
  margin-bottom: 20px;
}
.taglist-container {
  display: inline-block;
  border: 1px dashed #e3e3e3;
  // height: 30px;
  overflow: scroll;
  border-radius: 5px;
  padding: 5px;
}
.namelist-container {
  margin: 0 5px;
  color: #909399;
}
.tag-container {
  margin: 5px;
}

.notice-container {
  font-size: 12px;
  color: #606266;
}
.btn-container {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>

import request from '@/router/axios';


/**
 * @description: 请求列表
 * @param {object} params
 * @author:
 */
export const getList = (params) => {
  return request({
    url: '/api/admin/informationOther/page',
    method: 'get',
    params,
  })
}

/**
 * @description: 新增
 * @param {object} data
 * @author:
 */
export const save = (data) => {
  return request({
    url: '/api/admin/informationOther/save',
    method: 'post',
    data,
  })
}

/**
 * @description: 查看详情
 * @param {object} params
 * @author:
 */
export const detail = (params) => {
  return request({
    url: '/api/admin/informationOther/detail',
    method: 'get',
    params,
  })
}

/**
 * @description: 修改
 * @param {object} data
 * @author:
 */
export const update = (data) => {
  return request({
    url: '/api/admin/informationOther/update',
    method: 'post',
    data,
  })
}



/**
 * @description: 删除
 * @param {object} params
 * @author:
 */
export const remove = (params) => {
  return request({
    url: '/api/admin/informationOther/remove',
    method: 'post',
    params,
  })
}


/**
 * @description: 发布
 * @param {object} params
 * @author:
 */
 export const release = (params) => {
  return request({
    url: '/api/admin/informationOther/release',
    method: 'post',
    params,
  })
}

/**
 * @description: 取消发布
 * @param {object} params
 * @author:
 */
export const unRelease = (params) => {
  return request({
    url: '/api/admin/informationOther/unRelease',
    method: 'post',
    params,
  })
}

/**
 * @description: 发送审核
 * @param {object} params
 * @author:
 */
export const sendAudit = (params) => {
  return request({
    url: '/api/admin/informationOther/sendAudit',
    method: 'post',
    params,
  })
}

/**
 * @description: 审核
 * @param {object} params
 * @author:
 */
export const doAudit = (data) => {
  return request({
    url: '/api/admin/informationOther/doAudit',
    method: 'post',
    data,
  })
}

/**
 * @description: 审定
 * @param {object} params
 * @author:
 */
export const doAuthorize = (data) => {
  return request({
    url: '/api/admin/informationOther/doAuthorize',
    method: 'post',
    data,
  })
}


export const informationSelect = (id) => {
  return request({
    url: '/api/user/informationColumn/typeSelect',
    method: 'get',
    params:{
      id
    }
  })
}
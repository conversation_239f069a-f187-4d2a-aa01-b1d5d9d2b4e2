<template>
  <div class="multi-source-player" :class="{'offline': deviceStatus === 3}">
    <div class="player-header">
      <span class="player-title">{{ deviceName }}</span>
      <span class="player-status" :class="statusClass">
        <i class="el-icon-circle"></i>
        {{ statusText }}
      </span>
      <select
        v-if="hasSource"
        v-model="currentSourceType"
        @change="switchSource"
        class="source-selector"
        :disabled="deviceStatus === 3 || loading"
      >
        <template v-for="(url, type) in sources">
          <option v-if="['flv', 'ws_flv', 'fmp4', 'ws_fmp4', 'hls'].includes(type)"
                  :key="type"
                  :value="type">
            {{ type.toUpperCase() }}
          </option>
        </template>
      </select>
    </div>

    <div v-if="!hasSource" class="no-source">
      <div v-if="errorMessage" class="error-message">
        <i class="el-icon-warning"></i>
        <p class="error-text">{{ errorMessage }}</p>
        <button class="retry-btn" @click="clearError" :disabled="loading">
          重试
        </button>
      </div>
      <div v-else>
        <p class="no-source-text">{{ getNoSourceMessage() }}</p>
        <button
          v-if="deviceStatus !== 3"
          class="fetch-source-btn"
          @click.stop="handleRequestSource"
          :disabled="loading"
        >
          <span v-if="loading">
            <span class="btn-spinner"></span> 获取中...
          </span>
          <span v-else>点击获取视频流</span>
        </button>
      </div>
    </div>

    <div v-else class="player-container">
      <div id="video-container" ref="container"></div>
      <div v-if="loading" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <span class="loading-text">{{ loadingText }}</span>
          <div class="loading-progress">
            <div class="progress-bar" :style="{ width: loadingProgress + '%' }"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "MultiSourcePlayer",
  props: {
    deviceName: {
      type: String,
      default: "未命名设备",
      validator: value => typeof value === 'string' && value.length > 0
    },
    sources: {
      type: Object,
      default: () => ({}),
      validator: value => value === null || typeof value === 'object'
    },
    initialSourceType: {
      type: String,
      default: "flv",
      validator: value => ['flv', 'ws_flv', 'fmp4', 'ws_fmp4', 'hls'].includes(value)
    },
    deviceStatus: {
      type: Number,
      default: 1,
      validator: value => [2, 3, 1].includes(value)
    },
    isFullscreenMode: {
      type: Boolean,
      default: false
    },
    autoPlay: {
      type: Boolean,
      default: true
    },
    retryCount: {
      type: Number,
      default: 3,
      validator: value => value >= 0 && value <= 10
    }
  },
  data() {
    return {
      jessibuca: null,
      playing: false,
      loaded: false,
      currentSourceType: this.initialSourceType,
      performance: "",
      useWCS: false,
      useMSE: true,
      loading: false,
      initRetryCount: 0,
      maxRetryCount: this.retryCount,
      errorMessage: "",
      isDestroyed: false,
      // 添加定时器管理
      retryTimer: null,
      sourceRequestTimer: null,
      // 加载状态优化
      loadingText: "初始化播放器...",
      loadingProgress: 0
    };
  },
  computed: {
    hasSource() {
      return Object.keys(this.sources).length > 0 &&
             this.sources[this.currentSourceType];
    },
    currentSourceUrl() {
      return this.sources[this.currentSourceType] || '';
    },
    statusClass() {
      return {
        'online': this.deviceStatus === 2,
        'offline': this.deviceStatus === 3,
        'unknown': this.deviceStatus === 1
      };
    },
    statusText() {
      return this.deviceStatus === 2 ? '在线' :
             this.deviceStatus === 3 ? '离线' : '未知';
    },
    canPlay() {
      return this.deviceStatus === 2 && this.hasSource && !this.loading;
    }
  },
  watch: {
    sources: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.$nextTick(() => {
            if (!this.playing && this.autoPlay) {
              this.initPlayer();
            }
          });
        } else if (this.jessibuca) {
          this.destroyPlayer();
        }
      },
      deep: true
    },
    deviceStatus(newVal) {
      if (newVal === 3 && this.jessibuca) {
        this.destroyPlayer();
      } else if (newVal === 2 && this.hasSource) {
        this.$nextTick(() => {
          this.initPlayer();
        });
      }
    },
    currentSourceType(newVal, oldVal) {
      if (newVal !== oldVal && this.hasSource) {
        this.switchSource();
      }
    }
  },
  mounted() {
    if (this.hasSource && this.autoPlay) {
      this.initPlayer();
    }
  },
  beforeDestroy() {
    this.isDestroyed = true;

    // 清理所有定时器
    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
      this.retryTimer = null;
    }

    if (this.sourceRequestTimer) {
      clearTimeout(this.sourceRequestTimer);
      this.sourceRequestTimer = null;
    }

    this.destroyPlayer();
  },
  methods: {
    handleRequestSource() {
      if (this.loading) return;

      this.loading = true;
      this.loadingText = "正在获取视频源...";
      this.loadingProgress = 20;
      this.errorMessage = "";
      this.$emit('request-source');

      // 清除之前的定时器
      if (this.sourceRequestTimer) {
        clearTimeout(this.sourceRequestTimer);
      }

      this.sourceRequestTimer = setTimeout(() => {
        if (!this.isDestroyed) {
          this.loading = false;
          this.loadingProgress = 0;
        }
        this.sourceRequestTimer = null;
      }, 5000);
    },

    async initPlayer(options = {}) {
      if (this.isDestroyed) return;

      try {
        this.loading = true;
        this.loadingText = "初始化播放器...";
        this.loadingProgress = 10;
        this.errorMessage = "";

        await this.destroyPlayer();
        this.loadingProgress = 30;
        await this.$nextTick();

        if (!this.$refs.container || this.$refs.container.offsetParent === null) {
          throw new Error('Container not available');
        }

        this.detectBestPlayMethod();
        this.loadingProgress = 50;

        this.jessibuca = new window.Jessibuca({
          container: this.$refs.container,
          videoBuffer: 0.2,
          isResize: false,
          useWCS: this.useWCS,
          useMSE: this.useMSE,
          loadingText: "加载中...",
          debug: false,
          supportDblclickFullscreen: false,
          isNotMute: true,
          decoder: '/videoPlayer/decoder.js',
          wasmDecodeErrorReplay:false,
          showBandwidth:true,
          operateBtns: {
            play: true,
            pause: true,
            audio: true,
            fullscreen: true,
          },
          ...options
        });

        this.loadingProgress = 70;
        this.setupEventListeners();

        if (this.currentSourceUrl && this.autoPlay) {
          this.loadingText = "正在连接视频流...";
          this.loadingProgress = 80;
          await this.play();
        }

        this.initRetryCount = 0;
        this.loadingProgress = 100;
        setTimeout(() => {
          this.loading = false;
          this.loadingProgress = 0;
        }, 200);
      } catch (error) {
        console.error('Player initialization failed:', error);
        this.errorMessage = this.getErrorMessage(error);
        this.initRetryCount++;

        if (this.initRetryCount <= this.maxRetryCount && !this.isDestroyed) {
          // 清除之前的重试定时器
          if (this.retryTimer) {
            clearTimeout(this.retryTimer);
          }

          this.loadingText = `重试中 (${this.initRetryCount}/${this.maxRetryCount})...`;
          this.retryTimer = setTimeout(() => {
            this.retryTimer = null;
            this.initPlayer(options);
          }, 500 * this.initRetryCount);
        } else {
          this.loading = false;
          this.loadingProgress = 0;
          this.$emit('error', {
            ...error,
            friendlyMessage: this.errorMessage,
            retryCount: this.initRetryCount
          });
        }
      }
    },

    detectBestPlayMethod() {
      this.useMSE = ['flv', 'ws_flv', 'fmp4', 'ws_fmp4', 'hls'].includes(this.currentSourceType);
      this.useWCS = false;
    },

    setupEventListeners() {
      if (!this.jessibuca) return;

      this.jessibuca.on("play", () => {
        this.playing = true;
        this.loaded = true;
        this.loading = false;
        this.$emit('play');
      });

      this.jessibuca.on("pause", () => {
        this.playing = false;
        this.$emit('pause');
      });

      this.jessibuca.on("load", () => {
        this.loaded = true;
        this.loading = false;
        this.$emit('load');
      });

      this.jessibuca.on("error", (error) => {
        console.error("播放错误:", error);
        this.errorMessage = this.getErrorMessage(error);
        this.loading = false;
        this.loadingProgress = 0;
        this.$emit('error', {
          ...error,
          friendlyMessage: this.errorMessage
        });
      });

      this.jessibuca.on("performance", (performance) => {
        const levels = ["卡顿", "流畅", "非常流畅"];
        this.performance = levels[performance] || "";
        this.$emit('performance', performance);
      });

      this.jessibuca.on("fullscreen", (isFullscreen) => {
        this.$emit('fullscreen', isFullscreen);
      });

      this.jessibuca.on("volume", (volume) => {
        this.$emit('volume', volume);
      });
    },

    async destroyPlayer() {
      if (this.jessibuca) {
        try {
          await this.jessibuca.destroy();
        } catch (e) {
          console.warn('Error while destroying player:', e);
        } finally {
          this.jessibuca = null;
          this.playing = false;
          this.loaded = false;
          this.loading = false;
        }
      }
      if (this.$refs.container) {
        this.$refs.container.innerHTML = '';
      }
    },

    async play() {
      if (this.currentSourceUrl && this.jessibuca && !this.isDestroyed) {
        try {
          this.loading = true;
          await this.jessibuca.play(this.currentSourceUrl);
        } catch (error) {
          console.error('Play failed:', error);
          this.errorMessage = this.getErrorMessage(error);
          this.loading = false;
          this.loadingProgress = 0;
          this.$emit('error', {
            ...error,
            friendlyMessage: this.errorMessage
          });
        }
      }
    },

    async switchSource() {
      if (this.playing) {
        await this.destroyPlayer();
      }
      this.initPlayer();
    },

    pause() {
      if (this.jessibuca && this.playing) {
        this.jessibuca.pause();
      }
    },

    resume() {
      if (this.jessibuca && !this.playing) {
        this.play();
      }
    },

    getPerformance() {
      return this.performance;
    },

    getPlayerState() {
      return {
        playing: this.playing,
        loaded: this.loaded,
        loading: this.loading,
        currentSourceType: this.currentSourceType,
        performance: this.performance,
        errorMessage: this.errorMessage
      };
    },

    getNoSourceMessage() {
      switch (this.deviceStatus) {
        case 3:
          return '设备已离线';
        case 1:
          return '设备状态未知';
        case 2:
          return '';
        default:
          return '暂无视频源';
      }
    },

    clearError() {
      this.errorMessage = "";
      if (this.deviceStatus === 2) {
        this.handleRequestSource();
      }
    },

    getErrorMessage(error) {
      if (typeof error === 'string') return error;

      const errorMap = {
        'Network Error': '网络连接失败，请检查网络状态',
        'Timeout': '连接超时，请稍后重试',
        'Container not available': '播放器容器不可用',
        'Source not found': '视频源不存在',
        'Decoder error': '视频解码失败',
        'Permission denied': '权限不足，无法访问视频流'
      };

      const message = (error && error.message) || (error && error.toString()) || '未知错误';
      return errorMap[message] || `播放失败: ${message}`;
    }
  }
};
</script>

<style scoped>
.multi-source-player {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #181c23;
  color: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  border: 1.5px solid #232a36;
  overflow: hidden;
  position: relative;
}

.multi-source-player.offline {
  opacity: 0.7;
  filter: grayscale(0.8);
}

.player-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  font-size: 15px;
  background: rgba(0,0,0,0.2);
  flex-shrink: 0;
  z-index: 10;
}

.player-title {
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.player-status {
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 3px;
}

.player-status.online {
  color: #67c23a;
}

.player-status.offline {
  color: #f0f0f0;
}

.player-status.unknown {
  color: #e6a23c;
}

.player-status .el-icon-circle {
  font-size: 12px;
}

.source-selector {
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #3a4252;
  background: #232a36;
  color: white;
  font-size: 12px;
  margin-left: 10px;
  cursor: pointer;
  transition: all 0.2s;
}

.source-selector:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.source-selector:not(:disabled):hover {
  border-color: #409eff;
}

.no-source {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 20px 20px 20px;
  background: #10131a;
  color: #bfcbd9;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.no-source-text {
  margin-bottom: 22px;
  font-size: 18px;
  font-weight: bold;
  color: #e0e6ed;
  letter-spacing: 1px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.08);
  text-align: center;
}

.fetch-source-btn {
  padding: 10px 24px;
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  margin-top: 0;
  min-width: 140px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.fetch-source-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.fetch-source-btn:disabled {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  opacity: 0.6;
  cursor: not-allowed;
}

.fetch-source-btn:not(:disabled):hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.fetch-source-btn:not(:disabled):hover::before {
  left: 100%;
}

.fetch-source-btn:not(:disabled):active {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.2);
}

.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: rgba(245, 108, 108, 0.1);
  border: 1px solid rgba(245, 108, 108, 0.3);
  border-radius: 8px;
  margin-bottom: 16px;
}

.error-message i {
  font-size: 32px;
  color: #f56c6c;
  margin-bottom: 12px;
}

.error-message .error-text {
  color: #f56c6c;
  font-size: 14px;
  text-align: center;
  margin-bottom: 16px;
  line-height: 1.4;
}

.error-message .retry-btn {
  padding: 8px 16px;
  background: #f56c6c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.2s;
}

.error-message .retry-btn:hover:not(:disabled) {
  background: #f78989;
}

.error-message .retry-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.player-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  position: relative;
}

#video-container {
  flex: 1;
  min-height: 0;
  background: #000;
  position: relative;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 5;
  backdrop-filter: blur(2px);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top: 3px solid #409eff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 14px;
  margin-bottom: 12px;
  color: rgba(255, 255, 255, 0.9);
}

.loading-progress {
  width: 120px;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #409eff, #66b1ff);
  border-radius: 2px;
  transition: width 0.3s ease;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

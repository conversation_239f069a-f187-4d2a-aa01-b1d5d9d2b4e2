/**
* @Author: zhengjh43
* @Date: 2024-01-17 16:03:58
* @LastEditors: zhengjh43
* @LastEditTime: 2024-02-05 15:14:49
* @Explain: 个性化积分规则
*/
<template>
  <el-row ref="test">
    <el-col :span="5">
      <div class="box">
        <el-scrollbar>
          <basic-container v-loading="treeLoading">
            <avue-tree :option="treeOption" :data="treeData" :filter-node-method="filterNodeMethod"
              @node-click="nodeClick" />
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :span="19">
      <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :search.sync="query"
          :permission="permissionList" v-model="form" ref="crud" :before-open="beforeOpen" @row-update="rowUpdate"
          @row-save="rowSave" @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset"
          @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange"
          @refresh-change="refreshChange" @on-load="onLoad" class="directSupply-dialog">
          <!-- 表格顶部按钮 -->
          <template slot="menuLeft">
            <el-button type="danger" size="small" icon="el-icon-delete" @click="handleDelete">批 量 删 除
            </el-button>
            <!-- <el-button type="primary" size="small" @click="openDialog('1')">通信合作社人员系数配置
            </el-button>
            <el-button type="primary" size="small" @click="openDialog('2')">活动期间系数配置
            </el-button> -->
          </template>

          <!-- 表格列 -->
          <template slot="updateUser" slot-scope="{ row }">
            <span>{{ row.status === 1 ? row.updateUser : "" }}</span>
          </template>

          <!-- 表单按钮 -->
          <!-- <template slot="menuForm">
              <el-button
                type="primary"
                size="small"
                v-if="permissionList.viewBtn"
                icon="el-icon-view"
                @click.stop="showUview()"
                plain
                >预览
              </el-button>
            </template> -->

          <template slot-scope="{ placeholder }" slot="statusSearch">
            <el-select v-model="releaseStatus" :placeholder="placeholder">
              <el-option label="已发布" value="1" key="1" />
              <el-option label="未发布" value="0" key="0" />
            </el-select>
          </template>
          <template slot-scope="{ row, index }" slot="isOpen">
            <el-switch :value="row.isOpen" @change="changeIsOpen(row, index)"></el-switch>
          </template>
        </avue-crud>

        <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" :append-to-body="true" custom-class="dialogwidth"
          @close="$refs.settingForm.resetForm()">
          <avue-form ref="settingForm" v-model="settingForm" :option="dialogFormOption" @submit="submitSetting">
            <template slot="menuForm">
              <el-button size="small" icon="el-icon-view" @click.stop="closeDialog">取 消</el-button>
            </template>
          </avue-form>
        </el-dialog>
      </basic-container>
    </el-col>
    <Uview :dialogVisible="uviewVisible" :changeVisible="changeUviewVisible" ref="uview" />
  </el-row>
</template>

<script>
import Uview from "@/components/uview/main.vue";
import {
  detail, getDeptTree, getDeptTreeFive
} from "@/api/infoRelease/partyLead";
import * as api from '@/api/integralManagement/rules'
import { userKindList } from '@/api/system/user'
import { mapGetters } from "vuex";
export default {
  components: {
    Uview,
  },
  data () {
    return {
      userKind: userKindList.villager,
      deptId: '',
      treeLoading: false,
      treeOption: {
        nodeKey: 'id',
        addBtn: false,
        menu: false,
        size: 'small',
        props: {
          label: 'title',
          value: 'value',
          children: 'children'
        }
      },
      treeData: [],
      releaseStatus: "",
      uviewVisible: false,
      dialogVisible: false,
      bannerUrl: "",
      srcList: [],
      form: {},
      query: {},
      loading: true,
      box: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      type: "",
      selectionList: [],
      // viewDialog: false, //详情弹窗
      option: {
        // viewBtnText: "预览",
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        indexLabel: '序号',
        selection: true,
        menuWidth: 160,
        labelWidth: 120,
        dialogWidth: 700,
        dialogClickModal: false,
        // delBtn: false, //默认是有删除、编辑按钮
        // editBtn: false,
        dialogCustomClass: "directSupply-dialog",
        column: [
          {
            label: "规则描述",
            prop: "title",
            width: 200,
            search: true,
            searchSpan: 8,
            span: 24,
            type: "input",
            showWordLimit: true,
            minlength: 1,
            maxlength: 20,
            rules: [
              {
                required: true,
                whitespace: true,
                message: "请输入规则描述",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "积分值",
            prop: "integral",
            width: 100,
            span: 24,
            type: "number",
            min: -999.0,
            max: 999.0,
            precision: 1,
            rules: [
              {
                required: true,
                message: "请输入积分值",
                trigger: ["blur"],
              },
            ],
          },
          {
            label: '创建人',
            prop: 'createUserName',
            width: 100,
            addDisplay: false, //表单新增时是否可见
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: '规则应用',
            prop: 'isOpen',
            type: 'switch',
            search: true,
            searchSpan: 8,
            width: 100,
            span: 24,
            value: true,
            dicData: [{
              label: '关',
              value: false
            }, {
              label: '开',
              value: true
            }],
            // addDisplay: false, //表单新增时是否可见
            // editDisplay: false,
            // viewDisplay: false,
            rules: [
              {
                required: true,
                message: "请选择规则应用",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: '备注',
            prop: 'remark',
            type: 'textarea',
            width: 200,
            span: 24,
            minRows: 5,
            showWordLimit: true,
            minlength: 0,
            maxlength: 200,
          },
          {
            label: '更新时间',
            prop: 'updateTime',
            addDisplay: false, //表单新增时是否可见
            editDisplay: false,
            viewDisplay: false,
          },
        ],
      },
      data: [],
      dialogVisible: false,
      dialogType: "",
      settingForm: {},
      settingOption1: {
        labelSuffix: "：",
        labelWidth: 180,
        detail: false,
        column: [
          {
            label: "通信合作社人员系数",
            prop: "coefficient",
            type: 'number',
            span: 24,
            minRows: 0.1,
            maxRows: 5,
            precision: 1,
            rules: {
              required: true,
              message: '请输入通信合作社人员系数',
              trigger: ["blur", "change"],
            }
          },
        ],
      },
      settingOption2: {
        labelSuffix: "：",
        labelWidth: 130,
        detail: false,
        column: [
          {
            label: "活动期间系数",
            prop: "coefficient",
            type: 'number',
            span: 24,
            minRows: 0.1,
            maxRows: 5,
            precision: 1,
            rules: {
              required: true,
              message: '请输入活动期间系数',
              trigger: ["blur", "change"],
            }
          },
          {
            label: "活动时间",
            startPlaceholder: '请选择活动开始时间',
            endPlaceholder: '请选择活动结束时间',
            prop: "dateRange",
            type: 'datetimerange',
            rangeSeparator: '至',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            span: 24
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters(["permission", "website", "userInfo"]),
    permissionList () {
      return {
        addBtn: true,
        viewBtn: true,
        editBtn: true,
        delBtn: true,
        publishBtn: true,
        cancelBtn: true
      };
    },
    ids () {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    dialogTitle () {
      if (this.dialogType == '1') return '通信合作社人员系数配置'
      if (this.dialogType == '2') return '活动期间系数配置'
    },
    dialogFormOption () {
      return this[`settingOption${this.dialogType}`]
    }
  },
  created () {
    this.treeLoading = true
    this.initTreeData(this.userInfo.tenant_id)
  },
  methods: {
    //Uview预览 start
    showUview () {
      this.uviewVisible = true;
      this.$refs.uview.sendMessage(this.form.title, this.form.content);
    },
    changeUviewVisible () {
      this.uviewVisible = false;
    },
    //Uview预览 end
    // 搜索过滤
    // filterNodeMethod(value, data) {
    //   if (!value) return true;
    //   return data.value.indexOf(value.trim()) !== -1;
    // },
    // 详情弹窗
    preview (row) {
      this.form = row;
      this.queryDetail();
    },
    // 保存
    rowSave (row, done, loading) {
      const { title, integral, isOpen, remark } = row
      const submitData = {
        title, integral, isOpen, remark,
        deptId: this.deptId
      }
      api.add(submitData).then(res => {
        this.$message.success(`积分规则新增成功`);
        this.onLoad(this.page, this.query);
        done();
      }).catch(err => {
        loading();
      })
    },
    // 修改
    rowUpdate (row, index, done, loading) {
      console.log(row, this.form)
      const { deptId, id, integral, isOpen, remark, title } = row
      const submitData = {
        deptId, id, integral, isOpen, remark, title
      }
      api.update(submitData).then(res => {
        this.$message.success(`积分规则编辑成功`);
        this.onLoad(this.page, this.query);
        done();
      }).catch(err => {
        loading();
      })
    },
    // 删除
    rowDel (row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return api.remove({ id: row.id });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // 重置
    searchReset () {
      this.query = {};
      this.releaseStatus = "";
      this.query.deptId = this.deptId
      this.onLoad(this.page);
    },
    // 搜索
    searchChange (params, done) {
      this.page.currentPage = 1;
      const deptId = this.query.deptId
      this.query = params;
      this.query.deptId = deptId
      this.onLoad(this.page, params);
      done();
    },
    // 勾选
    selectionChange (list) {
      this.selectionList = list;
    },
    // 重置勾选
    selectionClear () {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    // 多选删除
    handleDelete () {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      // if (this.selectionList.findIndex((item) => item.status === 1) !== -1) {
      //     this.$message.warning("存在已发布的数据,已发布的数据无法删除");
      //     return;
      // }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return api.remove({
            id: this.ids,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    // 查看详情
    queryDetail () {
      detail({
        id: this.form.id,
      }).then((res) => {
        this.form = res.data.data;
        if (this.form.picture.length !== 0) {
          this.bannerUrl = this.form.picture[0].link;
          this.srcList = [this.form.picture[0].link];
        }
        let fileList = [];
        let idList = [];

        this.form.attachVos &&
          Array.isArray(this.form.attachVos) &&
          this.form.attachVos.forEach((ele) => {
            fileList.push({
              label: ele.originalName,
              value: ele.link,
            });
            idList.push(ele.id);
          });
        this.form.fileList = fileList;
        this.form.attachList = idList;
        this.showUview();
      });
    },
    // 打开前回调
    async beforeOpen (done, type) {
      this.bannerUrl = "";
      if (type === "add") {
        if (this.deptId) {
          this.form = {};
          done();
        } else {
          this.$message.warning("请点击左侧选择规则应用范围");
        }
      }
      if (["edit", "view"].includes(type)) {
        api.detail({
          id: this.form.id,
        }).then((res) => {
          this.form = res.data.data;
          done();
        });
      }
    },
    // 当前页切换
    currentChange (currentPage) {
      this.page.currentPage = currentPage;
    },
    // 页面显示条数切换
    sizeChange (pageSize) {
      this.page.pageSize = pageSize;
    },
    // 刷新
    refreshChange () {
      this.onLoad(this.page, this.query);
    },
    // 首次加载
    async onLoad (page, params = {}) {
      this.loading = true;
      let query = Object.assign(params, {
        ...this.query,
        current: this.page.currentPage,
        size: this.page.pageSize,
      });
      let res = await api.getList(query);
      if (res && res.data && res.data.success) {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      }
    },

    filterNodeMethod (value, data) {
      if (!value) return true
      const title = this.userKind === userKindList.villager ? data.deptName : data.deptName
      return title.indexOf(value.trim()) !== -1
    },
    nodeClick (data) {
      this.query.deptId = data.id
      this.deptId = data.id
      this.page.currentPage = 1;
      this.onLoad(this.page, this.query);
    },
    // 数村cv代码，祖传代码看不懂，
    // getDeptTree和getDeptTreeFive接口一模一样
    // this.userKind === userKindList.villager必定为true
    async initTreeData (tenantId) {
      if (this.userKind === userKindList.villager) {
        // this.$set(this.treeOption.props, 'label', 'deptName')
        // this.$set(this.treeOption.props, 'value', 'key')
        this.treeData = (await getDeptTree(tenantId)).data.data
      } else this.treeData = (await getDeptTreeFive(tenantId)).data.data
      if (this.userKind !== userKindList.visitor) this.$emit('treeData', this.treeData)
      this.treeLoading = false
    },

    openDialog (type) {
      this.dialogType = type
      this.getCoefficient(type)
      this.dialogVisible = true
    },
    closeDialog () {
      this.dialogVisible = false;
    },

    // 获取两个系数
    async getCoefficient (type) {
      if (type == '1') {
        const res = await api.getCommunicateCoefficient()
        this.settingForm = {
          coefficient: res.data.data || 1.5 // 默认值1.5
        }
      }
      if (type == '2') {
        const res = await api.getActivityCoefficient()
        const { coefficient, startTime, endTime } = res.data.data
        this.settingForm = {
          coefficient: coefficient || 2.0,
          dateRange: [startTime, endTime]
        }
      }
    },

    // 系数配置提交
    async submitSetting (form, done) {
      console.log(form, this.settingForm)
      const { coefficient, dateRange } = form
      let res, type = this.dialogType
      if (type == '1') {
        res = await api.updateCommunicateCoefficient({ coefficient })
      }
      if (type == '2') {
        const data = { coefficient }
        if (dateRange.length) {
          data.startTime = dateRange[0]
          data.endTime = dateRange[1]
        }
        res = await api.updateActivityCoefficient(data)
      }
      if (res.data.success) {
        this.$message.success('系数配置成功')
        this.closeDialog()
      }
      done()
    },

    changeIsOpen (row, index) {
      api.changeIsOpen({
        id: row.id,
        isOpen: !row.isOpen
      }).then(res => {
        console.log(res.data.success)
        if (res.data.success) {
          let data = [...this.data]
          data[index] = { ...data[index], isOpen: !row.isOpen }
          this.data = data

          // this.onLoad(this.page, this.query);
        }
      })
    }
  },
};
</script>
<style scoped lang='scss'>
::v-deep .dialogwidth {
  width: 700px;
}
</style>
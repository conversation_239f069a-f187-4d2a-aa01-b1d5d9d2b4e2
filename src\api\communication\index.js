/*
 * @Descrip: 通讯合作社接口
 * @Author: chenn26
 * @Date: 2023-01-13
 * @LastEditors: chenn26
 * @LastEditTime: 2023-01-16 15:26:43
 */
import request from '@/router/axios';

/**
 * @description 通讯合作社通过部门id获取数据
 * @param {object} data
 */
export const getListByDept = (current, size, params) => {
  return request({
    url: '/api/communicate/list',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
}

/**
 * @desc 获取地区树
 * @param {number} data 该用户tenantid
 * @returns {Promise<ResponseData>}
 */
export const getTreeList = (data) => {
  return request({
    url: '/api/blade-system/dept/tree',
    method: 'get',
    params: {
      tenantId: data
    }
  })
}

/**
 * @description 通讯合作社新增
 * @param {object} data
 */
export const add = (data) => {
  return request({
    url: '/api/communicate',
    method: 'post',
    data: data
  })
}

/**
 * @description 通讯合作社更新
 * @param {object} data
 */
export const update = (id,data) => {
  return request({
    url: '/api/communicate',
    method: 'put',
    data: data
  })
}

/**
 * @description 通讯合作社批量删除
 * @param {string} ids
 */
export const remove = (ids) => {
  return request({
    url: '/api/communicate',
    method: 'delete',
    params: {
      ids,
    }
  })
}

/**
 * @description 通讯合作社获取详情
 * @param {number} id
 */
export const getDetail = (id) => {
  return request({
    url: '/api/communicate/detail',
    method: 'get',
    params: {
      id
    }
  })
}
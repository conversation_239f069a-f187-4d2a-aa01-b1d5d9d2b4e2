/*
 * @Date: 2025-02-18 10:52:36
 * @LastEditors: linqh21
 * @LastEditTime: 2025-02-20 18:28:58
 * @Description:
 * @FilePath: \src\api\digitalGovernance\stationletter.js
 */
import request from '@/router/axios';


/**
 * @description: 请求列表
 * @param {object} params
 * @author:
 */
export const getList = (params) => {
  return request({
    url: '/api/user/stationLetter/myPage',
    method: 'get',
    params,
  })
}

/**
 * @description: 查看详情
 * @param {object} params
 * @author:
 */
export const detail = (params) => {
  return request({
    url: '/api/user/stationLetter/myDetail',
    method: 'get',
    params,
  })
}


/**
 * @description: 隐藏
 * @param {object} params
 * @author:
 */
export const remove = (params) => {
  return request({
    url: '/api/user/stationLetter/doHide',
    method: 'post',
    params,
  })
}



/**
 * @description: 删除
 * @param {object} params
 * @author:
 */
export const isRead = (params) => {
  return request({
    url: '/api/user/stationLetter/doRead',
    method: 'post',
    params,
  })
}


/**
 * @description: 查看详情
 * @param {object} params
 * @author:
 */
export const unReadList = (params) => {
  return request({
    url: '/api/user/stationLetter/unReadList',
    method: 'get',
    params,
  })
}

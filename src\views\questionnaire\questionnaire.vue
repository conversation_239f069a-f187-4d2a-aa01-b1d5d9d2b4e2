<!--
 * @Description: 投票管理-问卷调查
 * @Author: zhouwj83
 * @Date: 2021-06-09 17:14:05
 * @LastEditors: chenz76
 * @LastEditTime: 2022-03-16 11:43:58
-->
<template>
    <basic-container>
        <el-tabs v-model="activeName">
            <el-tab-pane label="所有问卷" name="0"></el-tab-pane>
            <el-tab-pane :label="draftsLabel" name="1"></el-tab-pane>
            <el-tab-pane label="模版管理" name="2"></el-tab-pane>
        </el-tabs>
        <Table :activeName="activeName" @changeTab="changeTab"></Table>
    </basic-container>
</template>
<script >
import Table from './components/questionnaire-table.vue'
import { getDraftCount } from '@/api/questionnaire/survey'
export default {
    components: {
        Table
    },
    provide() {
        return {
            getDraftNum: this.getDraftNum
        }
    },
    data() {
        return {
            activeName: '0', // 0 所有问卷 1 草稿箱
            draftsLabel: '草稿箱(0)',
        }
    },
    mounted() {
        this.getDraftNum()
    },
    methods: {
        getDraftNum() {
            console.log('caogao')
            getDraftCount().then(res => {
                this.draftsLabel = '草稿箱(' + res.data.data + ')'
            })
        },
        changeTab(e){
            this.activeName=e
        }
    }
}
</script>

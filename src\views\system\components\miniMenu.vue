<template>
  <basic-container>
    <avue-crud :option="option" :defaults.sync="defaults" :table-loading="loading" :data="data" :search.sync="query" ref="crud" v-model="form" :permission="permissionList" :before-open="beforeOpen" :before-close="beforeClose" @row-del="rowDel" @row-update="rowUpdate" @row-save="rowSave" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad" @tree-load="treeLoad">
      <template slot="menuLeft">
        <el-button type="primary" size="small" icon="el-icon-plus" v-if="permission.menu_add" @click="handleAddLevel">新 增</el-button>
        <el-button type="danger" size="small" icon="el-icon-delete" v-if="permission.menu_delete" @click="handleDelete">删 除</el-button>
      </template>
      <template slot-scope="scope" slot="menu">
        <el-button type="text" icon="el-icon-circle-plus-outline" size="small" @click.stop="handleAdd(scope.row,scope.index)" v-if="userInfo.role_name.includes('admin') && scope.row.category === 1">新增子项</el-button>
      </template>
      <template slot="sourceForm">
        <el-upload class="avatar-uploader" :http-request="uploadFile" :on-remove="handleElUploadRemove" :show-file-list="true" :on-success="handleElUploadSuccess" :before-upload="beforeAvatarUpload" :on-exceed="handleExceed" :file-list="imageFileList" list-type="picture-card" :limit="1" accept="image/jpeg,image/png">
          <i class="el-icon-plus"></i>
        </el-upload>
      </template>
      <template slot-scope="{row}" slot="source">
        <div style="text-align:center">
          <el-image v-if="row.sourceLink" :src="row.sourceLink" :preview-src-list="[row.sourceLink]" style="width: 40px; height: 40px; cursor: pointer;" fit="cover" />
          <i v-else :class="row.source" />
        </div>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getLazyList, remove, update, add, getMenu, getMenuTree } from "@/api/system/menu";
import { mapGetters } from "vuex";
import iconList from "@/config/iconList";
import request from '@/router/axios';
export default {
  data() {
    return {
      form: {},
      query: {},
      defaults: {},
      loading: true,
      selectionList: [],
      parentId: 0,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        height: 'auto',
        calcHeight: 30,
        lazy: true,
        tip: false,
        simplePage: true,
        searchShow: true,
        searchMenuSpan: 6,
        dialogWidth: "60%",
        tree: true,
        border: true,
        index: true,
        selection: true,
        viewBtn: true,
        menuWidth: 300,
        dialogClickModal: false,
        addBtn: false,
        labelWidth: 120,
        column: [
          {
            label: "菜单名称",
            prop: "name",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入菜单名称",
                trigger: "blur"
              }
            ]
          },
          {
            label: "平台类型",
            prop: "type",
            type: "select",
            dataType: "string",
            dicUrl: "/api/blade-system/dict/dictionary?code=menu_type",
            dicFlag: true,
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            disabled: true,
            hide: false,
            rules: [
              { required: true, message: "请选择平台类型", trigger: "change" }
            ]
          },
          {
            label: "菜单类型",
            prop: "category",
            type: "radio",
            addDisabled: false,
            editDisabled: true,
            span: 12,
            dicData: [
              {
                label: "菜单",
                value: 1
              },
              {
                label: "按钮",
                value: 2
              }
            ],
            hide: true,
            rules: [
              {
                required: true,
                message: "请选择菜单类型",
                trigger: "blur"
              }
            ],

          },
          {
            label: "路由地址",
            prop: "path",
            rules: [
              {
                required: true,
                message: "请输入路由地址",
                trigger: "blur"
              }
            ]
          },
          {
            label: "上级菜单",
            prop: "parentId",
            type: "tree",
            dicData: [],
            hide: true,
            // addDisabled: false,
            props: {
              label: "title"
            },
            rules: [
              {
                required: false,
                message: "请选择上级菜单",
                trigger: "click"
              }
            ],
            display: false,
          },
          {
            label: "关联菜单",
            prop: "associatedMenuId",
            type: "tree",
            dicData: [],
            hide: true,
            multiple: true,
            props: {
              label: "title",
              value: "id",
              children: "children",
              disabled: (data) => {
                // console.log(data,this.form.associatedMenuId);
                if (data.associatedMenuIds && data.associatedMenuIds.length > 0) return true
                return false
              },
            },
            showCheckbox: true,
            filterable: true,
            display: false,
          },

          {
            label: "菜单别名",
            prop: "alias",
            search: true,
            rules: [
              {
                required: true,
                message: "请输入菜单别名",
                trigger: "blur"
              }
            ]
          },
          {
            label: "菜单编号",
            prop: "code",
            search: true,
            span: 12,
            rules: [
              {
                required: true,
                message: "请输入菜单编号",
                trigger: "blur"
              }
            ]
          },
          {
            label: "菜单排序",
            prop: "sort",
            type: "number",
            span: 12,
            rules: [
              {
                required: true,
                message: "请输入菜单排序",
                trigger: "blur"
              }
            ]
          },
          {
            label: "是否镇村联动",
            prop: "isLinkage",
            type: "select",
            span: 12,
            value: false,
            display:true,
            dicData: [
              {
                label: "是",
                value: true
              },
              {
                label: "否",
                value: false
              }
            ],
            hide: true,
          },
          {
            label: "菜单图标",
            prop: "source",
            formslot: true,
            display: false,
            span: 24,
            rules: [
              {
                required: true,
                message: '请上传图标',
                trigger: 'change'
              }
            ]
          },
          {
            label: "菜单备注",
            prop: "remark",
            type: "textarea",
            span: 24,
            minRows: 2,
            hide: true
          },
        ]
      },
      data: [],
      imageFileList: [],
      iconList: iconList,
      maps: new Map()
    };
  },
  watch: {
    'form.category'() {
      console.log(this.form.category, "变化");
      const category = Number(this.form.category);
      this.$refs.crud.option.column.filter(item => {
        if (item.prop === "path") {
          item.rules[0].required = category === 1;
        }
        if (item.prop === "isLinkage") {
          item.display = category === 1;
        }
        // if (item.prop === "parentId") {

        // }
        if (item.prop === "source") {
          item.display = category === 1;
        }
        if (item.prop === "associatedMenuId") {
          item.display = category === 1;
        }
        if (item.prop === "parentId") {
          item.display = category === 2;
           item.rules[0].required = category === 2;
        }
      });
    },
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.menu_add, false),
        viewBtn: this.vaildData(this.permission.menu_view, false),
        delBtn: this.vaildData(this.permission.menu_delete, false),
        editBtn: this.vaildData(this.permission.menu_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    vaildData(val, def) {
      return typeof val !== "undefined" && val !== null ? val : def;
    },
    findObject(list, prop) {
      return list.find(item => item.prop === prop);
    },
    initData() {
      getMenuTree(2).then(res => {
        const column = this.findObject(this.option.column, "parentId");
        column.dicData = res.data.data;
      });
      getMenuTree(1).then(res => {
        const column = this.findObject(this.option.column, "associatedMenuId");
        column.dicData = res.data.data;
      });
    },
    handleAddLevel() {
      this.$refs.crud.rowAdd();
    },
    handleAdd(row) {
      console.log(row, 'row');
      this.parentId = row.id;
      this.form.category = 2;
      this.form.parentId = row.id;
      const column = this.findObject(this.option.column, "parentId");
      const column1 = this.findObject(this.option.column, "category");
      column.addDisabled = true;
      column1.addDisabled = true;
      this.$refs.crud.rowAdd();

    },
    rowSave(row, done, loading) {
      // row.type = 2;
      // row.associatedMenuId = row.associatedMenuId.join(',');
      // if (row.category == 1) {
      //   row.parentId = "0"
      // }
      let param
      if(row.category == 1){
        param = {
          alias: row.alias,
          associatedMenuId: row.associatedMenuId.join(','),
          category: row.category,
          code: row.code,
          name: row.name,
          isLinkage: row.isLinkage,
          path: row.path,
          remark: row.remark,
          sort: row.sort,
          source: row.source,
          type: 2,
          parentId:"0",
        }
      }else{
        param = {
          alias: row.alias,
          category: row.category,
          code: row.code,
          name: row.name,
          path: row.path,
          remark: row.remark,
          sort: row.sort,
          parentId: row.parentId,
          type: 2,
        }
      }
      add(param).then((res) => {
        const data = res.data.data;
        row.id = data.id;
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        let treeData = this.maps.get(row.parentId)
        // console.log(treeData,'treeData')
        if (treeData) this.treeLoad(treeData.tree, treeData.treeNode, treeData.resolve)
        else{
          this.parentId = "0"
          this.onLoad(this.page);
        }
        done(row);
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowUpdate(row, index, done, loading) {
      // row.type = 2;
      // if (row.category == 1) {
      //   row.parentId = "0"
      // }
      let param
      if(row.category == 1){
        param = {
          id: row.id,
          alias: row.alias,
          associatedMenuId: row.associatedMenuId,
          category: row.category,
          code: row.code,
          isLinkage: row.isLinkage,
          name: row.name,
          path: row.path,
          remark: row.remark,
          sort: row.sort,
          source: row.source,
          type: 2,
          parentId:"0",
        }
      }else{
        param = {
          id: row.id,
          alias: row.alias,
          category: row.category,
          code: row.code,
          name: row.name,
          path: row.path,
          remark: row.remark,
          sort: row.sort,
          parentId: row.parentId,
          type: 2,
        }
      }
      update(param).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        let treeData = this.maps.get(row.parentId)
        // console.log(treeData,'treeData')
        if (treeData) this.treeLoad(treeData.tree, treeData.treeNode, treeData.resolve)
        else{
          this.parentId = "0"
          this.onLoad(this.page);
        }
        done(row);
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowDel(row, index, done) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id, 2);
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          done(row);
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids, 2);
        })
        .then(() => {
          this.data = [];
          this.parentId = 0;
          this.$refs.crud.refreshTable();
          this.$refs.crud.toggleSelection();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    searchReset() {
      this.query = {};
      this.parentId = 0;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.parentId = '';
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    beforeOpen(done, type) {
      if (["add", "edit"].includes(type)) {
        this.imageFileList = [];
        this.initData();
        this.form.type = 2;
      }
      if (["edit", "view"].includes(type)) {
        getMenu(this.form.id, 2).then(res => {
          let data = res.data.data
          if (data.sourceLink) {
            this.imageFileList = [{
              url: data.sourceLink,
              status: 'done',
            }];
          }
          this.form = data;
        });
      }
      done();
    },
    beforeClose(done) {
      this.parentId = "";
      const column = this.findObject(this.option.column, "parentId");
      const column1 = this.findObject(this.option.column, "category");
      column1.addDisabled = false;
      column.value = "";
      column.addDisabled = false;
      console.log(this.defaults, this.option.column);
      done();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
      this.$refs.crud.refreshTable();
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getLazyList(this.parentId, Object.assign({}, params, this.query, { type: 2 })).then(res => {
        this.data = res.data.data;
        this.loading = false;
        this.selectionClear();
      });
    },
    treeLoad(tree, treeNode, resolve) {
      const parentId = tree.id;
      getLazyList(parentId, { type: 2 }).then(res => {
        resolve(res.data.data);
      });
    },
    customUpload(options) {
      const formData = new FormData();
      formData.append('file', options.file);
      this.$http.post('/api/blade-resource/oss/endpoint/put-file-attach', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then(res => {
        if (res.data.success) {
          options.onSuccess(res.data);
        } else {
          options.onError(new Error(res.data.msg || '上传失败'));
        }
      }).catch(err => {
        options.onError(err);
      });
    },
    async uploadFile(param) {
      // param.file就是上传文件本身
      // console.log(param, "param");
      const formData = new FormData()
      formData.append('file', param.file)
      //  console.log(this.imageFileList);
      this.imageFileList = [{
        url: "",
        name: param.file.name,
        percentage: 0,
        status: "uploading",
        size: param.file.size,
        type: param.file.type,
        uid: param.file.uid,
      }];
      // 发起请求
      request({
        method: 'post',
        // 上传地址，因为我这里的request请求是自己封装过的，所以就只需要填写接口后面的地址即可
        url: '/api/blade-resource/oss/endpoint/put-file-attach',
        data: formData,
        // 重点一：complete就是处理后的上传进度数值1-100
        onUploadProgress: progressEvent => {
          // console.log(progressEvent)
          const complete = parseInt(
            ((progressEvent.loaded / progressEvent.total) * 100) | 0,
            10
          )
          // 重点二：onProgress()方法需要以上方接收的形参来调用
          // 这个方法有一个参数"percent"，给他进度值 complete 即可
          param.onProgress({ percent: complete })
        },
        // onSuccess: function (response) {
        // // 成功响应时触发，处理on-success事件
        // console.log('上传成功：', response);
        // },
      }).then(res => {
        // console.log(res, "res");
        if (res.data.success) {
          this.$message.success("上传成功");
          this.form.source = res.data.data.attachId;
          this.$refs.crud.validateField("source");
          this.imageFileList[0].status="done"
          this.imageFileList[0].url = res.data.data.link;
          // console.log(this.imageFileList);
          // this.imageFileList = [file];
          // param.onSuccess(res.data);
          // this.imageFileList=[]
        } else {
          // param.onError(new Error(res.data.msg || '上传失败'));
        }
      }).catch(err => {
        param.onError(new Error(err.data.msg || '上传失败'));
      });
    },
    // handleElUploadSuccess(res, file) {
    //   console.log(res,file,"res file");
    //   if (res && res.data && res.data.attachId) {
    //     this.form.source = res.data.attachId;
    //     this.$refs.crud.validateField("source");
    //     this.imageFileList = [{
    //       url: (res.data.link || file.url),
    //       status: 'done',
    //     }];
    //   } else {
    //     this.form.source = ''
    //     this.imageFileList = [];
    //   }
    // },
    handleElUploadRemove() {
      this.form.source = ''
      this.imageFileList = [];
    },
    beforeAvatarUpload(file) {
      // console.log(file)
      const isJPGorPNG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt1M = file.size / 1024 / 1024 < 1;
      if (!isJPGorPNG) {
        this.$message.error('只能上传JPEG/PNG格式的图片!');
        return false;
      }
      if (!isLt1M) {
        this.$message.error('上传图片大小不能超过1M!');
        return false;
      }
      return true;
      // return isJPGorPNG && isLt1M;
    },
    handleExceed() {
      this.$message.warning('最多只能上传1张图片');
    },
  }
};
</script>

<style>
</style>

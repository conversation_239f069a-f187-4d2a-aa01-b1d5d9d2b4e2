<!-- 
/**
  * @Author: 林中奇
  * @Date: 2025/07/07
  * @lastAuthor:
  * @lastChangeDate:
  * @Explain: 应用中心
  */
 
-->
<template>
  <basic-container>
    <div style="display: flex; line-height: 32px;">
      <span style="font-size: 14px; color: #606266;">应用名称：</span>
      <el-input v-model="name" size="small" placeholder="请输入 应用名称" style="width: 15%;"></el-input>
      <el-button type="primary" size="small" icon="el-icon-search" style="margin-left: 48px;"
        @click="getAppList()">搜索</el-button>
      <el-button size="small" icon="el-icon-delete" style="margin-left: 14px;" @click="searchReset()">清空</el-button>
    </div>
    <el-row style="margin: 18px 8px;" :gutter="20">
      <el-col :span="3" v-for="(item, index) in appList" :key="index">
        <div class="app-item" @click="handleClick(item)">
          <div class="card">
            <el-image :src="item.iconUrl" style="width: 100%; height: 120px;"></el-image>
            <div v-if="item.isCollect" class="right-top-radius-subscript type4" @click.stop="handleStarClick(false, item.id)" title="取消常用">
              <i class="el-icon-star-on"></i>
            </div>
            <div v-else class="right-top-radius-subscript type4" @click.stop="handleStarClick(true, item.id)" title="设为常用">
              <i class="el-icon-star-off"></i>
            </div>
          </div>
          <div class="title" :title="item.name">{{ item.name }}</div>
        </div>
      </el-col>
    </el-row>

  </basic-container>
</template>

<script>
import { getAppCenterList, isCollect } from "@/api/appCenter/";

export default {
  components: {

  },
  data () {
    return {
      name: '', // 应用名称
      appList: []
    }
  },
  created () {
    this.getAppList()
  },
  methods: {
    getAppList () {
      getAppCenterList(1, 999, this.name).then(res => {
        this.appList = res.data.data;
      });
    },
    searchReset () {
      this.name = ''
      this.getAppList()
    },
    handleClick (item) {
      if (!item.jumpAddress) {
        return this.$message({
          type: "error",
          message: "该应用无链接地址，无法跳转！!"
        });
      }
      window.open(item.jumpAddress, '_blank');
    },
    handleStarClick(type, id){
      if(type){
        this.$confirm('是否确定设为常用应用?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            isCollect(type, id).then(() => {
              this.$message.success('设置成功');
              this.getAppList()
            })
          })
      }else{
        this.$confirm('是否确定取消常用应用?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            isCollect(type, id).then(() => {
              this.$message.success('设置成功');
              this.getAppList()
            })
          })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-item {
  cursor: pointer;

  .card {
    position: relative;
    background: #fff;
    overflow: visible;
  }

  .right-top-radius-subscript {
    $corner: 10px;
    width: 32px;
    height: 24px;
    color: white;
    text-align: center;
    background: linear-gradient(135deg, #ffffff66, transparent) #ea3447;
    box-shadow: 0 2px 10px 0 #00000022;
    border-radius: 5px 5px 5px 0;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: -10px;
    top: 10px;

    &::after {
      content: "";
      position: absolute;
      clip-path: polygon(0 0, 100% 0, 100% 100%);
      width: $corner;
      height: $corner;
      bottom: -$corner;
      left: 0;
      background-color: inherit;
    }

    &.type4 {
      filter: hue-rotate(264deg);
    }
  }

  .right-top-radius-subscript {
    left: revert;
    right: -10px;
    border-radius: 5px 5px 0 5px;

    &::after {
      left: revert;
      right: 0;
      clip-path: polygon(0 0, 0 100%, 100% 0);
    }
  }

  .title {
    font-size: 14px;
    text-align: center;
    word-break: break-all;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
}
</style>

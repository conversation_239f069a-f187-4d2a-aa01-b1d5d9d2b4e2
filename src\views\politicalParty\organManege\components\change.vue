<template>
  <div class='' style="margin: 24px 25%;">
    <avue-form :option="option" v-model="form" ref="form" @submit="submit">
    </avue-form>
  </div>
</template>

<script>
export default {
  name: '',
  components: {},
  props: {
    dept: {
      default: '',
      type: String
    }
  },
  data () {
    return {
      form: {},
      option: {
        ubmitText: '保存',
        emptyBtn: false,
        labelWidth: 120,
        column: [
          {
            label: '换届时间',
            prop: 'birthDate',
            type: 'date',
            rules: [{ required: true, message: '请选择换届时间', trigger: 'change' }],
            span: 24,
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          }, {
            label: '换届方式',
            prop: 'householderName',
            type: 'input',
            maxlength: 500,
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入换届方式",
                trigger: "blur",
              }
            ],
          }, {
            label: '换届结果',
            prop: 'householderName',
            type: 'input',
            maxlength: 500,
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入换届结果",
                trigger: "blur",
              }
            ],
          }, {
            label: '任期',
            prop: 'householderName',
            type: 'input',
            maxlength: 50,
            span: 24,
            rules: [
              {
                required: true,
                message: "请输入任期",
                trigger: "blur",
              }
            ],
          }, 
        ],
      },
    };
  },
  // 监听属性 类似于data概念
  computed: {},
  watch: {},
  created () { },
  mounted () { },
  // 方法集合
  methods: {
    submit () {
      this.$refs.form.validate((valid, done) => {
        if (valid) {
          var obj = Object.assign({}, this.form);
          obj.id = this.id;
          delete obj.$status;
          api
            .submitStock(obj)
            .then((res) => {
              if (res.data.success) {
                this.$message({
                  type: "success",
                  message: res.data.msg,
                });
              } else {
                this.$message({
                  type: "warning",
                  message: res.data.msg,
                });
              }
              done();
              this.emptyForm();
              this.initData();
            })
            .catch(() => {
              done();
            });
        }
      });
    },
  }
}
</script>
<style lang='scss' scoped></style>
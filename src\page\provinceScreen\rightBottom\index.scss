.province-right-bottom-content {
  position: absolute;
  width: 100%;
  height: 100%;
  .header {
    position: absolute;
    width: 100%;
    white-space: nowrap;
    top: 2vh;
    left: 50%;
    transform: translate(-50%, 0);
    .title-nation {
      position: absolute;
      letter-spacing: 3px;
      color: #fff;
      font-size: 1.7vh;
      cursor: pointer;
      left: 0;
    }
    .title-pilot {
      position: absolute;
      letter-spacing: 3px;
      color: #fff;
      font-size: 1.7vh;
      cursor: pointer;
      left: 150px;
    }
    .title-nation:hover {
      color: #5bffdc;
    }
    .title-pilot:hover {
      color: #5bffdc;
    }
    .title-direction {
      position: absolute;
      width: 35px;
      top: 0.8vh;
      right: 0;
      height: auto;
    }
    .left-on-line {
      position: absolute;
      width: 100%;
      top: 3.6vh;
    }
  }
  .content {
    position: absolute;
    float: left;
    margin-top: 7vh;
    width: 100%;
    color: #fff;
    .select-1 {
      display: inline-block;
      width: 33%;
      height: 3vh;
      font-size: 2vh;
      text-align: center;
      cursor: pointer;
    }

    .select-2 {
      width: 34%;
      font-size: 2vh;
      height: 3vh;
      cursor: pointer;
      display: inline-block;
      text-align: center;
    }
    .select-3 {
      width: 33%;
      font-size: 2vh;
      height: 3vh;
      cursor: pointer;
      display: inline-block;
      text-align: center;
    }

    .title-select-back {
      position: absolute;
      width: 33%;
      height: 3vh;
    }

    .content-value {
      margin-left: 5%;
      margin-top: 1vh;
      height: 22vh;
      .header-item {
        margin-top: -50px;
        text-align: center;
        font-size: 16px;
        height: 35px;
        color: #d19e04;
      }
      .rows {
        margin-top: 33px;
        .row-item {
          .ceil {
            text-align: center;
          }
        }
      }
    }
  }
}

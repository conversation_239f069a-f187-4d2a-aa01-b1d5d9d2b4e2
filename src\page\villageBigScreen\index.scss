/***********common**************/
.font-size-styleA {
  font-size: 2.5vh;
  color: #b6d7ff;
}

.font-size-styleB {
  font-size: 1.8vh;
  color: #b6d7ff;
}

.font-size-styleC {
  font-size: 2.5vh;
}

.pointer-events-auto {
  pointer-events: auto;
}

.text-style-A {
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/***********common**************/

.screen1 {
  position: fixed;
  min-width: 1200px;
  width: 100%;
  height: 100vh;
  background-color: #05072c !important;
  display: flex;
  flex-direction: column;

  //头
  .title-header {
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    width: 100%;
    height: 12vh;
    display: flex;
    flex-direction: row;

    .header_left {
      position: relative;
      flex: 0 0 28%;
      height: 100%;
      margin-left: 0;
      margin-top: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .header_center {
      position: relative;
      flex: 0 0 44%;
      height: 100%;
      margin-top: 0;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 0 4vw;
      box-sizing: border-box;
    }

    .header_right {
      position: relative;
      flex: 0 0 28%;
      height: 100%;
      margin-top: 0;
      z-index: -1;
    }

    .header_image {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      // height: 12vh;
      // margin-top: -0.5vh;
    }
  }

  .title-back-desc {
    position: absolute;
    width: 40%;
    z-index: 500;
    left: 50.5%;
    transform: translate(-50.5%, 0);
    text-align: center;
    margin-top: 1.8vh;
    font-size: 4vh;
    color: #fff;
    height: 4.5vh;
    font-weight: bold;
    letter-spacing: 20px;
  }

  //左边时间
  .screen-time {
    position: absolute;
    width: 23%;
    left: 1%;
    margin-top: 3vh;
    .screen-time-back {
      position: absolute;
      width: 100%;
      height: 6vh;
    }

    .screen-time-desc {
      position: absolute;
      width: 60%;
      text-align: center;
      font-size: 1.8vh;
      margin-top: 1.5vh;
      font-weight: bold;
      margin-left: 2%;
      color: #1da9d7;
      height: 5vh;
      letter-spacing: 2px;
    }
  }
  //右边地点
  .screen-local {
    position: absolute;
    width: 23%;
    right: 1%;
    margin-top: 3vh;
    .screen-local-back {
      position: absolute;
      width: 100%;
      height: 6vh;
      transform: rotateY(180deg);
    }
    .screen-local-desc {
      position: absolute;
      width: 60%;
      text-align: center;
      margin-top: 1.5vh;
      margin-left: 40%;
      height: 5vh;
      .screen-local-img {
        width: 10%;
        margin-top: -0.3vh;
      }
      .screen-local-title {
        font-weight: bold;
        font-size: 2vh;

        letter-spacing: 2px;
        color: #1da9d7;
        vertical-align: top;
      }
    }
  }

  .screen-left {
    position: absolute;
    left: 1%;
    width: 25%;
    top: 11vh;

    .screen-left-all {
      position: absolute;
      width: 100%;
      height: 85vh;

      .left-on-back {
        position: absolute;
        left: 0%;
        width: 100%;
        height: 86vh;
      }
      //左上
      .left-on {
        position: absolute;
        width: 86%;
        left: 7%;
        height: 32vh;
      }
      //左中
      .left-center {
        position: absolute;
        top: 32vh;
        width: 86%;
        left: 7%;
        height: 24vh;
      }
      //左下
      .left-bottom {
        position: absolute;
        top: 56vh;
        width: 86%;
        left: 7%;
        height: 27vh;
      }
    }
  }

  .screen-center {
    position: absolute;
    left: 50%;
    width: 45%;
    transform: translate(-50%, 0);
    top: 11vh;

    .center-back-img {
      position: absolute;
      left: 0%;
      width: 100%;
      height: 86vh;
    }
    //中上
    .center-on {
      position: absolute;
      width: 88%;
      left: 6%;
    }
    //中中
    .center-center {
      position: absolute;
      width: 88%;
      left: 6%;
      // background:red;
    }
    //中中2
    .center-center2{
      position: absolute;
      width: 88%;
      left: 6%;
    }
    //中下
    .center-bottom {
      position: absolute;
      width: 88%;
      left: 6%;
    }
  }

  .screen-right {
    position: absolute;
    right: 1%;
    width: 25%;
    top: 11vh;
    height: 86vh;

    .right-img {
      position: absolute;
      left: 0%;
      width: 100%;
      height: 86vh;
    }

    //右上
    .right-on {
      position: absolute;
      left: 7%;
      width: 86%;
      height: 25vh;
    }
    //右中
    .right-center {
      position: absolute;
      top: 26vh;
      left: 7%;
      width: 86%;
      height: 34vh;
    }
    //右下
    .right-bottom {
      position: absolute;
      top: 58vh;
      left: 7%;
      width: 86%;
      height: 25vh;
    }
  }
}

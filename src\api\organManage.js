/*
 * @Author: linzq33
 * @Date: 2025-08-05
 * @LastEditors: 
 * @LastEditTime: 
 * @Description: 党组织管理
 */
import request from '@/router/axios';

/**
 * @description 获取列表
 * @param {number} current 
 * @param {number} size 
 * @param {object} params 
 */
// 党组织列表
export const getOrganList = (current, size, params) => {
  return request({
    url: '/api/blade-partyorganization/partyOrganization/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

/**
 * @description 获取详情
 * @param {number} id 
 * @param {boolean} isHide  
 */
export const getOrganDetail = (id) => {
  return request({
    url: '/api/blade-partyorganization/partyOrganization/detail',
    method: 'get',
    params: {
      id,
    }
  })
}

/**
 * @description 移除
 * @param {string} ids
 */
export const organRemove = (ids) => {
  return request({
    url: '/api/blade-partyorganization/partyOrganization/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}
/**
 * @description 添加/修改户籍管理
 * @param {object} row
 */
export const orgaAddOrUpDate = (row) => {
  return request({
    url: '/api/blade-partyorganization/partyOrganization/submit',
    method: 'post',
    data: row
  })
}



/**
 * @description 更新户籍管理
 * @param {object} row
 */
export const update = (row) => {
  return request({
    url: '/api/census/submit',
    method: 'post',
    data: row
  })
}

/**
 * @description 获取成员详情
 * @param {string} id
 * @param {boolean} isView
 */
export const getVillagerDetail = (id, isView) => {
  return request({
    url: '/api/villager/detail',
    method: 'get',
    params: {
      id,
      isView
    }
  })
}

/**
 * @description 获取成员管理列表
 * @param {number} current 
 * @param {number} size 
 * @param {object} params 
 */
export const getMemberList = (id) => {
  return request({
    url: '/api/census/all-families',
    method: 'get',
    params: {
      id
    }
  })
}

/**
 * @description 删除村民
 * @param {string} ids
 */
export const removeVillager = (ids) => {
  return request({
    url: '/api/villager/remove',
    method: 'delete',
    params: {
      ids,
    }
  })
}
/**
 * @description 添加村民
 * @param {object} row
 */
export const addVillager = (row) => {
  return request({
    url: '/api/villager/submit',
    method: 'post',
    data: row
  })
}
/**
 * @description 更新村民
 * @param {object} row
 */
export const updateVillager = (row) => {
  return request({
    url: '/api/villager/submit',
    method: 'post',
    data: row
  })
}
// 成员列表
export const getVillagerList = (current, size, params) => {
  return request({
    url: '/api/villager/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

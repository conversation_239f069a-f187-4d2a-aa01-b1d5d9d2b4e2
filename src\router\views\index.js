/*
 * @Date: 2025-06-24 09:38:27
 * @LastEditors: linqh21
 * @LastEditTime: 2025-08-05 09:48:58
 * @Description:
 * @FilePath: \src\router\views\index.js
 */
import Layout from '@/page/index/'

export default [{
  path: '/wel',
  component: Layout,
  redirect: '/wel/index',
  children: [{
    path: 'index',
    name: '首页',
    meta: {
      i18n: 'dashboard'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/wel/index')
  }, {
    path: 'dashboard',
    name: '控制台',
    meta: {
      i18n: 'dashboard',
      menu: false,
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/wel/dashboard')
  }]
},{
  path: '/info',
  component: Layout,
  redirect: '/info/index',
  children: [{
    path: 'index',
    name: '个人信息',
    meta: {
      i18n: 'info'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/system/userinfo')
  }]
},{
  path: '/todo',
  component: Layout,
  redirect: '/todo/index',
  children: [{
    path: 'index',
    name: '我的待办',
    meta: {
      i18n: 'todo'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/wel/todoList')
  }]
},{
  path: '/mynotice',
  component: Layout,
  redirect: '/mynotice/index',
  children: [{
    path: 'index',
    name: '消息公告',
    meta: {
      i18n: 'mynotice'
    },
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/wel/noticeList')
  }]
},{
  path: '/infoRelease',
  component: Layout,
  children: [{
    path: 'column/:id',
    name: '信息栏目',
    component: () =>
      import( /* webpackChunkName: "views" */ '@/views/infoRelease/column')
  }]
}]

<!--
 * @Description: 左下-荣誉展示
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2022-01-06 10:34:04
-->
<template>
  <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column; padding: 1vh 1vw; padding-right: 0.5vw; box-sizing: border-box;">
    <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column;">
      <img class="img-common" src="/img/bigScreen/modulebg.png" mode="scaleToFill" />

      <div style="display: flex; flex-direction: row; padding-right: 1vw;">
        <div class="head-idx" style="position: relative; width: 3vh; height: 3vh; font-weight: bold;">
          <img class="img-common" src="/img/bigScreen/idx.png" mode="scaleToFill" />
          03
        </div>
        <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center;">
          <img class="img-common" src="/img/bigScreen/subTitlebg.png" mode="scaleToFill" />
          <div class="head-title" style="margin-left: 1vw;">镇视频</div>
        </div>
      </div>
      <div style="padding: 2vh 1.15vw; padding-bottom: 1vh; flex: 1; display: flex; justify-content: center; align-items: center; flex-shrink: 0; flex-grow: 0;">
        <video
          class="video-player"
          controls
          autoplay
          muted
          loop
          preload="auto">
          <source src="/video/town-video.mp4" type="video/mp4">
          您的浏览器不支持视频播放。
        </video>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: { honorList: Array },
};
</script>

<style lang="scss" scoped>

.img-common {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.head-idx {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 1.7vh;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
}

.head-title {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 1.5vh;
  font-style: normal;
  font-weight: Bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
}

.video-player {
  width: 100%;
  // height: 13.3vh;
  object-fit: cover; // 保持视频比例并填充容器
  border-radius: 4px; // 可选：圆角
}
</style>

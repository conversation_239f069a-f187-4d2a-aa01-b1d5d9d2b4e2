import request from '@/router/axios';

export const homeStatistics = (params) => {
  return request({
    url: '/api/admin/home/<USER>',
    method: 'get',
    params,
  })
}

export const commonApplications = (params) => {
  return request({
    url: '/api/admin/home/<USER>',
    method: 'get',
    params,
  })
}
export const homeList = (params) => {
  return request({
    url: '/api/admin/home/<USER>/homeList',
    method: 'get',
    params,
  })
}
export const taskList = (params) => {
  return request({
    url: '/api/admin/home/<USER>/taskList',
    method: 'get',
    params,
  })
}

export const alarmTaskList = (params) => {
  return request({
    url: '/api/admin/home/<USER>/taskList',
    method: 'get',
    params,
  })
}
export const allApplications = (params) => {
  return request({
    url: '/api/admin/home/<USER>',
    method: 'get',
    params,
  })
}
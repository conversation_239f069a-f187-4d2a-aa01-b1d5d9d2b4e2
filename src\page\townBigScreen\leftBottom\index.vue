<!--
 * @Description: 左下-荣誉展示
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenn26
 * @LastEditTime: 2023-05-19 14:34:22
-->
<template>
  <div class="town-left-bottom-content">
    <div class="header">
      <img
        src="/img/screen/title-decorate-left.png"
        class="left-decorate"
        alt=""
      />
      <span class="title">荣誉展示</span>
      <img
        src="/img/screen/title-decorate-left.png"
        class="right-decorate"
        alt=""
      />
    </div>
    <div class="content">
      <div class="content-carousel block">
        <el-carousel height="25vh" interval="4000" indicator-position="none">
          <el-carousel-item v-for="item in honorList" :key="item">
            <img :src="item.link" class="content-img" alt="" />
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>
  </div>
</template>
<script>
import "./index.scss";
export default {
  props: { honorList: Array },
};
</script>
<!--
 * @Author: zhouwj83
 * @Date: 2021-06-22 10:26:35
 * @LastEditors: chenz76
 * @LastEditTime: 2022-08-01 17:29:33
 * @Description: 户籍管理
-->

<template>
  <el-row ref="test">
    <el-col :span="5" v-loading="treeLoading">
      <div class="box">
        <el-scrollbar>
          <basic-container>
            <avue-tree :option="treeOption" :data="treeData" @node-click="nodeClick" show-checkbox>
            </avue-tree>
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :span="19">
      <basic-container>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="家庭" name="first" />
          <el-tab-pane label="成员" name="second" />
        </el-tabs>
        <!-- 家庭 -->
        <family v-if="activeName == 'first'" :dept="deptId" />
        <!-- 成员 -->
        <member v-else :dept="deptId" type="list" />
      </basic-container>
    </el-col>
  </el-row>

</template>

<script>
import { getList, getDetail, add, update, remove } from '@/api/governance/census'
import { mapGetters } from 'vuex'
import family from './components/family.vue'
import member from './components/member.vue'
import { getDeptTree } from "@/api/infoRelease/partyLead"

export default {
  components: {
    family, member
  },
  data () {
    return {
      activeName: 'first',
      deptId: '',
      treeLoading: false,
      treeData: [],
      treeOption: {
        addBtn: false,
        menu: false,
        size: 'small',
        props: {
          labelText: '标题',
          label: 'title',
          value: 'value',
          children: 'children'
        }
      },
    }
  },
  computed: {
    ...mapGetters(['permission', 'userInfo']),
  },
  created () {
    this.treeLoading = true
    this.initTreeData(this.userInfo.tenant_id)
  },
  methods: {
    handleClick () {

    },
    nodeClick (data) {
      this.deptId = data.id
    },
    async initTreeData (tenantId) {
      this.treeData = (await getDeptTree(tenantId)).data.data
      this.treeLoading = false
    },
  }
}
</script>

<style lang="scss" scoped></style>

<!--
 * @Author: chenn26
 * @Date: 2023-05-10 16:23:14
 * @LastEditors: chenn26
 * @LastEditTime: 2023-05-23 09:49:32
 * @Description: 右上-神眼大屏
-->
<template>
  <div class="town-right-on-content">
    <div class="header">
      <img src="/img/screen/title-decorate-left.png" class="left-decorate" alt="" />
      <span class="title">神眼监控</span>
      <img src="/img/screen/title-decorate-left.png" class="right-decorate" alt="" />
    </div>
    <div class="content">
      <el-row>
        <el-col>
          <div style="width:100%;height:13vh;position: relative;">
            <el-tooltip class="item" effect="dark" :content="cameraName" placement="top-start">
              <el-select v-model="cameraName" placeholder="请选择设备" size="mini" class="deviceSelect"
                @change="(val) => selectDevice(val, 0)">
                <el-option v-for="(item, index) in cameraList" :key="index" :disabled="item.status === 0"
                  :label="item.cameraName" :value="item.id" :title="item.cameraName"></el-option>
              </el-select>
            </el-tooltip>
            <img src="/img/godEye/open.png" title="全屏" class="open-icon" @click="fullScreen()"
              :style="selectItem === 0 ? 'display: block' : 'display: none'" alt="" />
            <img src="/img/godEye/destroy.png" title="关闭" class="destroy-icon" @click="destroy(selectItem)"
              :style="selectItem === 0 ? 'display: block' : 'display: none'" alt="" />
            <img src="/img/godEye/stop.png" title="暂停" class="stop-icon" @click="stop(selectItem)"
              :style="selectItem === 0 ? 'display: block' : 'display: none'" alt="" />
            <img src="/img/godEye/refresh.png" title="刷新" class="refresh-icon" @click="refresh(selectItem)"
              :style="selectItem === 0 ? 'display: block' : 'display: none'" alt="" />
            <!-- <div class="item-name">{{ cameraName }}</div> -->
            <div id="container" :class="openItem === 0 ? 'open-item' : 'close-item'"
              style="border:1px solid #B4FFFF;"
              @click="changeSelect(0)"></div>
          </div>
        </el-col>
      </el-row>
    </div>
    <div
      :class="openItem === null ? 'right-close-bottom' : 'right-open-bottom'"
    >
      <img
        src="/img/godEye/close.png"
        class="close-button"
        @click="closeFullScreen()"
        title="退出全屏"
        alt=""
      />
    </div>
  </div>
</template>
<script>
import { getPlayUrl, getCameraAdminAll } from "@/api/woEyes/index";
import { debounce } from "lodash";
import './index.scss'
export default {
  props:{
    deptId:{
      type:String,
      default:''
    }
  },
  data() {
    return {
      selectItem: 0,
      openItem: null,
      webPlayer: null,

      isDisabled: true,
      cameraName: "暂无监控",
      cameraList: null,
      loading: true,
      timeTimer: null,
      time: "",
    };
  },
  mounted() {
    this.getCameraAdminAll();
    let self = this;
    this.$nextTick(function () {
      document.addEventListener("keyup", function (e) {
        //此处填写你的业务逻辑即可
        if (e.keyCode == 27) {
          self.closeFullScreen();
        }
      });
    });
  },
  destroyed() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    //获取监控列表
    async getCameraAdminAll() {
      const result = await new Promise((resolve, reject) => {
        var params = {
          deptId:this.deptId
        }
        getCameraAdminAll(params)
          .then((res) => {
            this.loading = false;
            resolve(res);
          })
          .catch((error) => {
            this.loading = false;
            reject(error);
          });
      });
      if (result.data.code === 200) {
        this.cameraList = result.data.data;
        console.log(this.cameraList);
        this.$nextTick(() => {
          this.initVideoList();
        });
      } else {
        this.$message.error("监控播放失败，请稍后再试");
      }
    },
    //初始加载监控列表
    initVideoList() {
      let end = 2;
      let index = 0;
      for (let i = 0; i < end; i++) {
        let data = this.cameraList[i];
        if (data) {
          if (data.status === 1) {
            let params = {
              id: data.id
            };
            this.getPlayUrl(index, data.cameraName, params, false);
            index++;
          } else {
            end++;
          }
        }
      }
    },
    //获取url，并播放
    async getPlayUrl(index, cameraName, params, isClick) {
      const result = await new Promise((resolve, reject) => {
        getPlayUrl(params)
          .then((res) => {
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
      if (result.data.code === 200) {
        this.initVideo(index, cameraName, result.data.data, isClick);
      } else {
        this.$message.error("监控播放失败，请稍后再试");
      }
    },
    initVideo(index, cameraName, url, isClick) {
      let _this = this;
      if (window.WebPlayer.isSupported()) {
        //先销毁再创建
        this.destroy(index);
        console.log(111,index)
        const WebPlayerEvents = window.WebPlayer.WebPlayerEvents;
        this[`webPlayer`] = new window.WebPlayer({
          playerContainer: "#container",
          hasBaseUI: true,
          isSDKMode: false,
          playUrl: url,
        });
        this[`webPlayer`].on(WebPlayerEvents.PLAYER_INIT, function () {
          _this[`cameraName`] = cameraName;
          _this[`isDisabled`] = false;
          if (isClick) {
            _this[`webPlayer`].play();
          } else {
            setTimeout(() => {
              _this[`webPlayer`].play();
            }, 600 * index + 1);
          }
        });
      } else {
        this.$message.error("当前浏览器不支持播放，请更换浏览器");
      }
    },
    changeSelect(item) {
      this.selectItem = item;
    },
    start: debounce(function (data) {
      if (data.status) {
        let params = {
          id: data.id
        };
        this.getPlayUrl(this.selectItem, data.cameraName, params, true);
      } else {
        this.$message.warning("设备未在线，请选择在线设备");
      }
    }, 300),
    //销毁
    destroy() {
      if (!this[`webPlayer`]) return;
      this[`cameraName`] = "暂无监控";
      this[`webPlayer`].destroy();
      this[`webPlayer`] = null;
    },
    //暂停
    stop() {
      if (!this[`webPlayer`]) return;
      this[`webPlayer`].stop();
    },
    //刷新
    refresh: debounce(function () {
      if (!this[`webPlayer`]) return;
      this[`webPlayer`].play();
    }),
    //全屏
    fullScreen() {
      this.openItem = this.selectItem;
    },
    closeFullScreen() {
      this.openItem = null;
    },
    selectDevice: debounce(function (val, index) {
      console.log(val);
      let data = this.cameraList.filter((item) => item.id === val)[0];
      this.selectItem = index;
      if (data.status) {
        let params = {
          id: data.id
        };
        this.getPlayUrl(this.selectItem, data.cameraName, params, true);
      } else {
        this.$message.warning("设备未在线，请选择在线设备");
      }
    }, 300),
  },
};
</script>
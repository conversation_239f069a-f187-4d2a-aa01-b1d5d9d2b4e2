<!--
 * @Author: chenn26
 * @Date: 2023-05-10 16:23:14
 * @LastEditors: chenn26
 * @LastEditTime: 2023-05-25 16:10:14
 * @Description: 右下-村务公开
-->
<template>
  <div class="town-right-bottom-content">
    <div class="right-bottom-header">
      <img src="/img/screen/title-decorate-left.png" class="left-decorate" alt="" />
      <span class="title">政务公开</span>
      <img src="/img/screen/title-decorate-left.png" class="right-decorate" alt="" />
    </div>
    <div class="content">
      <div class="content-value">
        <dv-scroll-board :config="config" ref="scrollBoard" />
      </div>
    </div>
  </div>
</template>
<script>
import "./index.scss";
import { getInformationRelease } from "@/api/screen/townScreen";
export default {
  data() {
    return {
      config: {
        header: [
          "标题",
          "发布日期",
        ],
        rowNum: 5,
        headerBGC: "#031e7a",
        headerHeight: 30,
        columnWidth: [150, 350, 400],
        evenRowBGC: '#00135d', //偶数行
        oddRowBGC: '#00176b', //奇数行
        index: true,
        data: [],
        waitTime:5000,
      },
      timer: null,
      currentPage: 1,
      tmp:[],
      infomationData:[]
    };
  },
  created() {
    this.getInformationRelease();
    this.timer = setInterval(this.getInformationRelease, 1000);
  },
  destroyed() {
    this.stopTimer();
  },
  methods: {
    getInformationRelease() {
      let params = {
        current: this.currentPage,
        size: 6,
        type: "村务公开",
        module: "三务公开",
        status:1
      };
      getInformationRelease(params).then(res => {
        if (res.data.data.records.length === 6) {
          this.currentPage++;
        } else {
          this.currentPage = this.currentPage;
          clearInterval(this.timer);
        }
        this.tmp = this.tmp.concat(res.data.data.records);
        for (let i = 0; i < this.tmp.length; i++) {
          let editData = [];
          editData[0] =
            "<span style='color:#fff' title='" +
            this.tmp[i].title +
            "'>" +
            this.tmp[i].title +
            "</span>";
          editData[1] =
            "<span style='color:#fff' title='" +
            this.tmp[i].createTime +
            "'>" +
            this.tmp[i].createTime +
            "</span>";
            this.infomationData[i] = editData;
        }
        this.config = {
          header: [
            "标题",
            "发布日期",
          ],
          rowNum: 5,
          headerBGC: "#031e7a",
          headerHeight: 30,
          columnWidth: [150, 350, 400],
          evenRowBGC: '#00135d', //偶数行
          oddRowBGC: '#00176b', //奇数行
          index: true,
          hoverPause: true,
          data: this.infomationData,
          waitTime:5000,
        };
      });
    },
    stopTimer() {
      if (this.timer) clearInterval(this.timer);
    }
  },
};
</script>

<style scoped>
::v-deep .dv-scroll-board .row-item {
  height: 40px !important;
  line-height: 40px !important;
}
</style>
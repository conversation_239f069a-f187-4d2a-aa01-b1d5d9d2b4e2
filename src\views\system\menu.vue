<!--
 * @Date: 2025-07-03 15:53:54
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-03 16:24:14
 * @Description:
 * @FilePath: \src\views\system\menu.vue
-->
<template>
  <basic-container>
    <el-tabs v-model="type" @tab-click="handleTabChange" style="margin-bottom: 16px">
      <el-tab-pane label="Web菜单" :name="1"></el-tab-pane>
      <el-tab-pane label="小程序菜单" :name="2"></el-tab-pane>
    </el-tabs>
    <webMenu v-if="type == 1" />
    <miniMenu v-else />
  </basic-container>
</template>

<script>
import webMenu from './components/webMenu.vue'
import miniMenu from './components/miniMenu.vue'
export default {
  components: { webMenu, miniMenu },
  data() {
    return {
      type: 1
    }
  },
  methods: {
    handleTabChange(tab) {
      this.type = tab.name
    }
  }
}
</script>

<style>
</style>

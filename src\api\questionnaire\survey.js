/*
 * @Author: zhouwj83
 * @Date: 2021-07-07 15:58:28
 * @LastEditors: zhouwj83
 * @LastEditTime: 2021-07-07 16:22:31
 * @Description: 
 */
import request from '@/router/axios';
// 获取问卷列表
export const getList = (current, size, params) => {
    return request({
      url: '/api/admin/survey/page',
      method: 'get',
      params: {
        ...params,
        current,
        size,
      }
    })
}

// 获取草稿箱列表
export const getDraftList = (current, size, params) => {
  return request({
    url: '/api/admin/survey/draft/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
// 获取详情
export const getDetail = (id) => {
    return request({
      url: `/api/admin/survey/${id}`,
      method: 'get'
    })
}
// 获取草稿详情
export const getDraftDetail = (id) => {
    return request({
      url: `/api/admin/survey/draft/${id}`,
      method: 'get'
    })
}
// 删除结束问卷
export const remove = (ids) => {
    return request({
      url: `/api/admin/survey`,
      method: 'delete',
      params: {
        ids
      }
    })
}
// 删除草稿
export const removeDraft = (ids) => {
    return request({
      url: `/api/admin/survey/draft`,
      method: 'delete',
      params: {
        ids
      }  
    })
}
// 提前结束问卷
export const end = (id) => {
    return request({
      url: `/api/admin/survey/${id}`,
      method: 'put'
    })
}
// 添加问卷
export const add = (row) => {
    return request({
      url: '/api/admin/survey',
      method: 'post',
      data: row
    })
}
// 编辑问卷  
export const update = (row) => {
    return request({
      url: '/api/admin/survey/draft',
      method: 'put',
      data: row
    })
}
// 获取草稿箱数量
export const getDraftCount = () => {
    return request({
      url: '/api/admin/survey/draft/count',
      method: 'get'
    })
}

// 数据统计
export const statistics = (id) => {
    return request({
      url: `/api/admin/survey/statistics?id=${id}`,
      method: 'get',
    })
}
// 详细数据分页
export const getStatisticsList = (current, size, id) => {
    return request({
      url: '/api/admin/survey/statistics/page',
      method: 'get',
      params: {
        id,
        current,
        size
      }
    })
}
//详细数据查看
export const statisticsDetail = (id) => {
    return request({
      url: `/api/admin/survey/statistics/${id}`,
      method: 'get'
    })
}

// 保存到草稿
export const saveToDraft = (row) => {
    return request({
        url: '/api/admin/survey/draft',
        method: 'post',
        data: row
    })
}

// 新建问卷
export const saveQuestionnaire = (row) => {
    return request({
        url: '/api/admin/survey',
        method: 'post',
        data: row
    })
}

// 编辑问卷
export const updateDraft = (row) => {
    return request({
        url: '/api/admin/survey/draft',
        method: 'put',
        data: row
    })
}

/**模版 */
//问卷模板分页
export const getTemplateList = (current, size, params) => {
    return request({
      url: '/api/survey/template/page',
      method: 'get',
      params: {
        ...params,
        current,
        size,
      }
    })
}
// 获取详情
export const getTemplateDetail = (id) => {
    return request({
      url: `/api/survey/template/${id}`,
      method: 'get'
    })
}
// 新增模板
export const addTemplate = (row) => {
    return request({
      url: '/api/survey/template/save',
      method: 'post',
      data: row
    })
}
// 编辑模板
export const updateTemplate = (row) => {
    return request({
        url: '/api/survey/template/update',
        method: 'post',
        data: row
    })
}
// 删除模板
export const removeTemplate = (ids) => {
    return request({
      url: `/api/survey/template/remove`,
      method: 'post',
      params: {
        ids
      }  
    })
}
// 删除模板
export const setTemplate = (id) => {
    return request({
      url: `/api/survey/template/setTemplate`,
      method: 'post',
      params: {
        id
      }  
    })
}
<!--
 * @Description:积分详情弹窗
 * @Author: wangyy553
 * @Date: 2021-10-27 16:23:34
 * @LastEditors: linzq33
 * @LastEditTime: 2025-07-01 11:33:26
-->
<template>
  <el-dialog
    :fullscreen="isFullscreen"
    :visible.sync="dialogVisible"
    append-to-body="true"
    :close-on-click-modal="false"
    top="100px"
    width="60%"
    @close="close()"
  >
    <div slot="title" class="header">
      <div class="avue-crud__dialog__header">
        <span class="el-dialog__title">{{ title }}</span>
        <div class="avue-crud__dialog__menu">
          <i
            @click="isFullScreen"
            class="el-dialog__close el-icon-full-screen"
          ></i>
        </div>
      </div>
      <button type="button" aria-label="Close" class="el-dialog__headerbtn">
        <i class="el-dialog__close el-icon el-icon-close"></i>
      </button>
    </div>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      ref="crud"
      @refresh-change="refreshChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @on-load="onLoad"
    >
      <template slot="integral" slot-scope="scope">
        <span v-if="scope.row.type == 1">{{ "+" + scope.row.integral }}</span>
        <span v-if="scope.row.type == 2">{{ "-" + scope.row.integral }}</span>
      </template>
    </avue-crud>
  </el-dialog>
</template>

<script>
import { getRecord } from "@/api/integralManagement/statistics";
export default {
  props: ["visible", "userId"],

  data() {
    return {
      isFullscreen: false,
      title: "查看积分明细",
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        index: true,
        indexLabel: "序号",
        // height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: false,
        searchMenuSpan: 6,
        border: true,
        selection: false,
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: false,
        dicFlag: true,
        menu: false,
        column: [
          {
            label: "类目",
            prop: "title",
          },
          {
            label: "积分（单位：分）",
            prop: "integral",
            slot: true,
            formslot: true,
          },
          {
            label: "获取/支出时间",
            prop: "createTime",
          },
        ],
      },
      data: [],
    };
  },
  methods: {
    close() {
      this.page.currentPage = 1;
      this.page.pageSize = 20;
      this.dialogVisible = false;
    },
    isFullScreen() {
      this.isFullscreen = !this.isFullscreen;
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onload();
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onload();
    },
    refreshChange() {
      this.onload(this.page);
    },
    onload() {
      this.loading = true;
      getRecord(this.page.currentPage, this.page.pageSize, this.userId).then(
        (res) => {
          this.data = res.data.data.records;
          this.page.total = res.data.data.total;
          this.loading = false;
        }
      );
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
};
</script>

<style scoped>
.time-item {
  display: inline-block;
  width: 200px;
  font-weight: 600;
}
</style>

<template>
  <el-row ref="test">
    <el-col :span="4">
      <basic-container>
        <div class="tip">网格组织</div>
        <avue-tree  ref="tree1" :option="treeOption" :data="treeData" @node-click="nodeClick" node-key="id" :current-node-key="treeGridId"/>
      </basic-container>
    </el-col>
    <el-col :span="20">
      <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :search.sync="query" :permission="permissionList" v-model="form" ref="crud" :before-open="beforeOpen"  @row-update="rowUpdate" @row-save="rowSave" @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad" class="directSupply-dialog">
          <!-- 左侧菜单按钮 -->
          <template slot="menuLeft">
            <el-button type="danger" size="small" icon="el-icon-delete" plain @click="handleDelete" v-if="permissionList.delBtn">
              删除
            </el-button>
          </template>



          <!-- 行操作按钮 -->
          <template slot-scope="{ type, size, row, index }" slot="menu">
            <!-- <el-button :type="type" :size="size" @click.stop="preview(row)">预览</el-button> -->
            <el-button :type="type" :size="size" v-if="
                !(
                  doAuditList.includes(row.dataStatus) &&
                  permissionList.doAuditBtn
                ) &&
                !(
                  doAuthorizeList.includes(row.dataStatus) &&
                  permissionList.doAuthorizeBtn
                )
              " @click.stop="$refs.crud.rowView(row, index)">
              查看
            </el-button>
            <el-button :type="type" :size="size" @click="$refs.crud.rowEdit(row, index)" v-if="
                edit_delist.includes(row.dataStatus) &&
                permissionList.editBtn
              ">
              编辑
            </el-button>
            <el-button :type="type" :size="size" @click="rowDel(row)" v-if="
                edit_delist.includes(row.dataStatus) &&
                permissionList.delBtn
              ">
              删除
            </el-button>
            <el-button :type="type" :size="size" v-if="
                sendAuditList.includes(row.dataStatus) && permissionList.delBtn
              " @click="sendAudit(row)">
              发送审核
            </el-button>
            <el-button :type="type" :size="size" v-if="
                doAuditList.includes(row.dataStatus) &&
                permissionList.doAuditBtn
              " @click="$refs.crud.rowView(row, index)">
              审核
            </el-button>
            <el-button :type="type" :size="size" v-if="
                doAuthorizeList.includes(row.dataStatus) &&
                permissionList.doAuthorizeBtn
              " @click="$refs.crud.rowView(row, index)">
              审定
            </el-button>
          </template>

          <!-- 审核意见表单 -->
          <template slot-scope="{ disabled }" slot="failReasonForm">
            <div v-if="form.auditStatus == 2">
              工作队审核不通过：{{ form.auditFailReason }}
            </div>
            <div v-else-if="form.auditStatus == 4">
              村管理员审定不通过：{{ form.authorizeFailReason }}
            </div>
            <el-input v-else-if="
                doAuditList.includes(form.dataStatus) &&
                permissionList.doAuditBtn
              " :rows="4" :disabled="!disabled" v-model="form.failReason" type="textarea" showWordLimit maxlength="200"/>
            <el-input v-else-if="
                doAuthorizeList.includes(form.dataStatus) &&
                permissionList.doAuthorizeBtn
              " :rows="4" :disabled="!disabled" v-model="form.failReason" type="textarea" showWordLimit maxlength="200"/>
            <div v-else></div>
          </template>

          <!-- 表单操作按钮 -->
          <template slot-scope="{}" slot="menuForm">
            <el-button type="default" size="small" v-if="
                (doAuditList.includes(form.dataStatus) &&
                  permissionList.doAuditBtn) ||
                (doAuthorizeList.includes(form.dataStatus) &&
                  permissionList.doAuthorizeBtn)
              " @click="audit(false)">
              不通过
            </el-button>
            <el-button type="primary" size="small" v-if="
                (doAuditList.includes(form.dataStatus) &&
                  permissionList.doAuditBtn) ||
                (doAuthorizeList.includes(form.dataStatus) &&
                  permissionList.doAuthorizeBtn)
              " @click="audit(true)">
              通过
            </el-button>
          </template>
        </avue-crud>
      </basic-container>
    </el-col>
  </el-row>
</template>

<script>
import { mapGetters } from "vuex";
import { validatename20,phoneValid,noSpace } from "@/util/validate";

import {getGridTree} from "@/api/digitalGovernance/grid";
// 审核状态

export default {
  components: {
  },
  props:{
    moduleName: {
      type: String,
      default: "",
    },
    moduleKey: {
      type: String,
      default: "",
    },
    funObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    const validatePhone = (rule, value, callback) => {
      if (!phoneValid(value)) {
        callback("请输入正确的联系方式");
      } else {
        callback();
      }
    };
    const checkRealName = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("请输入姓名"));
      } else if (!noSpace(value)) {
        return callback(new Error("姓名不能含有空格"));
      } else if (!validatename20(value)) {
        return callback(new Error("姓名必须是中文，长度2-20"));
      } else {
        callback();
      }
    };

    return {
      treeGridId: '',
      treeData: [],
      treeOption: {
          nodeKey: 'id',
          defaultExpandAll: false,
          addBtn: false,
          menu: false,
          size: 'small',
          props: {
            label: 'name',
            value: 'id',
            children: 'children'
          }
        },

      dialogVisible: false,
      limitCountImg: 1, //上传图片的最大数量
      srcList: [],
      tabLoading: true, //类别loading
      form: {},
      query: {},
      loading: true,
      box: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      type: "",
      // allTypeData: [],
      selectionList: [],
      // viewDialog: false, //详情弹窗
      option: {
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        selection: true,
        // menuWidth: 210,
        // menuAlign:"center",
        // labelWidth: 120,
        dialogWidth: 1200,
        dialogClickModal: false,
        viewBtn: false,
        viewBtnText: "预览",
        delBtn: false, //默认是有删除、编辑按钮
        editBtn: false,
        dialogCustomClass: "directSupply-dialog",
        column: [
          {
              label: "所属网格",
              prop: "gridId",
              span: 24,
              type: "tree",
              dicData: [],
              addDisabled: false,
              dataType: "string",
              dicUrl: "/api/admin/grid/tree",
              hide: true,
              props: {
                label: "name",
                value: 'id',
                children: 'children'
              },
              rules: [
                {
                  required: true,
                  message: "请选择所属网格",
                  trigger: ["blur", "change"]
                }
              ]
          },
          {
            label: "姓名",
            prop: "name",
            searchSpan: 4,
            span: 24,
            search: true,
            maxlength: 20,
            showWordLimit: true,
            rules: [{ required: true, whitespace: true, trigger: ["blur", "change"], validator: checkRealName }]
          },
          {
            label: "联系方式",
            prop: "phone",
            span: 24,
            maxlength: 20,
            showWordLimit: true,
            rules: [{ required: true, whitespace: true, trigger: ["blur", "change"], validator: validatePhone }]
          },
          {
            label: "岗位",
            prop: "postId",
            dataType: "string",
            search: true,
            row: true,
            searchSpan: 5,
            span: 24,
            type: "select",
            dicUrl: `/api/blade-system/dict-biz/dictionary?code=gridStaffPost`,
            props: {
              //对应select的属性
              label: "dictValue",
              value: "dictKey",
            },
            filterable: true,
            rules: [
              {
                required: true,
                message: "请选择岗位",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "备注",
            prop: "remark",
            hide: true,
            type: "textarea",
            span: 24,
            maxlength: 250,
            showWordLimit: true,
            addDisplay: true, //表单新增时是否可见
            editDisplay: true,
            viewDisplay: true,
          },
          {
            label: "数据状态",
            prop: "dataStatus",
            dataType:"string",
            search:true,
            searchSpan: 5,
            addDisplay: false, //表单新增时是否可见
            editDisplay: false,
            viewDisplay: false,
            type: "select",
            dicUrl: `/api/blade-system/dict-biz/dictionary?code=dataStatus`,
            props: {
              //对应select的属性
              label: "dictValue",
              value: "dictKey",
            },
            filterable: true,
          },
          {
            label: "审核意见",
            prop: "failReason",
            addDisplay: false, //表单新增时是否可见
            editDisplay: true,
            viewDisplay: true,
            hide: true,
            span: 24,
            maxlength: 200,
            showWordLimit: true,
            showColumn: false,
            readonly: true,
            formslot: true,
          },
        ],
      },
      data: [],
      failReason: "",
      edit_delist: [0, 3, 8],
      sendAuditList: [0, 3],
      doAuditList: [1, 4, 6],
      doAuthorizeList: [2, 5, 7],
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    // 模块名称
    // moduleName() {
    //   return  "领导班子";
    // },
    // moduleKey() {
    //   return  "leader";
    // },
    // 优化后的权限计算属性
    permissionList() {
      const prefix = this.moduleKey;
      return Object.entries({
        addBtn: "_add",
        viewBtn: "_view",
        editBtn: "_edit",
        delBtn: "_delete",
        doAuditBtn: "_doAudit",
        doAuthorizeBtn: "_doAuthorize",
      }).reduce((acc, [key, suffix]) => {
        acc[key] = this.vaildData(this.permission[`${prefix}${suffix}`], false);
        return acc;
      }, {});
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    // console.log(this.permission);
  },

  mounted(){
    this.initData();
  },
  methods: {
    nodeClick(data) {
        this.treeGridId = data.id;
        this.page.currentPage = 1;
        this.onLoad(this.page);
    },
    initData() {
      getGridTree().then(res => {
          this.$nextTick(() => {
              this.treeData =res.data.data;
          });
          const column = this.findObject(this.option.column, "gridId");
          column.dicData = res.data.data;
        });
      },

    // 详情弹窗
    // preview(row) {
    //   this.form = row;
    //   this.queryDetail();
    // },
    // 新增
    rowSave(row, done, loading) {
      if (!this.form.attachList) {
        this.form.attachList = [];
      }
      let submitData = {
        gridId: row.gridId,
        name: row.name,
        phone: row.phone,
        postId: row.postId,
        remark: row.remark

      };
      this.funObj.save(submitData).then(
        async () => {
          this.$message.success(`成功新增`);
          await this.onLoad(this.page, this.query);
          done();
        },
        () => {
          loading();
        }
      );
    },
    // 修改
    rowUpdate(row, index, done, loading) {
      if (!this.form.attachList) {
        this.form.attachList = [];
      }
      let submitData = {
        id: row.id,
        gridId: row.gridId,
        name: row.name,
        phone: row.phone,
        postId: row.postId,
        remark: row.remark
      };
      this.funObj.update(submitData).then(
        async () => {
          this.$message.success(`成功修改`);
          await this.searchReset();
          done();
        },
        () => {
          loading();
        }
      );
    },
    // 删除
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return this.funObj.remove({
            ids: row.id,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // 重置
    searchReset() {
      this.query = {};
      this.treeGridId = '';
      this.$refs.tree1.setCurrentKey(null);

      this.onLoad(this.page);
    },
    // 搜索
    searchChange(params, done) {
      this.page.currentPage = 1;
      this.query = params;
      this.onLoad(this.page, params);
      done();
    },
    // 勾选
    selectionChange(list) {
      this.selectionList = list;
    },
    // 重置勾选
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    // 多选删除
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }

      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return this.funObj.remove({
            ids: this.ids,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    // 查看预览
    // queryDetail() {
    //   this.funObj.detail({
    //     id: this.form.id,
    //   }).then(() => {
    //     this.showUview();
    //   });
    // },
    // 打开前回调
    async beforeOpen(done, type) {
      this.initData();
      if (type === "add") {
        this.form = {};
        done();
        return;
      }
      if (["edit", "view"].includes(type)) {
        this.funObj.detail({
          id: this.form.id,
        }).then((res) => {
          this.form = res.data.data;
          done();
        });
      }
    },
    // 当前页切换
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    // 页面显示条数切换
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    // 刷新
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    // 首次加载
    async onLoad(page, params = {}) {

      this.loading = true;
      let query = {
        ...params,
        current: this.page.currentPage,
        size: this.page.pageSize
      };
      if (query.updateTime) {
        if (Array.isArray(query.updateTime)) {
          query.begin = query.updateTime[0];
          query.end = query.updateTime[1];
        }
        delete query.updateTime;
      }
      // this.type !== "全部" ? (query.type = this.type) : null;
      query.gridId = this.treeGridId;
      let res = await this.funObj.getList(query);
      if (res && res.data && res.data.success) {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        if(this.page.currentPage >1 && this.page.currentPage > data.pages){
          this.page.currentPage = data.pages;
          this.onLoad();
        }
        this.loading = false;
        this.selectionClear();
      }
    },
    //发送审核
    sendAudit(row) {
      this.funObj.sendAudit({ id: row.id }).then(() => {
        this.onLoad(this.page, this.query);
        this.$message({
          type: "success",
          message: "操作成功!",
        });
      });
    },

    //审核
    audit(isPass) {
      if (!isPass && this.form.failReason == "") {
        this.$message.info("请填写不通过原因");
        return;
      }
      if (this.doAuthorizeList.includes(this.form.dataStatus)) {
        this.funObj.doAuthorize({
          id: this.form.id,
          isPass: isPass,
          remark: isPass ? "" : this.form.failReason,
        }).then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.closeDialog();
        });
      } else {
        this.funObj.doAudit({
          id: this.form.id,
          isPass: isPass,
          remark: isPass ? "" : this.form.failReason,
        }).then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.closeDialog();
        });
      }
    },
  },
};
</script>
<style scoped lang="scss">

.tip {
    padding: 4px 8px;
    // background-color: #ecf8ff;
    border-radius: 4px;
    border-left: 5px solid #50bfff;
    margin: 0 0 5px 0;
}

.img-tiny {
  height: 100px;
  width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(245, 247, 250);
}
.btn-group-container {
  display: flex;
}
</style>

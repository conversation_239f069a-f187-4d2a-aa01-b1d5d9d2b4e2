/*
 * @Description: 重构消息框提示，防止重复点击重复弹出message弹框
 * @Author: chenz76
 * @Date: 2021-08-19 16:34:57
 * @LastEditors: chenz76
 * @LastEditTime: 2021-08-24 09:23:43
 */

import { Message } from 'element-ui';
  let messageInstance = null;
  const resetMessage = (options,close) => {
    // 如果已经存在实例则关闭
    if(messageInstance) {
        messageInstance.close()
    }
    // 不存在实例则赋予实例
    messageInstance = Message(options)
    // 当有关闭参数的时候关闭实例,为什么添加close参数后面详细说明
    if(close){
      messageInstance.close()
    }
  }
  ;['error','success','info','warning'].forEach(type => {
    resetMessage[type] = options => {
        if(typeof options === 'string') {
            options = {
                message:options
            }
        }
        options.type = type
        return resetMessage(options)
    }
  })
  export const myMessage = resetMessage
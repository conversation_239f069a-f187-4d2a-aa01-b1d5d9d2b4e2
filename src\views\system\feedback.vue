<!--
 * @Author: la<PERSON><PERSON><PERSON>g
 * @Date: 2021-08-17 10:56:10
 * @LastEditTime: 2022-01-07 17:28:12
 * @LastEditors: chenz76
 * @Description: 意见反馈
-->
<template>
  <basic-container>
    <avue-crud
      ref="crud"
      :option="option"
      :table-loading="loading"
      :search.sync="search"
      :data="data"
      :page.sync="page"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button v-if="permission.feedback_delete" type="danger" size="small" icon="el-icon-delete" plain @click="handleDelete">删除</el-button>
      </template>
      <template slot="status" slot-scope="{ row }">
        <el-tag v-if="row.status === 0">未回复</el-tag>
        <el-tag v-else-if="row.status === 1" type="success">已回复</el-tag>
      </template>
      <template slot="menu" slot-scope="{ row }">
        <el-button v-if="permission.feedback_view" type="text" icon="el-icon-view" size="small" @click="rowView(row)">查看</el-button>
        <el-button v-if="permission.feedback_comment && row.status === 0" type="text" icon="el-icon-chat-round" size="small" @click="rowComment(row)">回复</el-button>
        <el-button v-if="permission.feedback_delete" type="text" icon="el-icon-delete" size="small" @click="rowDel(row)">删除</el-button>
      </template>
    </avue-crud>

    <el-dialog title="详情" :visible.sync="showDetail" top="15vh" append-to-body center>
      <el-timeline v-if="showDetail">
        <el-timeline-item :timestamp="currentObject.createTime" placement="top" type="success">
          <div class="detail-box">
            <h4>发送人：{{ currentObject.createUserName }}</h4>
            <template v-if="currentObject.attachUrl">
              <el-image
                class="image"
                :src="currentObject.attachUrl"
                alt="image"
                :preview-src-list="[currentObject.attachUrl]"
                :z-index="9999"
              />
            </template>
            <p>{{ currentObject.content }}</p>
          </div>
        </el-timeline-item>
        <template v-if="!currentObject.reply">
          <el-timeline-item type="info"> 暂无回复内容 </el-timeline-item>
        </template>
        <template v-else>
          <el-timeline-item :timestamp="currentObject.replyTime" placement="top" type="success">
            <div>
              <h4>回复人：{{ currentObject.replyUserName }}</h4>
              <p>{{ currentObject.reply }}</p>
            </div>
          </el-timeline-item>
        </template>
      </el-timeline>
    </el-dialog>
    <el-dialog custom-class="feedback-comment" title="回复" :visible.sync="showComment" top="15vh" append-to-body center>
      <div v-if="showComment">
        <div class="comment-box">
          <div class="left">
            <h4>反馈意见</h4>
            <template v-if="currentObject.attachUrl">
              <el-image
                class="image"
                :src="currentObject.attachUrl"
                alt="image"
                :preview-src-list="[currentObject.attachUrl]"
                :z-index="9999"
              />
            </template>
            <p>
              {{ currentObject.content }}
            </p>
          </div>
          <div class="right">{{ currentObject.createTime }}</div>
        </div>
      </div>
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item prop="reply">
          <el-input v-model.trim="form.reply" type="textarea" :autosize="{ minRows: 3, maxRows: 15 }" placeholder="填写回复" :maxlength="1000" show-word-limit />
        </el-form-item>
      </el-form>
      <span slot="footer">
        <el-button type="primary" :loading="showBtnLoading" @click="doComment()">回复</el-button>
        <el-button @click="showComment = false">关闭</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import { getFeedback, remove, getList, comment } from '@/api/system/feedback'
import { mapGetters } from 'vuex'

export default {
  name: 'Feedback',
  data() {
    return {
      showDetail: false,
      currentObject: {},
      showComment: false,
      form: {
        id: undefined,
        reply: ''
      },
      rules: {
        reply: [{ required: true, message: '回复不能为空', trigger: 'blur' }]
      },
      showBtnLoading: false,
      search: {},
      query: {},
      loading: true,
      selectionList: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        index: true,
        border: true,
        selection: true,
        addBtn: false,
        editBtn: false,
        viewBtn: false,
        delBtn: false,
        dialogClickModal: false,
        column: [
          {
            label: '发送人',
            prop: 'createUserName'
          },
          {
            label: '所属机构',
            prop: 'createDeptName'
          },
          {
            label: '反馈意见',
            prop: 'content',
            search: true,
            maxlength: 50,
            overHidden: true,
            minWidth: '100px',
            maxWidth: '180px'
          },
          {
            label: '提交时间',
            prop: 'createTime'
          },
          {
            label: '回复状态',
            prop: 'status',
            type: 'select',
            dicUrl: '/api/blade-system/dict/dictionary?code=feedback_status',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            dataType: 'number',
            slot: true,
            search: true,
            align: 'center'
          }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        viewBtn: this.vaildData(this.permission.feedback_view, false),
        commentBtn: this.vaildData(this.permission.feedback_comment, false),
        delBtn: this.vaildData(this.permission.feedback_delete, false)
      }
    },
    ids() {
      let ids = []
      this.selectionList.forEach((ele) => {
        ids.push(ele.id)
      })
      return ids
    }
  },
  methods: {
    async rowView(row) {
      const res = await getFeedback(row.id)
      if (res) {
        this.currentObject = res.data.data
      }
      this.showDetail = true
    },
    async rowComment(row) {
      const res = await getFeedback(row.id)
      if (res) {
        this.currentObject = res.data.data
      }
      this.form.id = row.id
      this.form.reply = ''
      this.showComment = true
    },
    doComment() {
      this.$refs['form'].validate(async (valid) => {
        if (valid) {
          this.showBtnLoading = true
          const res = await comment(this.form)
          if (res) {
            this.$message.success('回复成功')
          }
          this.showBtnLoading = false
          this.showComment = false
          this.onLoad(this.page)
        }
      })
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return remove([row.id])
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        })
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.$refs.crud.toggleSelection()
        })
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then((res) => {
        this.data = res.data.data.records
        this.page.total = res.data.data.total
        this.loading = false
        this.selectionClear()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 修改textarea count位置
::v-deep .feedback-comment .el-input__count {
  height: 25px;
  line-height: 25px;
  bottom: -25px !important;
}
.detail-box {
  display: flex;
  flex-direction: column;
}
.comment-box {
  display: flex;
  background-color: #e6f7ff;
  margin-bottom: 4em;
  padding: 1em;
  .left {
    flex: auto;
    display: flex;
    flex-direction: column;
    padding-left: 1em;
    p {
      opacity: 0.9;
    }
  }
  .right {
    flex: none;
    padding: 2em;
    display: flex;
    align-items: center;
  }
}
.image {
  height: 25vh;
  margin-bottom: 1em;
  ::v-deep .el-image__inner {
    height: 100%;
    width: auto;
  }
  &:nth-last-child(2) {
    margin-bottom: 0;
  }
}
</style>

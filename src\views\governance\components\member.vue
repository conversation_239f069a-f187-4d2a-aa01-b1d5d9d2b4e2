<!-- 
/**
  * @Author: 林中奇
  * @Date: 2025/08/04
  * @lastAuthor:
  * @lastChangeDate:
  * @Explain: 户籍管理-成员
  */
 
-->

<template>
  <div>
    <avue-crud ref="childCrud" v-model="form" :option="option" :table-loading="loading" :data="data"
      :permission="permissionList" :before-open="beforeOpen" @row-update="rowUpdate" @row-save="rowSave"
      @row-del="rowDel" @selection-change="selectionChange" @search-change="searchChange" @search-reset="searchReset"
      @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger" v-if="permission.census_member_delete" size="small" icon="el-icon-delete" plain
          @click="handleDelete">批量删除</el-button>
        <el-button v-if="permission.census_export && type == 'list'" type="primary" size="small"
          icon="el-icon-download el-icon--right" :disabled="disableButton" @click="handleExport">导 出</el-button>
      </template>
      <template slot="userIdForm">
        <el-select v-model="form.userId" placeholder="请选择 成员" filterable remote :remote-method="searchUser"
          v-load-more="loadMore" @change="userSelectChange">
          <el-option v-for="(item, index) in userOptions" :key="index" :value="item.id" :label="item.realName">
            <div class="census-user-option">
              <img class="top-bar__img" :src="item.avatar" onerror="this.src='/img/defaultAvatarImg.png'" />
              <span class="content">{{ item.realName + (item.phone ? ' - ' + item.phone : '') }}</span>
            </div>
          </el-option>
        </el-select>
      </template>
    </avue-crud>
  </div>
</template>

<script>
import { getMemberList, getVillagerDetail, removeVillager, addVillager, updateVillager, getVillagerList } from '@/api/governance/census'
import { mapGetters } from 'vuex'
import { getToken } from '@/util/auth'
import { cardId, isMobile } from '@/util/validate'
import { handleDownload } from '@/util/download';
import { getDictionary } from "@/api/system/dict";

export default {
  props: {
    dept: {
      default: '',
      type: String
    },
    type: {
      default: 'list',
      type: String
    },
    familyId: {
      default: '',
      type: String
    }
  },
  directives: {
    'load-more': {
      bind (el, binding) {
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      }
    }
  },
  data () {
    var checkMobile = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入联系方式'))
      } else if (value.length > 0 && !isMobile(value)) {
        callback(new Error('手机号码格式错误'))
      } else {
        callback()
      }
    }
    var checkIdNo = (rule, value, callback) => {
      if (!value) {
        return callback();
      } else if (cardId(value)[0]) {
        return callback(new Error("身份证号格式错误"));
      } else {
        return callback();
      }
    };
    const checkName = (rule, value, callback) => {
      if (value !== '' && value.length > 1 && value.length < 21) {
        if (!value.trim()) {
          callback(new Error('姓名不能为纯空格'))
        }
      } else {
        callback(new Error('姓名长度在2到20个字符'))
      }
      callback()
    };
    return {
      disableButton: false,
      userOptions: [],
      baseInfo: {},

      form: {},
      query: {},
      loading: true,
      data: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        border: true,
        index: true,
        selection: true,
        selectable: (row, index) => {
          return row.relationship != 1;
        },
        viewBtn: true,
        searchMenuSpan: 6,
        addBtn: false,
        dialogCustomClass: 'census-member-drawer',
        column: [
          {
            label: '姓名',
            prop: 'realName',
            maxlength: 30,
            search: true,
            // showWordLimit: true,
            width: 110,
            rules: [{ required: true, validator: checkName, trigger: "blur" }],
          },
          {
            label: '户籍关系',
            prop: 'relationship',
            placeholder: '请选择 户主或与户主关系',
            width: 110,
            slot: true,
            type: 'select',
            editDisabled: true,
            // dicUrl: '/api/blade-system/dict/dictionary?code=householder_relationship',
            dicData: [],
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            dataType: 'number',
            rules: [{ required: true, message: '请选择与户主关系', trigger: 'change' }],
            span: 12,
          },
          {
            label: '性别',
            prop: 'sex',
            width: 90,
            type: 'radio',
            dicUrl: '/api/blade-system/dict/dictionary?code=sex',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            dataType: 'number',
            rules: [{ required: true, message: '请选择性别', trigger: 'change' }],
            span: 12
          },
          {
            label: '出生年月',
            prop: 'birthDate',
            type: 'date',
            rules: [{ required: true, message: '请选择出生年月', trigger: 'change' }],
            span: 12,
            type: "date",
            hide: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
          },
          {
            label: '联系方式',
            prop: 'contract',
            type: 'input',
            maxlength: 11,
            search: true,
            // showWordLimit: true,
            rules: [{ required: true, validator: checkMobile, trigger: 'change' }],
            span: 12
          },
          {
            label: '身份证',
            prop: 'idno',
            type: 'input',
            maxlength: 20,
            rules: [{ validator: checkIdNo, trigger: 'change' }],
            span: 12
          },
          {
            label: '民族',
            prop: 'nation',
            type: 'select',
            dicUrl: '/api/blade-system/dict/dictionary?code=nation',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            dataType: 'number',
            span: 12
          },
          {
            label: '人员标签',
            prop: 'personTag',
            type: 'select',
            dicUrl: '/api/blade-system/dict/dictionary?code=person_tag',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            hide: true,
            dataType: 'number',
            span: 12
          },
          {
            label: '技能标签',
            prop: 'skillTag',
            type: 'select',
            dicUrl: '/api/blade-system/dict/dictionary?code=skill_tag',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            hide: true,
            dataType: 'number',
            span: 12
          },
          {
            label: '政治面貌',
            prop: 'politicalStatus',
            type: 'select',
            dicUrl: '/api/blade-system/dict/dictionary?code=political_status',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            dataType: 'number',
            span: 12,
            hide: true,
          },
          {
            label: '婚姻情况',
            prop: 'maritalStatus',
            type: 'select',
            dicUrl: '/api/blade-system/dict/dictionary?code=marital_status',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            dataType: 'number',
            span: 12,
            hide: true,
          },
        ]
      },
    }
  },
  created () {
    if (this.type == 'list') {
      this.option.addBtn = false
    } else {
      this.option.addBtn = true
    }
    // 字典获取
    getDictionary({ code: 'householder_relationship' }).then(res => {
      const column = this.findObject(this.option.column, "relationship");
      const data = res.data.data
      if (this.type != 'list') {
        for (let i = 0; i < data.length; i++) {
          const e = data[i]
          if (e.dictKey == 1 && e.dictValue == '户主') {
            data[i].disabled = true
          }
        }
      }
      column.dicData = data
    })
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList () {
      return {
        addBtn: this.vaildData(this.permission.census_member_add, false),
        viewBtn: this.vaildData(this.permission.census_member_view, false),
        delBtn: this.vaildData(this.permission.census_member_delete, false),
        editBtn: this.vaildData(this.permission.census_member_edit, false)
      }
    },
    ids () {
      let ids = []
      this.selectionList.forEach((ele) => {
        ids.push(ele.id)
      })
      return ids.join(',')
    }
  },
  watch: {
    dept: function (val) {
      this.deptId = val
      this.page.currentPage = 1
      this.onLoad(this.page)
    },
  },
  methods: {
    handleClose () {
      this.baseInfo = this.$options.data().baseInfo
      this.selectionClear()
      this.data = []
      this.loading = true
    },
    relationChange (column, val) {
      // console.log('313', column, val);
    },

    handleExport () {
      this.$confirm('是否导出户籍数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let downloadUrl = `/api/census/export?${this.website.tokenHeader}=${getToken()}`
        if (this.selectionList.length > 0) {
          downloadUrl += `&ids=${this.ids}`
        }
        const householderName = this.query.householderName || ''
        if (householderName.length > 0) {
          downloadUrl += `&householderName=${householderName}`
        }
        this.icon = "el-icon-loading el-icon--right";
        this.disableButton = true;
        const result = await handleDownload(downloadUrl);
        if (result != null) {
          this.icon = "el-icon-download el-icon--right";
          this.disableButton = false;
        }
      })
    },
    rowSave (row, done, loading) {
      console.log(334, row);
      const _row = { ...row }
      console.log('336', _row);

      _row.cid = this.familyId;
      addVillager(_row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          done()
        },
        (error) => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate (row, index, done, loading) {
      let _this = this;
      const _row = { ...row }
      _row.cid = this.familyId;
      updateVillager(_row).then(
        () => {
          this.onLoad(this.page);
          //如果是户主，修改标题，刷新父类
          if (_row.relationshipName === '户主') {
            _this.baseInfo.householderName = _row.realName;
            this.$emit('reflashTable')
          }
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          done()
        },
        (error) => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowDel (row) {
      // 判断是否户主
      if (row.relationshipName == '户主') {
        const message = this.type == 'list' ? '请在“家庭”页删除户主信息!' : '请关闭弹窗在“家庭”页删除户主信息!'
        this.$message({
          type: 'error',
          message
        })
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return removeVillager(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        })
    },
    handleDelete () {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return removeVillager(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.$refs.childCrud.toggleSelection()
        })
    },
    async beforeOpen (done, type) {
      if (['edit', 'view'].includes(type)) {
        // 部分字段修改
        // const column = this.findObject(this.option.column, 'relationship')
        // column.disabled = true
        // console.log('409');

        let data = {}
        await new Promise((resolve) => {
          getVillagerDetail(this.form.id, type === 'view').then((res) => {
            data = res.data.data
            this.form = data
            resolve()
          })
        })
      }
      done()
    },
    searchReset () {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange (params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange (list) {
      this.selectionList = list
    },
    selectionClear () {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange (currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange (pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange () {
      this.onLoad(this.page, this.query)
    },
    onLoad (page, params = {}) {
      this.loading = true
      params.deptId = this.deptId
      if (this.type == 'list') {
        getVillagerList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then((res) => {
          const data = res.data.data
          this.page.total = data.total
          this.data = data.records
          this.loading = false
          this.selectionClear()
        })
      } else {
        getMemberList(this.familyId).then((res) => {
          this.data = res.data.data
          // this.page.total = data.total
          this.loading = false
          this.selectionClear()
        })
      }

    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .census-user-option {
  display: flex;
  align-items: center;
  padding: 5px 10px;

  .content {
    margin-left: 10px;
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>

<style lang="scss">
.census-member-drawer .el-textarea .el-input__count {
  height: 25px;
  line-height: 25px;
  bottom: -25px !important;
}
</style>

import request from '@/router/axios';


/**
 * @description: 请求列表
 * @param {object} params
 * @author:
 */
export const getList = (params) => {
  return request({
    url: '/api/admin/enterprise/page',
    method: 'get',
    params,
  })
}


/**
 * @description: 新增
 * @param {object} data
 * @author:
 */
export const save = (data) => {
  return request({
    url: '/api/admin/enterprise/save',
    method: 'post',
    data,
  })
}

/**
 * @description: 查看详情
 * @param {object} params
 * @author:
 */
export const detail = (params) => {
  return request({
    url: '/api/admin/enterprise/detail',
    method: 'get',
    params,
  })
}

/**
 * @description: 修改
 * @param {object} data
 * @author:
 */
export const update = (data) => {
  return request({
    url: '/api/admin/enterprise/update',
    method: 'post',
    data,
  })
}



/**
 * @description: 删除
 * @param {object} params
 * @author:
 */
export const remove = (params) => {
  return request({
    url: '/api/admin/enterprise/remove',
    method: 'post',
    params,
  })
}

/**
 * @description: 发送审核
 * @param {object} params
 * @author:
 */
export const sendAudit = (params) => {
  return request({
    url: '/api/admin/enterprise/sendAudit',
    method: 'post',
    params,
  })
}

/**
 * @description: 审核
 * @param {object} params
 * @author:
 */
export const doAudit = (data) => {
  return request({
    url: '/api/admin/enterprise/doAudit',
    method: 'post',
    data,
  })
}

/**
 * @description: 审定
 * @param {object} params
 * @author:
 */
export const doAuthorize = (data) => {
  return request({
    url: '/api/admin/enterprise/doAuthorize',
    method: 'post',
    data,
  })
}

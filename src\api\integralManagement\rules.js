/**
 * @Author: zhengjh43
 * @Date: 2024-01-22 15:32:44
 * @LastEditors: zhengjh43
 * @LastEditTime: 2024-01-24 10:38:28
 * @Explain: 个性化积分规则
 */
import request from "@/router/axios";

/**
 * @description: 获得个性化积分规则分页
 * @param {object}params
 */
export const getList = params => {
  return request({
    url: "/api/admin/integral-custom-rule/page",
    method: "get",
    params
  });
};

/**
 * @description: 抽奖详情
 * @param {int} id
 */
export const detail = params => {
  return request({
    url: "/api/admin/integral-custom-rule/detail",
    method: "get",
    params
  });
};

/**
 * @description: 新增
 * @param {object} data
 */
export const add = data => {
  return request({
    url: "/api/admin/integral-custom-rule/save",
    method: "post",
    data
  });
};

/**
 * @description: 修改
 * @param {object} data
 */
export const update = data => {
  return request({
    url: "/api/admin/integral-custom-rule/update",
    method: "post",
    data
  });
};

/**
 * @description: 修改规则应用
 * @param {object} data
 */
export const changeIsOpen = data => {
  let formData = new FormData();
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      formData.append(key, data[key]);
    }
  }
  return request({
    url: "/api/admin/integral-custom-rule/change",
    method: "post",
    data: formData
  });
};

/**
 * @description: 批量删除
 * @param {int}id
 */
export const remove = ({id}) => {
  let formData = new FormData();
  formData.append("ids", id);
  return request({
    url: "/api/admin/integral-custom-rule/remove",
    method: "post",
    data: formData
  });
};

/**
 * @description: 获取通信合作社人员系数
 * @param {int}id
 */
export const getCommunicateCoefficient = params => {
  return request({
    url: "/api/admin/integral-custom-rule/communicate-coefficient",
    method: "get",
    params
  });
};

/**
 * @description: 更新通信合作社人员系数
 * @param {int}id
 */
export const updateCommunicateCoefficient = data => {
  return request({
    url: "/api/admin/integral-custom-rule/update/communicate-coefficient",
    method: "post",
    data
  });
};

/**
 * @description: 获取活动期间系数
 * @param {int}id
 */
export const getActivityCoefficient = params => {
  return request({
    url: "/api/admin/integral-custom-rule/activity-coefficient",
    method: "get",
    params
  });
};
/**
 * @description: 更新活动期间系数
 * @param {int}id
 */
export const updateActivityCoefficient = data => {
  return request({
    url: "/api/admin/integral-custom-rule/update/activity-coefficient",
    method: "post",
    data
  });
};

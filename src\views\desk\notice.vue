<template>
  <el-row ref="test">
    <el-col :span="24">
      <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :search.sync="query"
          :permission="permissionList" v-model="form" ref="crud" :before-open="beforeOpen" @row-update="rowUpdate"
          @row-save="rowSave" @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset"
          @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange"
          @refresh-change="refreshChange" @on-load="onLoad" class="directSupply-dialog">
          <!-- 左侧菜单按钮 -->
          <template slot="menuLeft">
            <el-button size="small" icon="el-icon-document-add" plain @click="updateStatus(1)"
              v-if="permissionList.publishBtn">
              发布
            </el-button>
            <el-button size="small" icon="el-icon-document-delete" plain @click="updateStatus(0)"
              v-if="permissionList.cancelBtn">
              取消发布
            </el-button>
            <el-button type="danger" size="small" icon="el-icon-delete" plain @click="handleDelete"
              v-if="permissionList.delBtn">
              删除
            </el-button>
          </template>

          <!-- 状态显示 -->
          <template slot-scope="{ row }" slot="status">
            <el-tag type="danger" v-if="row.status === 0">未发布</el-tag>
            <el-tag type="success" v-if="row.status === 1">已发布</el-tag>
          </template>

          <!-- 行操作按钮 -->
          <template slot-scope="{ type, size, row, index }" slot="menu">
            <el-button :type="type" :size="size" @click.stop="preview(row)">预览</el-button>
            <el-button :type="type" :size="size" @click.stop="$refs.crud.rowView(row, index)"
              v-if="permissionList.viewBtn">
              查看
            </el-button>
            <el-button :type="type" :size="size" @click="$refs.crud.rowEdit(row, index)" v-if="permissionList.editBtn">
              编辑
            </el-button>
            <el-button :type="type" :size="size" @click="rowDel(row)" v-if="permissionList.delBtn">
              删除
            </el-button>
          </template>

        </avue-crud>
      </basic-container>
    </el-col>
    <Uview :dialogVisible="uviewVisible" :changeVisible="changeUviewVisible" ref="uview" />
  </el-row>
</template>

<script>
import Uview from "@/components/uview/main.vue";
import { mapGetters } from "vuex";

import * as funList from "@/api/desk/notice/";
import { getDictionary } from "@/api/system/dict";

export default {
  components: {
    Uview,
  },
  props: {
    funObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data () {
    return {
      uviewVisible: false,
      dialogVisible: false,
      limitCountImg: 1, //上传图片的最大数量
      srcList: [],
      tabLoading: true, //类别loading
      form: {},
      query: {},
      loading: true,
      box: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      type: "",
      // allTypeData: [],
      selectionList: [],
      // viewDialog: false, //详情弹窗
      option: {
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        selection: true,
        // menuWidth: 210,
        // menuAlign:"center",
        // labelWidth: 120,
        dialogWidth: 1200,
        dialogClickModal: false,
        viewBtn: false,
        viewBtnText: "预览",
        delBtn: false, //默认是有删除、编辑按钮
        editBtn: false,
        dialogCustomClass: "directSupply-dialog",
        column: [
          {
            label: "标题",
            prop: "title",
            searchSpan: 6,
            span: 24,
            search: true,
            maxlength: 100,
            showWordLimit: true,
            rules: [
              {
                required: true,
                whitespace: true,
                message: "请输入标题",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "用户端",
            prop: "platform",
            dataType: "string",
            span: 24,
            type: "radio",
            dicData: [],
            filterable: true,
            rules: [
              {
                required: true,
                message: "请选择用户端",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "消息类型",
            prop: "noticeTypeAdd",
            dataType: "string",
            type: "select",
            hide: true,
            span: 24,
            dicData: [],
            props: {
              //对应select的属性
              label: "dictValue",
              value: "dictKey",
            },
            filterable: true,
            rules: [
              {
                required: true,
                message: "请选择消息类型",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "消息类型",
            prop: "noticeType",
            dataType: "string",
            search: true,
            searchSpan: 6,
            span: 24,
            type: "select",
            dicData: [],
            props: {
              //对应select的属性
              label: "dictValue",
              value: "dictKey",
            },
            filterable: true,
            editDisplay: false,
            addDisplay: false,
            viewDisplay: false,
          },
          {
            label: "消息位置",
            prop: "position",
            dataType: "string",
            span: 24,
            hide: true,
            type: "select",
            dicData: [],
            filterable: true,
            rules: [
              {
                required: true,
                message: "请选择消息位置",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "发布范围",
            prop: "deptIds",
            filter: true,
            type: "tree",
            checkStrictly: true,
            multiple: true,
            dicUrl: "/api/blade-system/dept/tree",
            dicMethod: "get",
            props: {
              label: "title",
              value: "value",
            },
            expandOnClickNode: false,
            span: 24,
            rules: [{ required: true, message: "请选择发布范围", type: "array", trigger: ['blur', 'change'] }],
          },
          {
            label: "发布对象",
            prop: "target",
            span: 24,
            type: "radio",
            dicData: [
              { label: '全体', value: '1' },
              { label: '仅村干部', value: '2' },
              { label: '仅镇干部', value: '3' },
            ],
            filterable: true,
            rules: [
              {
                required: true,
                message: "请选择信息类别",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "发布时间",
            prop: "releaseTime",
            slot: true,
            addDisplay: false, // 表单新增时是否可见
            editDisplay: false,
            viewDisplay: false,
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "发布状态",
            prop: "status",
            type: "select",
            slot: true,
            addDisplay: false, // 表单新增时是否可见
            editDisplay: false,
            viewDisplay: false,
          },
          {
            type: "ueditor",
            label: "通知内容",
            prop: "content",
            component: "AvueUeditor",
            // options: {
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            customConfig: {
              excludeMenus: [
                "code",
                "todo",
                "fontName",
                "video",
                "table",
                "source",
                "fullScreen",
              ],
              uploadImgMaxLength: 1, //限制单次图片上传张数
            }, //wangEditor编辑的配置
            propsHttp: {
              res: "data",
              url: "link",
            },
            // },
            hide: true,
            span: 24,
            showColumn: false, //不出现显隐面板中
          },
        ],
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),

    // 优化后的权限计算属性
    permissionList () {
      const prefix = "notice";
      return Object.entries({
        addBtn: "_add",
        viewBtn: "_view",
        editBtn: "_edit",
        delBtn: "_delete",
        publishBtn: "_publish",
        cancelBtn: "_cancel",
      }).reduce((acc, [key, suffix]) => {
        acc[key] = this.vaildData(this.permission[`${prefix}${suffix}`], false);
        return acc;
      }, {});
    },
    adminFlag () {
      // 超管administrator和租户管理员admin为系统管理员
      return this.userInfo.role_name.indexOf('administrator') > -1 || this.userInfo.role_name.indexOf('admin') > -1;
    },
    ids () {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created () {
    console.log(this.permission);
  },
  mounted () {
    this.getPersonalDictionary()
  },
  methods: {
    // 发布
    async updateStatus (status) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (
        this.selectionList.findIndex((item) => item.status === 1) !== -1 &&
        status === 1
      ) {
        this.$message.warning("存在已发布的数据,已发布的数据无法再次发布");
        return;
      }
      if (
        this.selectionList.findIndex((item) => item.status === 0) !== -1 &&
        status === 0
      ) {
        this.$message.warning("存在未发布的数据,未发布的数据无法取消发布");
        return;
      }
      let res = null;
      if (status) {
        res = await funList.release({ ids: this.ids });
      } else {
        res = await funList.unRelease({ ids: this.ids });
      }
      if (res && res.data && res.data.success) {
        this.$message.success(`成功${status === 1 ? "发布" : "取消发布"}`);
        this.onLoad(this.page, this.query);
      }
    },
    //Uview预览 start
    showUview () {
      this.uviewVisible = true;
      this.$refs.uview.sendMessage(this.form.title, this.form.content);
    },
    changeUviewVisible () {
      this.uviewVisible = false;
    },

    // 字典修改
    getPersonalDictionary () {
      // 消息类型
      getDictionary({ code: 'noticeType' }).then(res => {
        let temp = []
        if (this.adminFlag) {
          for (let i = 0; i < res.data.data.length; i++) {
            if (res.data.data[i].dictValue !== "疫情防控") {
              temp.push(res.data.data[i])
            }
          }
        } else {
          for (let i = 0; i < res.data.data.length; i++) {
            if (res.data.data[i].dictValue == "疫情防控" || res.data.data[i].dicValue == "其他") {
              temp.push(res.data.data[i])
            }
          }
        }
        const columnAdd = this.findObject(this.option.column, "noticeTypeAdd");
        columnAdd.dicData = temp;
        const column = this.findObject(this.option.column, "noticeType");
        column.dicData = res.data.data;
      })
      const platform = this.findObject(this.option.column, "platform");
      const position = this.findObject(this.option.column, "position");

      if (this.adminFlag) {
        platform.dicData = [
          { label: '小程序', value: '1' },
          { label: 'WEB', value: '2' }
        ];
        position.dicData = [
          { label: '首页弹窗', value: '1' },
          { label: '消息列表', value: '2' }
        ];
      } else {
        platform.dicData = [
          { label: '小程序', value: '1' },
        ];
        position.dicData = [
          { label: '首页轮播', value: '1' },
          { label: '消息列表', value: '2' },
        ];
        // this.option.column.splice(5, 2)
      }
    },
    // 详情弹窗
    preview (row) {
      this.form = row;
      this.queryDetail();
    },
    // 新增
    rowSave (row, done, loading) {
      if (!this.form.attachList) {
        this.form.attachList = [];
      }
      let submitData = {
        title: row.title,
        userType: this.adminFlag ? 0 : 1,
        noticeType: row.noticeTypeAdd,
        platform: row.platform,
        position: row.position,
        target: row.target,
        content: row.content,
        deptIds: row.deptIds.join(","),
      };
      funList.save(submitData).then(
        async () => {
          this.$message.success(`成功新增`);
          await this.onLoad(this.page, this.query);
          done();
        },
        () => {
          loading();
        }
      );
    },
    // 修改
    rowUpdate (row, index, done, loading) {
      if (!this.form.attachList) {
        this.form.attachList = [];
      }
      let submitData = {
        id: row.id,
        title: row.title,
        userType: this.adminFlag ? 0 : 1,
        noticeType: row.noticeTypeAdd,
        platform: row.platform,
        position: row.position,
        target: row.target,
        content: row.content,
        deptIds: row.deptIds.join(","),
      };
      funList.update(submitData).then(
        async () => {
          this.$message.success(`成功修改`);
          await this.onLoad(this.page, this.query);
          done();
        },
        () => {
          loading();
        }
      );
    },
    // 删除
    rowDel (row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funList.remove({
            ids: row.id,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // 重置
    searchReset () {
      this.query = {};
      this.onLoad(this.page);
    },
    // 搜索
    searchChange (params, done) {
      this.page.currentPage = 1;
      this.query = params;
      this.onLoad(this.page, params);
      done();
    },
    // 勾选
    selectionChange (list) {
      this.selectionList = list;
    },
    // 重置勾选
    selectionClear () {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    // 多选删除
    handleDelete () {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.selectionList.findIndex((item) => item.status === 1) !== -1) {
        this.$message.warning("存在已发布的数据,已发布的数据无法删除");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funList.remove({
            ids: this.ids,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    // 查看预览
    queryDetail () {
      funList.detail({
        id: this.form.id,
      }).then((res) => {
        this.form = res.data.data;
        this.showUview();
      });
    },
    // 打开前回调
    async beforeOpen (done, type) {
      if (type === "add") {
        this.form = {};
        done();
        return;
      }
      if (["edit", "view"].includes(type)) {
        funList.detail({
          id: this.form.id,
        }).then((res) => {
          this.form = res.data.data;
          this.form.noticeTypeAdd = res.data.data.noticeType
          done();
        });
      }
    },

    // 当前页切换
    currentChange (currentPage) {
      this.page.currentPage = currentPage;
    },
    // 页面显示条数切换
    sizeChange (pageSize) {
      this.page.pageSize = pageSize;
    },
    // 刷新
    refreshChange () {
      this.onLoad(this.page, this.query);
    },
    // 首次加载
    async onLoad (page, params = {}) {
      this.loading = true;
      let query = {
        ...params,
        current: this.page.currentPage,
        size: this.page.pageSize,
      };
      if (query.updateTime) {
        if (Array.isArray(query.updateTime)) {
          query.begin = query.updateTime[0];
          query.end = query.updateTime[1];
        }
        delete query.updateTime;
      }
      // this.type !== "全部" ? (query.type = this.type) : null;
      let res = await funList.getList(query);
      if (res && res.data && res.data.success) {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        if (this.page.currentPage > 1 && this.page.currentPage > data.pages) {
          this.page.currentPage = data.pages
          this.onLoad()
        }
        this.selectionClear();
      }
    },
  },
};
</script>
<style scoped lang="scss">
.img-tiny {
  height: 100px;
  width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(245, 247, 250);
}

.btn-group-container {
  display: flex;
}
</style>

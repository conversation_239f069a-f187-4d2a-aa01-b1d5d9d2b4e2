.town-right-on-content {
    position: absolute;
    width: 100%;
    height: 21vh;

    .header {
        position: absolute;
        width: 100%;
        white-space: nowrap;
        top: 2vh;
        left: 50%;
        transform: translate(-50%, 0);

        .left-decorate {
            width: 34%;
        }

        .title {
            letter-spacing: 3px;
            vertical-align: top;
            margin: 0 5%;
            color: #597cff;
            font-size: 2vh;
        }

        .right-decorate {
            width: 34%;
            transform: rotateY(180deg);
        }
    }

    .content {
        position: absolute;
        float: left;
        width: 100%;
        margin-top: 7vh;

        .deviceSelect {
            position: absolute;
            width: 30%;
            z-index: 1;
            top: -2vh;
            right: 1vw;
        }

        .open-item {
            display: block;
            position: fixed;
            width: 99.8%;
            height: 94.8vh;
            left: 0.06%;
            top: 0;
            background: #374d9c;
            z-index: 10;
            background-image: url("/img/bg/online-bg.png");
            background-size: 100% 100%;
        }

        .close-item {
            display: flex;
            border: 1px solid #374d9c;
            width:23vw;
            height:15vh;
            position:absolute;
            top:-2vh;
            // background:#363C48;
            background-image: url("/img/bg/online-bg.png");
            background-size: 100% 100%;
        }

        .close-item:hover {
            border: 1px solid rgba(236, 163, 4, 0.815);
            cursor: pointer;
        }

        .open-icon {
            display: block;
            position: absolute;
            width: 1.4vw;
            z-index: 1;
            top: -2vh;
            left: 0.4vw;
        }

        .destroy-icon {
            display: block;
            position: absolute;
            width: 1.2vw;
            z-index: 1;
            top: -2vh;
            left: 2vw;
        }

        .stop-icon {
            display: block;
            position: absolute;
            width: 1.2vw;
            z-index: 1;
            bottom: 0;
            left: 0.5vw;
        }

        .refresh-icon {
            display: block;
            position: absolute;
            width: 1.2vw;
            z-index: 1;
            bottom: 0;
            right: 1vw;
        }

        .right-open-bottom {
            display: block;
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 5vh;
            background-color: #09122e;
            z-index: 10;

            .close-button {
                position: absolute;
                cursor: pointer;
                top: 5px;
                right: 10px;
            }
        }

        .right-close-bottom {
            display: none;
        }

    }
}
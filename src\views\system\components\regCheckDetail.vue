<!--
 * @Author: chenn123
 * @Date: 2021-01-06
 * @LastEditors: chenn123
 * @LastEditTime: 2021-01-06
 * @Description: 注册审核详情页
-->
<template>
    <el-dialog
            :fullscreen="isFullscreen"
            visible="true"
            append-to-body="true"
            :close-on-click-modal="true"
            top="100px"
            width="60%"
            @close="close()"
    >
        <div slot="title" class="header">
            <div class="avue-crud__dialog__header">
                <span class="el-dialog__title">{{ title }}</span>
                <div class="avue-crud__dialog__menu">
                    <i
                            @click="isFullScreen"
                            class="el-dialog__close el-icon-full-screen"
                    ></i>
                </div>
            </div>
            <button type="button" aria-label="Close" class="el-dialog__headerbtn">
                <i class="el-dialog__close el-icon el-icon-close"></i>
            </button>
        </div>
        <avue-form ref="form" v-model="detail" :option="option">
            <template slot="realName" slot-scope="scope">
                <span>{{ detail.realName }}</span>
            </template>
            <template slot="nikeName" slot-scope="scope">
                <span>{{ detail.nikeName }}</span>
            </template>
            <template slot="mobile" slot-scope="scope">
                <span>{{ detail.mobile }}</span>
            </template>
            <template slot="idcard" slot-scope="scope">
                <span>{{ detail.idcard }}</span>
            </template>
            <template slot="houseNumber" slot-scope="scope">
                <span>{{ detail.houseNumber }}</span>
            </template>
            <template slot="email" slot-scope="scope">
                <span>{{ detail.email }}</span>
            </template>
            <template slot="detailAddr" slot-scope="scope">
                <span>{{ detail.detailAddr }}</span>
            </template>
            <template slot="createTime" slot-scope="scope">
                <span>{{ detail.createTime }}</span>
            </template>
            <template slot="status" slot-scope="scope">
                <span>{{ detail.status===1?'通过':'不通过' }}</span>
            </template>
            <template slot="reviewTime" slot-scope="scope">
                <span>{{ detail.reviewTime }}</span>
            </template>
            <template slot="reviewUser" slot-scope="scope">
                <span>{{ detail.reviewUser }}</span>
            </template>
            <template slot="reviewComments" slot-scope="scope">
                <span>{{ detail.reviewComments }}</span>
            </template>
        </avue-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="close()">关 闭</el-button>
        </div>
    </el-dialog>
</template>
<script>
  import {mapGetters} from 'vuex';

  export default {
    name: "Deatil",
    props: {
      detail: {
        type: Object,
        required: false
      }
    },
    data() {
      return {
        title: '查看',
        obj: {},
        mapLoading: true,
        isFullscreen: false,
        option: {
          column: [
            {
              label: "姓名",
              prop: "realName",
              span: 12
            },
            {
              label: "用户昵称",
              prop: "nikeName",
              span: 12
            },
            {
              label: "手机号码",
              prop: "mobile",
              span: 12
            },
            {
              label: "身份证号",
              prop: "idcard",
              span: 12
            },
            {
              label: "户号",
              prop: "houseNumber",
              span: 12
            },
            {
              label: "电子邮箱",
              prop: "email",
              span: 12
            },
            {
              label: "详细地址",
              prop: "detailAddr",
              span: 24
            },
            {
              label: "申请注册时间",
              prop: "createTime",
              span: 24
            },
            {
              label: "审核结果",
              prop: "status",
              span: 12,
              control: (val) => {
                if (val == 0) {
                  return {
                    status: {display: false},
                    reviewTime: {display: false},
                    reviewUser: {display: false},
                    reviewComments: {display: false}
                  }
                } else {
                  return {
                    status: {display: true},
                    reviewTime: {display: true},
                    reviewUser: {display: true},
                    reviewComments: {display: true}
                  }
                }
              }
            },
            {
              label: "审核时间",
              prop: "reviewTime",
              span: 12,
              display: false
            },
            {
              label: "审核人",
              prop: "reviewUser",
              span: 12,
              display: false
            },
            {
              label: "审核意见",
              prop: "reviewComments",
              span: 24,
              display: false
            }
          ],
          labelPosition: "right",
          labelSuffix: "：",
          labelWidth: 150,
          menuBtn: true,
          submitBtn: false,
          emptyBtn: false,
          menuPosition: "center",
          tabs: false,
          detail: false
        }
      };
    },
    computed: {
      ...mapGetters(['userInfo']),
    },
    methods: {
      close() {
        this.$parent.isShowDetail(false, false);
      },
      isFullScreen() {
        this.isFullscreen = !this.isFullscreen
      }
    }
  };
</script>
<style lang="scss" scoped>
    .dialogStyle {
        padding: 16px 24px;
        min-height: 20px;
        border-bottom: 1px solid #f0f0f0;
    }

    .title {
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
        word-wrap: break-word;
    }

    .header {
        padding: 0px 10px;
    }
</style>
.el-dropdown-menu__item {
  font-size: 12px !important;
  line-height: 28px !important;
}

//.el-card.is-always-shadow {
//  box-shadow: none;
//  border: none !important;
//}

.el-scrollbar__view {
  height: 100%;
}

.el-menu--horizontal {
  border-bottom: none !important;
}

.el-menu {
  border-right: none !important;
}

.el-menu--display,
.el-menu--display + .el-submenu__icon-arrow {
  display: none;
}


.el-message__icon,
.el-message__content {
  display: inline-block;
}

.el-date-editor .el-range-input,
.el-date-editor .el-range-separator {
  height: auto;
  overflow: hidden;
}

.el-dialog__wrapper {
  z-index: 2048;
}

.el-col {
  margin-bottom: 8px;
}

.el-main {
  padding: 0 !important;
}

.el-dropdown-menu__item--divided:before, .el-menu, .el-menu--horizontal > .el-menu-item:not(.is-disabled):focus, .el-menu--horizontal > .el-menu-item:not(.is-disabled):hover, .el-menu--horizontal > .el-submenu .el-submenu__title:hover {
  background-color: transparent;
}


.el-dropdown-menu__item--divided:before, .el-menu, .el-menu--horizontal > .el-menu-item:not(.is-disabled):focus, .el-menu--horizontal > .el-menu-item:not(.is-disabled):hover, .el-menu--horizontal > .el-submenu .el-submenu__title:hover {
  background-color: transparent !important;
}

.el-card__header {
  padding: 6px 18px !important;
}

.el-card__body {
  //padding: 16px !important;
}

.el-divider--horizontal {
  margin: 12px 0 !important;
}

<!--
 * @Author: linzq33
 * @Date: 2025-08-04
 * @LastEditors: 
 * @LastEditTime: 
 * @Description: 党组织管理
-->

<template>
  <el-row ref="test">
    <el-col :span="5" v-loading="treeLoading">
      <div class="box">
        <el-scrollbar>
          <basic-container>
            <avue-tree :option="treeOption" :data="treeData" @node-click="nodeClick" show-checkbox>
            </avue-tree>
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :span="19">
      <basic-container>
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="党员信息" name="first" />
          <el-tab-pane label="基本信息" name="second" />
          <el-tab-pane label="换届信息" name="third" />
          <el-tab-pane label="荣誉信息" name="fourth" />
        </el-tabs>
        <!-- 党员信息 -->
        <List v-if="activeName == 'first'" :dept="deptId" />
        <!-- 基本信息 -->
        <Base v-else-if="activeName == 'second'" :dept="deptId" />
        <!-- 换届信息 -->
        <Change v-else-if="activeName == 'third'" :dept="deptId" />
        <!-- 荣誉信息 -->
        <Honor v-else-if="activeName == 'fourth'" :dept="deptId" />
      </basic-container>
    </el-col>
  </el-row>

</template>

<script>
import { getList, getDetail, add, update, remove } from '@/api/governance/census'
import { mapGetters } from 'vuex'
import List from './components/list.vue'
import Change from './components/change.vue'
import Base from './components/base.vue'
import Honor from './components/honor.vue'
import { getDeptTree } from "@/api/infoRelease/partyLead"

export default {
  components: {
    List, Base, Change, Honor
  },
  data () {
    return {
      activeName: 'first',
      deptId: '',
      treeLoading: false,
      treeData: [],
      treeOption: {
        addBtn: false,
        menu: false,
        size: 'small',
        props: {
          labelText: '标题',
          label: 'title',
          value: 'value',
          children: 'children'
        }
      },
    }
  },
  computed: {
    ...mapGetters(['permission', 'userInfo']),
  },
  created () {
    this.treeLoading = true
    this.initTreeData(this.userInfo.tenant_id)
  },
  methods: {
    handleClick () {

    },
    nodeClick (data) {
      this.deptId = data.id
    },
    async initTreeData (tenantId) {
      this.treeData = (await getDeptTree(tenantId)).data.data
      this.treeLoading = false
    },
  }
}
</script>

<style lang="scss" scoped></style>

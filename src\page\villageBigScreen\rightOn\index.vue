<!--
 * @Description: 右上-劳务用工
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2022-01-04 11:28:15
-->
<template>

  <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column; padding: 1vh 1vw; padding-left: 0.5vw; box-sizing: border-box;">
    <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column;">
      <img class="img-common" src="/img/bigScreen/modulebg.png" mode="scaleToFill" />

      <div style="display: flex; flex-direction: row; padding-right: 1vw;">
        <div class="head-idx" style="position: relative; width: 3vh; height: 3vh; font-weight: bold;">
          <img class="img-common" src="/img/bigScreen/idx.png" mode="scaleToFill" />
          04
        </div>
        <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center;">
          <img class="img-common" src="/img/bigScreen/subTitlebg.png" mode="scaleToFill" />
          <div class="head-title" style="margin-left: 1vw;">村务信息</div>
        </div>
      </div>
      <div style="display: flex; flex-direction: row; justify-content: space-between; padding: 0 1vw; margin-top: 2vh; margin-bottom: 1vh;">
        <div class="btn" @click="onTap(1)">
          <img class="img-common" :src="selectedType==1?'/img/bigScreen/btnSelected.png':'/img/bigScreen/btnNormal.png'" mode="scaleToFill" />
          <div class="head-idx" style="font-size: 1.2vh; font-weight: Medium;">镇收入</div>
        </div>
        <div class="btn" @click="onTap(2)">
          <img class="img-common" :src="selectedType==2?'/img/bigScreen/btnSelected.png':'/img/bigScreen/btnNormal.png'" mode="scaleToFill" />
          <div class="head-idx" style="font-size: 1.2vh; font-weight: Medium;">三务公开</div>
        </div>
        <div class="btn" @click="onTap(3)">
          <img class="img-common" :src="selectedType==3?'/img/bigScreen/btnSelected.png':'/img/bigScreen/btnNormal.png'" mode="scaleToFill" />
          <div class="head-idx" style="font-size: 1.2vh; font-weight: Medium;">通知公告</div>
        </div>
      </div>
      <div style="display: flex; flex-direction: row; height: 2.96vh; padding: 0 1vw;">
        <div style="position: relative; display: flex; flex: 1;">
          <img class="img-common" src="/img/bigScreen/titleLeft.png" mode="scaleToFill" />
        </div>
        <div style="position: relative; display: flex; flex: 1;">
          <img class="img-common" src="/img/bigScreen/titleRight.png" mode="scaleToFill" />
        </div>
      </div>
        <div class="content">
          <div
            id="echart-labor"
            :style="{ width: '100%', height: '15.2vh',minWidth:'310px'}"
            @mouseenter="handlePause1" @mouseleave="handleAutoLoop1"
          ></div>
        </div>
      </div>
  </div>
</template>
<script>
  import * as echarts from "echarts/lib/echarts";
  import "echarts/lib/chart/bar";
  import "echarts/lib/chart/pie";
  import "echarts/lib/component/tooltip";
  import "echarts/lib/component/title";
  import "echarts/lib/component/legend";
  // import {
  //   GridComponent,
  //   ToolboxComponent,
  //   GraphicComponent,
  // } from "echarts/components";
  // echarts.use([GridComponent, ToolboxComponent, GraphicComponent]);
import { debounce } from "lodash";
import { getLabor } from '@/api/screen/screen'
  export default {
  data() {
    return {
      selectedType: 1,
      myChart:null,
      timer:null,
      index:0,
      myChartData:[],
       optionLabor:{
          title: {
            show:false,
            text: '用工信息统计',
            x: '0'
          },
          // tooltip: {
          //     trigger: 'item',
          // },
          tooltip: {
            trigger: "item",
            axisPointer: {
              type: "shadow",
              shadowStyle: {
                color: "rgba(0,46, 115, 0.3)",
              },
            },
            formatter: function (params) {
              var str = "";
              str += params.name +": " +params.value +" 人";
              return str;
            },
            textStyle: {
              align: "center",
              color: "#5cc1ff",
              fontSize: 14,
            },
            backgroundColor: "rgba(15, 52, 135, 0.5)",
            borderWidth: "1",
            borderColor: "#5cc1ff",
            extraCssText: "box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);",
          },
          color:['#2A4379','#0DECCF','#2D9AF8','#5470C6','#EE6666','#FAC858','#91CC75'],
          series: [
              {
                  name: '用工信息',
                  type: 'pie',
                  radius: ['45%', '60%'],
                  center: ['25%', '50%'],
                  avoidLabelOverlap: false,
                  itemStyle: {
                      // borderRadius: 4,
                      // borderColor: '#fff',
                      shadowBlur: 5,
                      shadowColor: '#1ABBF5',
                      borderWidth: 8,
                      borderRadius: 3,
                  },

                  label: {
                      show: false,
                      position: 'center',
                  },
                  emphasis: {
                      label: {
                          show: true,
                          fontSize: '12',
                          color:"white",
                      }
                  },
                  labelLine: {
                      show: false
                  },
                  data: [
                      {value: 0, name: '企业用工'},
                      {value: 0, name: '政府用工'},
                      {value: 0, name: '个人招工'}
                  ]
              }
          ],
           legend: {
              orient: 'vertical', // 布局方式，默认为水平布局，可选为：'horizontal' ¦ 'vertical'
              left:'50%',
              y: 'center',
              itemWidth: 25,
              itemHeight: 15,
               textStyle:{
                rich:{
                    a:{
                        fontSize:14,
                        color:"#fff",
                        verticalAlign:'top',
                        align:'left',
                        width:70,
                        padding:[0,0,0,5]
                    },
                    b:{
                        fontSize:14,
                        color:"#fff",
                        verticalAlign:'top',
                        align:'left',
                        width:90,
                        padding:[0,0,0,0]
                    },
                    // c:{
                    //     fontSize:18,
                    //     align:'left',
                    //     width:120
                    // }
                }
              },
              itemGap:20
            },
          },
    };
  },

  created() {
     setTimeout(()=>{
        this.handleAutoLoop1();
        this.setResize();
     },500)
  },
  mounted() {
      this.getLabor()
  },
  destroyed(){
    if (this.timer) {
      clearInterval(this.timer)
    }
    let self = this;
    window.removeEventListener("resize", function(){
      if(self.myChart){self.myChart = null}
    });
  },
  computed: {

  },
  methods: {
     async getLabor(){
        const result = await new Promise((resolve)=>{
          getLabor().then(res => {
             if(res && res.data.code === 200) {
               resolve(res.data.data);
             }
           })
        });
        this.$nextTick(() => {
           if(result.length > 0){
             this.optionLabor.series[0].data =result;
             this.myChartData = result
          }
          this.renderEcharts(result);
        })
      },
      renderEcharts() {
        this.optionLabor.tooltip.formatter = (params) => {
            var percent = 0;
            var total = 0;
            for (var i = 0; i < this.myChartData.length; i++) {
              total += parseFloat(this.myChartData[i].value);
            }
            if(total === 0){
              percent= 0;
            }else{
              percent = ((params.value / total) * 100).toFixed(0);
            }
            var str = "";
            str += params.name +": " +params.value +"<br>"+"占比："+percent+"%";
            return str;
          }
          this.optionLabor.legend.formatter = (name) => {
              // var total = 0;
              var target;
              let data =this.optionLabor.series[0].data;
              for (var i = 0, l = data.length; i < l; i++) {
              // total += data[i].value;
              if (data[i].name == name) {
                  target = data[i].value;
                  }
              }
              var arr = [
                  '{a|'+name+'}',
                  '{b|'+target+'}'
                  // '{c|'+(total === 0?0:((target/total)*100).toFixed(2))+'%}',
              ]
              return arr.join('')
          }
          this.optionLabor.series[0].emphasis.label.formatter = (params) => {
              var percent = 0;
              var total = 0;
              for (var i = 0; i < this.myChartData.length; i++) {
                total += parseFloat(this.myChartData[i].value);
              }
              if(total === 0){
                percent= 0;
              }else{
                percent = ((params.value / total) * 100).toFixed(0);
              }
              var str = "";
              str += params.name +": " +params.value +"\n\n"+"占比："+percent+"%";
              return str;
          }
          //将echarts存储起来，减少再次获取dom操作
          this.myChart = echarts.init(document.getElementById('echart-labor'))
          this.myChart.setOption(this.optionLabor);
    },
    setResize() {
      let self = this;
      window.addEventListener('resize',debounce(function () {
        if(self.myChart) self.myChart.resize();
      },200))
    },

    //开始自动轮播函数
    handleAutoLoop1 () {
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.timer = setInterval(() => {
        if (this.index > 0) {
          this.myChart.dispatchAction({
            type: "downplay",
            seriesIndex: 0,
            dataIndex: this.index - 1
          })
        }
        this.myChart.dispatchAction({ type: "highlight", seriesIndex: 0, dataIndex: this.index })
        this.index += 1
        if (this.index >= this.myChartData.length) {
          this.index = 0
          setTimeout(() => {
            this.myChart.dispatchAction({
              type: "downplay",
              seriesIndex: 0,
              dataIndex: this.myChartData.length - 1
            })
          }, 5000)
        }
      }, 5000)
    },
    //停止自动轮播函数
    handlePause1 () {
      clearInterval(this.timer)
      this.myChart.dispatchAction({
        type: "downplay",
        seriesIndex: 0,
        dataIndex: this.index - 1
      })
    },
    onTap(type) {
      this.selectedType = type;
    }
  },

};
</script>

<style lang="scss" scoped>
.img-common {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.head-idx {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 1.7vh;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
}

.head-title {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 1.5vh;
  font-style: normal;
  font-weight: Bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
}

.btn {
  position: relative;
  width: 6vw;
  height: 2.2vh;
  cursor: pointer;
}
</style>

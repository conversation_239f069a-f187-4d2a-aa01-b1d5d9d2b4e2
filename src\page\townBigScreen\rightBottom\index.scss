.town-right-bottom-content {
    position: absolute;
    width: 100%;
    height: 25vh;

    .right-bottom-header {
        position: absolute;
        width: 100%;
        white-space: nowrap;
        top: 3vh;

        .left-decorate {
            width: 35%;
        }

        .title {
            letter-spacing: 2px;
            vertical-align: top;
            margin: 0 5%;
            color: #597cff;
            font-size: 2vh;
        }

        .right-decorate {
            width: 35%;
            transform: rotateY(180deg);
        }
    }

    .content {
        position: absolute;
        width: 100%;
        margin-top: 7vh;

        .content-value {
            // margin-top: 1vh;
            height: 30vh;
            overflow: hidden;

            .header-item {
                text-align: center;
                font-size: 13px;
                height: 35px;
                color: white;
            }

            .rows {
                .row-item {
                    .ceil {
                        text-align: center;
                    }
                }
            }
        }
    }
}
<!--
 * @Description: 右下-试点参与度
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: lins14
 * @LastEditTime: 2022-03-18 16:28:19
-->
<template>
  <div class="province-right-bottom-content">
    <div class="header">
      <span
        class="title-nation"
        :style="this.flag === 0 ? 'color:#5BFFDC' : 'color:#fff'"
        @click="changeFlag(0)"
        >国家试点参与度</span
      >
      <span
        class="title-pilot"
        :style="this.flag === 1 ? 'color:#5BFFDC' : 'color:#fff'"
        @click="changeFlag(1)"
        >省级试点参与度</span
      >
      <img src="/img/privinceScreen/titleDirection.png" class="title-direction" alt=""/>
      <img src="/img/privinceScreen/line.png" class="left-on-line" alt="" />
    </div>
    <div class="content">
      <div class="content-value">
        <dv-loading v-if="loading">Loading...</dv-loading>
        <dv-scroll-board v-else :config="this.config" ref="scrollBoard" />
      </div>
    </div>
  </div>
</template>
<script>
import "./index.scss";
import { getNationPilot } from "@/api/privinceScreen/privinceScreen";
export default {
  data() {
    return {
      flag: 0,
      loading: true,
      config: {
        header: [
          "<span>地市</span>",
          "<span>试点数量</span>",
          "<span>已部署数</span>",
          "<span>参与度</span>",
        ],
        rowNum: 5,
        headerBGC: "",
        headerHeight: 30,
        columnWidth: [160, 150, 150, 150],
        evenRowBGC: "#11256B",
        oddRowBGC: "",
        data: [],
      },
    };
  },
  created() {
    this.getNationPilot(0);
  },
  methods: {
    async getNationPilot() {
      this.loading = true;
      const data = await new Promise((resolve) => {
        getNationPilot(this.flag).then((res) => {
          if (res && res.data.code === 200) {
            resolve(res.data.data);
          }
        });
      });
      let countfinancialList = [];
      for (let i = 0; i < data.length; i++) {
        let editData = [];
        editData[0] =
          "<span style='color:fff' title='" +
          data[i].name +
          "'>" +
          data[i].name +
          "</span>";
        editData[1] =
          "<span style='color:fff' title='" +
          data[i].pilotNum +
          "'>" +
          data[i].pilotNum +
          "</span>";
        editData[2] =
          "<span style='color:fff' title='" +
          data[i].deployed +
          "'>" +
          data[i].deployed +
          "</span>";
        editData[3] =
          "<span style='color:fff' title='" +
          data[i].participation +
          "%'>" +
          data[i].participation +
          "%</span>";
        countfinancialList[i] = editData;
      }
      this.loading = false;
      this.config = {
        header: [
          "<span>地市</span>",
          "<span>试点数量</span>",
          "<span>已部署数</span>",
          "<span>参与度</span>",
        ],
        rowNum: 5,
        headerBGC: "",
        headerHeight: 30,
        columnWidth: [160, 150, 150, 150],
        evenRowBGC: "#11256B",
        oddRowBGC: "",
        data: countfinancialList,
      };
    },
    changeFlag(flag) {
      this.flag = flag;
      this.getNationPilot();
    },
  },
};
</script>

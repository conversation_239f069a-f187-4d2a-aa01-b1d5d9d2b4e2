/**@module publicSentimentMangement
 * @Description: 民情管理
 * @Author: linzq33
 * @Date: 2025-07-18 10:14:53
 * @LastEditors: 
 * @LastEditTime: 
 */
import request from '@/router/axios'

/**
 * @func
 * @desc 根据部门分页
 * @param {number} current 当前页码
 * @param {number} size 每页数据条数
 * @param {{}} data 查询数据
 * @returns {Promise<ResponseData>}
 */
export const getList = (current, size, data, deptId) => {
  return request({
    url: '/api/admin/public-mind/page',
    method: 'get',
    params: {
      ...data,
      deptId,
      current,
      size,
    }
  })
}
/**
 * 获取详情
 * @func
 * @param {number} params - id
 * @returns {Promise<ResponseData>}
 */
export const getDetail = id => {
  return request({
    url: '/api/admin/public-mind/detail',
    method: 'get',
    params: {
      id
    }
  })
}

/**
 * 删除
 * @func
 * @param {number} params - id
 * @returns {Promise<ResponseData>}
 */
export const del = ids => {
  return request({
    url: '/api/admin/public-mind/remove',
    method: 'delete',
    params: {
      ids
    }
  })
}

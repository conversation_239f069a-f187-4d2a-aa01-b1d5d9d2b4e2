<!--
 * @Description: 轮播管理
 * @Author: lins14
 * @Date: 2021-09-24 15:42:31
 * @LastEditors: linqh21
 * @LastEditTime: 2025-03-11 16:36:34
-->
<template>
  <basic-container>
    <avue-crud
      ref="crud"
      :option="option"
      :table-loading="loading"
      :data="data"
      :page.sync="page"
      :before-open="beforeOpen"
      :before-close="beforeClose"
      :upload-preview="uploadPreview"
      :upload-before="uploadBefore"
      :upload-error="uploadError"
      v-model="form"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @search-change="searchChange"
      @search-reset="searchReset"
      @current-change="currentChange"
      @size-change="sizeChange"
      @selection-change="selectionChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template slot="menuLeft">
        <el-button type="primary" icon="el-icon-plus" size="small" plain @click.stop="$refs.crud.rowAdd()">新增</el-button>
        <el-button type="danger" size="small" icon="el-icon-delete" plain @click="deleteC(2)">批量删除</el-button>
      </template>
      <template slot-scope="{ type }" slot="menuForm">
        <el-button v-if="type == 'add'" type="primary" size="small" plain @click="$refs.crud.rowSave()">发布</el-button>
        <el-button v-if="type == 'edit'" type="primary" size="small" plain @click="$refs.crud.rowUpdate()">更新</el-button>
        <el-button v-if="type == 'edit'" type="danger" size="small" plain @click="deleteC(1)">删除</el-button>
        <el-button size="small" plain @click="$refs.crud.closeDialog()">取消</el-button>
      </template>
      <template slot="informationIdForm">
        <el-select v-model="form.informationId"
                   filterable placeholder="请选择 本站信息【仅支持党建发布、三务公开、涉农政策、乡村动态等模块发布信息内容】"
                   v-loadmore="loadMore"
                   remote="true"
                   :remote-method="searchInformation"
                   clearable
                   @clear="clearSelected">
          <el-option v-for="item in informationIdData" :key="item.id" :label="item.title" :value="item.id" @click.native="selectInformation(item)">
            <span>{{ cutLongTitle(item.title) }}</span>
            <span style="color: #e07706">[{{ item.updateTime }}]</span>
          </el-option>
        </el-select>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
/**
 * 创建者：huangwl36
 * 创建时间：2021年6月22日
 * 最后修改用户：lin14
 * 最后修改时间：2021-08-30 14:58:15
 * 模块：轮播管理
 */
import { startList, saveCar, editCar, delCar, getInformation } from '@/api/carouselMangement'
export default {
  directives: {
    loadmore: {
      bind: (el, binding) => {
        const SELECTWRAP = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap')
        SELECTWRAP.addEventListener('scroll', function() {
          if (this.scrollHeight - this.scrollTop <= this.clientHeight) {
            binding.value()
          }
        })
      }
    }
  },
  data() {
    /**
     * http https验证
     */
    var validateHttp = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('跳转地址必须填写'))
      } else {
        const p = /https:\/\/([\w.]+\/?)\S*/
        if (!p.test(value)) {
          callback(new Error('跳转地址格式不匹配'))
        }
        callback()
      }
    }
    /**
     * 纯空格验证
     */
    var validateSpace = (rule, value, callback) => {
      if (value !== '') {
        if (!value.trim()) {
          callback(new Error('不能为纯空格'))
        }
      }
      callback()
    }
    /**
     * 不包含空格验证
     */
    // var validateNoSpace = (rule, value, callback) => {
    //   if (value !== "") {
    //     if ((value + "").indexOf(" ") >= 0) {
    //       callback(new Error("不能包含空格"));
    //     }
    //   }
    //   callback();
    // };
    /**
     * 只能英文数字
     */
    var validateEngNum = (rule, value, callback) => {
      if (value !== '') {
        const p = /^[A-Za-z0-9]+$/
        if (!p.test(value)) {
          callback(new Error('只能是英文数字'))
        }
      }
      callback()
    }
    return {
      selectPageSize: 8,
      selectCurrentPage: 1,
      form: {},
      mode: '1',
      selectionList: [],
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      searchLoading: false,
      isSearch: false,
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        showHeader: true,
        border: true,
        index: true,
        selection: true,
        editBtn: true,
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        saveBtn: false,
        updateBtn: false,
        cancelBtn: false,
        menu: true,
        dialogWidth: 900,
        dialogClickModal: false,
        column: [
          {
            label: '编号',
            prop: 'id',
            slot: true,
            hide: true,
            editDisplay: false,
            addDisplay: false,
            width: 120
          },
          {
            label: '标题',
            type: 'input',
            row: true,
            span: 24,
            display: true,
            slot: true,
            maxlength: 100,
            showWordLimit: false,
            prop: 'description',
            search: true,
            required: true,
            rules: [
              { required: true, message: '标题必须填写' },
              { validator: validateSpace, trigger: ['blur', 'change'] }
            ]
          },
          {
            label: '展示界面',
            prop: 'userType',
            type: 'select',
            multiple: true,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=carouselMangement_userType',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            dataType: 'string',
            span: 24,
            display: true,
            // required: true,
            rules: [{ required: true, message: '请选择展示界面' }],
          },
          {
            label: '跳转类型',
            type: 'radio',
            prop: 'type',
            span: 24,
            display: true,
            dicUrl: '/api/blade-system/dict-biz/dictionary?code=carouselMangement_type',
            props: {
              label: 'dictValue',
              value: 'dictKey'
            },
            dataType: 'string',
            rules: [{ required: true, message: '请选择单选框组' }],
          },
          {
            type: 'number',
            label: '排序序号',
            controls: false,
            span: 12,
            display: true,
            prop: 'sort',
            minRows: 0,
            maxRows: 9999,
            step: 1,
            precision: 0,
            value: 0,
            required: true,
            rules: [
              {
                required: true,
                message: '排序序号必须填写'
              }
            ]
          },
          {
            label: '选择图片',
            prop: 'imgUrl',
            type: 'upload',
            accept: '.png, .jpg, .jpeg',
            span: 24,
            display: true,
            loadText: '图片上传中，请稍等',
            tip: '只能上传png/jpg/jpeg文件，且不超过10MB',
            propsHttp: {
              res: 'data',
              name: 'originalName',
              url: 'attachId'
            },
            action: '/api/blade-resource/oss/endpoint/put-file-attach',
            required: true,
            hide: true
          },
          {
            label: '已上传图片',
            prop: 'picPath',
            type: 'upload',
            listType: 'picture-img',
            span: 24,
            display: true,
            disabled: true,
            hide: true
          },
          {
            type: 'input',
            label: '跳转地址',
            span: 24,
            display: true,
            prop: 'urlLink',
            maxlength: 200,
            showWordLimit: true,
            tip: '跳转地址中必须包含 https://',
            tipPlacement: 'top',
            required: true,
            rules: [
              { required: true, message: '跳转地址必须填写' },
              { validator: validateHttp, trigger: ['blur', 'chnage'] }
            ],
            hide: true
          },
          {
            type: 'input',
            label: '小程序ID',
            span: 24,
            display: true,
            prop: 'miniProgramId',
            rules: [
              { required: true, message: '小程序ID必须填写' },
              { validator: validateEngNum, trigger: ['blur', 'change'] }
            ],
            showWordLimit: true,
            maxlength: 50,
            required: true,
            hide: true
          },
          {
            label: '本站信息',
            type: 'select',
            span: 24,
            display: true,
            prop: 'informationId',
            props: {
              label: 'title',
              value: 'id'
            },
            cascaderItem: [],
            rules: [{ required: true, message: '本站信息必须选择' }],

            hide: true,
          },
          {
            label: '时间',
            prop: 'createTime',
            addDisplay: false,
            editDisplay: false
          }
        ]
      },
      data: [],
      infoAllData: [],
      searchAllData: [],
    }
  },
  computed: {
    informationIdData() {
      let result = [];
      if(this.isSearch) {
        result = this.searchAllData
      } else {
        result = this.infoAllData.slice(0, this.selectCurrentPage * this.selectPageSize)
      }
      // return this.infoAllData.slice(0, this.selectCurrentPage * this.selectPageSize)
      return result
    },
    ids() {
      const ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    }
  },
  watch: {
    // mode() {
    //   this.onLoad(this.page)
    // },
    'form.type'(newV) {
      console.log(newV,"数据变化")
      var urlLink = this.findObject(this.option.column, 'urlLink')
      var miniProgramId = this.findObject(this.option.column, 'miniProgramId')
      var informationId = this.findObject(this.option.column, 'informationId')
      if (newV === '0') {
        urlLink.display = false
        miniProgramId.display = false
        informationId.display = false
      } else if (newV === '1') {
        urlLink.display = false
        miniProgramId.display = true
        informationId.display = false
      } else if (newV === '2') {
        urlLink.display = false
        miniProgramId.display = false
        informationId.display = true
      }
    }
  },
  mounted() {
    this.getInformationIdData()
  },
  methods: {
    selectInformation(value) {
      this.form.informationType = value.type
    },
    loadMore() {
      if(this.isSearch) {
        return
      }
      this.selectCurrentPage++
    },
    /**
     * 搜索刷新回调
     */
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    /**
     * 搜索回调
     */
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    /**
     * 多选选择回调
     */
    selectionChange(list) {
      this.selectionList = list
    },
    /**
     * 清除选中状态
     */
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    /**
     * 页数改变回调
     */
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    /**
     * 条数改变回调
     */
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    /**
     * 刷新回调
     */
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    // file, column, done
    uploadPreview(file, column, done) {
      // 终止预览
      done()
      // console.log(file, column,done);
    },
    /**
     * 开启前回调,编辑广告控制已上传图片显示
     */
    beforeOpen(done, type) {
      var picPath = this.findObject(this.option.column, 'picPath')
      var imgUrl = this.findObject(this.option.column, 'imgUrl')
      var description = this.findObject(this.option.column, 'description')
      description.showWordLimit = true
      if (['add'].includes(type)) {
        picPath.display = false
        imgUrl.rules = [{ required: true, message: '图片必须上传' }]
      } else if (['edit'].includes(type)) {
        picPath.display = true
        if (this.form.attachList && this.form.attachList.length > 0) {
          this.form.picPath = this.form.attachList[0].link
          this.form.attachFileIds = this.form.attachList[0].id
        }
        imgUrl.rules = []
        // 根据跳转类型，将另外2种输入内容置空，防止出现-1的情况
        switch (this.form.type + '') {
          case '0':
            this.form.miniProgramId = ''
            this.form.informationId = ''
            break
          case '1':
            this.form.urlLink = ''
            this.form.informationId = ''
            break
          case '2':
            this.form.urlLink = ''
            this.form.miniProgramId = ''
            break
          default:
            break
        }
      }
      done()
    },
    beforeClose(done) {
      var description = this.findObject(this.option.column, 'description')
      description.showWordLimit = false
      this.selectCurrentPage = 1
      done()
    },
    /**
     * 上传前回调，控制大小类型，限制只能上传一张
     */
    uploadBefore(file, done, loading) {
      const sizeOK = file.size <= 1024 * 1024 * 10 // 10m
      const typeOK = file.type === 'image/png' || file.type === 'image/jpg' || file.type === 'image/jpeg' // 限制类型
      if (!sizeOK) {
        this.$message.warning('图片不能超过10MB')
        loading()
        return
      }
      if (!typeOK) {
        this.$message.warning('文件类型只能是png/jpg/jpeg')
        loading()
        return
      }
      if (this.form.imgUrl && this.form.imgUrl.length > 0) {
        this.$message.warning('图片仅能上传一张，请删除已上传的图片后再操作')
        loading()
      } else {
        // 如果你想修改file文件,由于上传的file是只读文件，必须复制新的file才可以修改名字，完后赋值到done函数里,如果不修改的话直接写done()即可
        // var newFile = new File([file], file.name, { type: file.type });
        done()
      }
    },
    uploadError(error) {
      this.$message.success('上传失败!' + error)
    },
    /**
     * 通用生成参数
     */
    handleParams() {
      const obj = this.form
      // 通用参数
      const p = {
        description: obj.description,
        userType: obj.userType,
        type: obj.type,
        informationType: obj.informationType,
        sort: obj.sort
      }
      switch (obj.type + '') {
        case '0': // 链接地址
          p.urlLink = obj.urlLink
          break
        case '1': // 小程序
          p.miniProgramId = obj.miniProgramId
          break
        case '2': // 本站信息
          p.informationId = obj.informationId
          break
        default:
          break
      }
      // 选择图片
      if (obj.imgUrl && obj.imgUrl.length > 0) {
        p.attachFileIds = obj.imgUrl[0].value
      } else if (obj.attachList && obj.attachList.length > 0) {
        p.attachFileIds = obj.attachList[0].id
      }

      return p
    },
    /**
     * 新增
     */
    rowSave(row, done, loading) {
      const p = this.handleParams(row)
      // return;
      // 新增
      saveCar(p)
        .then(res => {
          if (res.data.code === 200) {
            this.$message.success('操作成功')
            this.onLoad(this.page)
          } else {
            this.$message.error('操作失败!' + res.data.msg)
          }
          done()
        })
        .catch(() => {
          loading()
        })
    },
    /**
     * 更新
     */
    rowUpdate(row, done, loading) {
      console.log(1)
      const p = this.handleParams(row)
      p.id = row.id
      editCar(p)
        .then(res => {
          if (res.data.code === 200) {
            this.$message.success('操作成功')
            this.onLoad(this.page)
          } else {
            this.$message.error('操作失败!' + res.data.msg)
          }
          done()
        })
        .catch(() => {
          loading()
        })
    },
    /**
     * 删除
     */
    deleteC(n) {
      let notText = ''
      const p = {}
      if (n === 1) {
        // 单一删除
        notText = '该数据'
        p.ids = this.form.id
      } else if (n === 2) {
        // 批量删除
        notText = '选中数据'
        if (this.selectionList.length === 0) {
          this.$message.warning('请选择至少一条数据')
          return false
        }
        p.ids = this.ids
      }
      this.$confirm(`此操作将删除${notText}, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          delCar(p).then(res => {
            if (res.data.code === 200) {
              this.$message({
                type: 'success',
                message: '操作成功'
              })
              this.$refs.crud.closeDialog()
              this.onLoad(this.page)
            } else {
              this.$message.error('操作失败!' + res.data.msg)
            }
          })
        })
        .catch(() => {})
    },
    /**
     * 加载表格
     */
    onLoad(page, params = {}) {
      const query = {
        ...this.query
      }
      this.loading = true
      startList(page.currentPage, page.pageSize, Object.assign(params, query)).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
    /**
     * 获取本站信息
     */
    getInformationIdData() {
      const p = {
        size: -1
      }
      getInformation(p).then(res => {
        if (res.data.code === 200) {
          this.infoAllData = res.data.data.records
        }
      })
    },
    /**
     *
     * 搜索本站信息
     */
    searchInformation(query) {
      if(query !== '') {
        this.searchLoading = true
        this.isSearch= true
        const p = {
          size: -1,
          title: query
        }
        getInformation(p).then(res => {
          if (res.data.code === 200) {
            this.searchAllData = res.data.data.records
            this.searchLoading = false
          }
        })
      } else {
        this.isSearch = false
      }

    },
    /**
     * 截取过长title，防止展示问题
     */
    cutLongTitle(title) {
      if (!title) return
      if ((title + '').length > 30) {
        return (title + '').substring(0, 30) + '……'
      } else {
        return title
      }
    },
    /**
     * 清空本站信息选中的值
     */
    clearSelected() {
      this.isSearch = false
    }
  }
}
</script>

<style>
.none-border {
  border: 0;
  background-color: transparent !important;
}
</style>

/*
 * @Description: 省级运营大屏接口
 * @Author: chenz76
 * @Date: 2021-12-29 14:27:42
 * @LastEditors: chenz76
 * @LastEditTime: 2021-12-30 10:42:36
 */
import request from '@/router/axios';

/**
 * @description: 中上-福建省各地市部署情况（地图概览）统计接口
 * @param {*}
 * @return {*}
 */
export const getMapOverview = () => {
  return request({
    url: '/api/admin/province-screen/map-overview',
    method: 'get',
  })
}

/**
 * @description: 中上-福建省各地市部署情况（地图概览）滚动文字统计接口
 * @param {*}
 * @return {*}
 */
 export const getMapOverviewUnder = () => {
  return request({
    url: '/api/admin/province-screen/map-overview-under',
    method: 'get',
  })
}

/**
 * @description: 右下-八闽数村国家（省级）试点参与度统计接口
 * @param {flag} 国家 0 省级 1
 * @return {*}
 * @author: chenz76
 */
 export const getNationPilot = (flag) => {
  return request({
    url: '/api/admin/province-screen/nation-pilot',
    method: 'get',
    params:{
        flag
    }
  })
}

/**
 * @description: 左中-人员激活活跃统计-按月
 * @param {*}
 * @return {*}
 * @author: chenz76
 */
 export const getUserActiveForm = () => {
  return request({
    url: '/api/admin/province-screen/user-active-form',
    method: 'get',
  })
}

/**
 * @description: 右中-人员激活活跃统计-按地市
 * @param {*}
 * @return {*}
 * @author: chenz76
 */
 export const getUserEnlivenForm = () => {
  return request({
    url: '/api/admin/province-screen/user-enliven-form',
    method: 'get'
  })
}

/**
 * @description: 左下-用户渗透率(地市)统计接口 按地市
 * @param {*}
 * @return {*}
 */
 export const getUserPenetrationCityForm = () => {
  return request({
    url: '/api/admin/province-screen/user-penetration-city-form',
    method: 'get',
  })
}
/**
 * @description: 左下-用户渗透率(全省)统计接口 按月
 * @param {*}
 * @return {*}
 */
 export const getUserPenetrationProvinceForm = () => {
    return request({
      url: '/api/admin/province-screen/user-penetration-province-form',
      method: 'get',
    })
  }
  /**
 * @description: 中下-平台渗透率排行榜统计接口
 * @param {*}
 * @return {*}
 */
 export const getUserPenetrationSort = () => {
    return request({
      url: '/api/admin/province-screen/user-penetration-sort',
      method: 'get',
    })
  }
  /**
 * @description: 左上-乡村部署统计接口
 * @param {*}
 * @return {*}
 */
 export const getVillageDeployForm = () => {
    return request({
      url: '/api/admin/province-screen/village-deploy-form',
      method: 'get',
    })
  }
    /**
 * @description: 右上-用户导入统计接口
 * @param {*}
 * @return {*}
 */
 export const getUserImportForm = () => {
    return request({
      url: '/api/admin/province-screen/user-import-form',
      method: 'get',
    })
  }
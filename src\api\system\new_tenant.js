import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/api/serviceTenant/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/api/serviceTenant/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const setting = (ids, form) => {
  return request({
    url: '/api/serviceTenant/setting',
    method: 'post',
    data: {
      ...form,
      ids:ids
    },
    header:{
      'Content-Type': 'application/json'
    }
  })
}

export const tenantWebMenuIds = (deptId) => {
  return request({
    url: '/api/serviceTenant/getWebMenuIds',
    method: 'get',
    params: {
      deptId
    }
  })
}

export const setTenantWebMenuIds = (data) => {
  return request({
    url: '/api/serviceTenant/setWebMenuIds',
    method: 'post',
    data: data
  })
}
.town-center-on-content {
    position: absolute;
    width: 100%;
    height: 100%;

    .header {
        position: absolute;
        width: 100%;
        white-space: nowrap;
        top: 2vh;
        left: 50%;
        transform: translate(-50%, 0);

        .left-decorate {
            position: absolute;
            width: 40%;
            top: 1vh;
        }

        .title {
            position: absolute;
            width: 22%;
            left: 51%;
            transform: translate(-51%, 0);
            letter-spacing: 3px;
            vertical-align: top;
            overflow: hidden;
            text-align: center;
            color: #597cff;
            font-size: 2vh;
        }

        .right-decorate {
            position: absolute;
            top: 1vh;
            right: 0;
            width: 38%;
            transform: rotateY(180deg);
        }
    }

    .content {
        position: absolute;
        float: left;
        width: 100%;
        margin-top: 5vh;

        .content-carousel {
            position: absolute;
            width: 38%;

            .el-carousel__button {
                display: block;
                opacity: 0.48;
                width: 10px;
                height: 2px;
                background-color: #fff;
                border: none;
                outline: 0;
                padding: 0;
                margin: 0;
                cursor: pointer;
                -webkit-transition: 0.3s;
                transition: 0.3s;
            }
        }

        .content-desc {
            position: absolute;
            width: 55%;
            left: 42%;
            height: 18vh;

            .desc {
                color: #fff;
                font-size: 1.5vh;
                position: absolute;
                top: 0;
                left: 0.5vw;
                width: 96%;
                overflow: auto;
                box-sizing: border-box;
                height: 16vh;
            }
        }

        .content-village-info {
            position: absolute;
            font-size: 1.3vh;
            width: 100%;
            top: 20vh;
            height: 19vh;
            color: #fff;
            word-wrap: break-word;
            text-align: left;
            line-height: 2.5vh;
            overflow-x: hidden !important;
            overflow-y: hidden !important;

            .box {
                width: 30%;
                height: 8vh;
                position: absolute;
            }

            // .content-village-data{
            //     position:absolute;
            //     width:60%;
            //     height:6vh;
            //     left:2.5vw;
            //     text-align: center;
            //     font-size:15px;
            //     overflow: hidden;
            //     white-space: nowrap;
            //     text-overflow: ellipsis;
            // }
        }
    }
}
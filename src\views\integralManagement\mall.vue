<!--
 * @Description:积分商城
 * @Author: wangyy553
 * @Date: 2021-12-15 16:58:21
 * @LastEditors: wangyy553
 * @LastEditTime: 2022-01-19 15:01:57
-->
<template>
  <div>
    <basic-container>
      <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :permission="permissionList"
        :before-open="beforeOpen" v-model="search" ref="crud" @search-change="searchChange" @search-reset="searchReset"
        @selection-change="selectionChange" @on-load="onLoad" @refresh-change="refreshChange"
        @current-change="currentChange" @size-change="sizeChange">
        <template slot="stock/sale" slot-scope="scope">
          <span>{{ scope.row.stock + "/" + scope.row.saleNum }}</span>
        </template>
        <template slot="labelIds" slot-scope="scope">
          <el-tag v-for="(item, key) in labelTypes" :key="key" :type="tagType(item, scope.row.labelIds)"
            style="margin-left: 12px;">
            {{ item.dictValue }}</el-tag>
        </template>
        <template slot="status" slot-scope="scope">
          <span>{{ scope.row.status == 1 ? "活跃" : "下架" }}</span>
        </template>
        <template slot-scope="scope" slot="menu">
          <el-button v-if="permission.integral_management_mall_view" icon="el-icon-view" :size="scope.size"
            :type="scope.type" @click="openDialog('view', scope.row.id)">
            查看
          </el-button>
          <el-button v-if="
            scope.row.status !== 1 && permission.integral_management_mall_edit
          " icon="el-icon-edit" :size="scope.size" :type="scope.type" @click="openDialog('edit', scope.row.id)">
            编辑
          </el-button>
          <el-button v-if="
            scope.row.status !== 1 && permission.integral_management_mall_up
          " icon="el-icon-top" :size="scope.size" :type="scope.type" @click="upOrDown('up', scope.row.id)">
            上架
          </el-button>
          <el-button v-if="
            scope.row.status == 1 && permission.integral_management_mall_down
          " icon="el-icon-bottom" :size="scope.size" :type="scope.type" @click="upOrDown('down', scope.row.id)">
            下架
          </el-button>
          <el-button v-if="
            scope.row.status !== 1 &&
            permission.integral_management_mall_delete
          " icon="el-icon-delete" :size="scope.size" :type="scope.type" @click="delRow(scope.row.id)">
            删除
          </el-button>
          <el-button v-if="permission.integral_management_mall_sync" icon="el-icon-refresh" :size="scope.size"
            :type="scope.type" @click="openDialog('sync', scope.row.id)">
            同步
          </el-button>
          <el-button v-if="
            scope.row.status == 2 && permission.integral_management_mall_stock
          " icon="el-icon-box" :size="scope.size" :type="scope.type" @click="openDialog('stock', scope.row.id)">
            库存
          </el-button>
        </template>
        <template slot="menuLeft">
          <el-button v-if="permission.integral_management_mall_add" type="primary" icon="el-icon-plus" size="small"
            @click="openDialog('add')">新 增
          </el-button>
          <el-button v-if="permission.integral_management_mall_up" type="primary" icon="el-icon-top" size="small"
            @click="upOrDown('up')">
            上架
          </el-button>
          <el-button v-if="permission.integral_management_mall_down" type="primary" icon="el-icon-bottom" size="small"
            @click="upOrDown('down')">
            下架
          </el-button>
        </template>
      </avue-crud>
    </basic-container>
    <!-- v-if="editGoodsVisible"  -->
    <EditGoods :visible.sync="editGoodsVisible" :detail="detail" :type="dialogType" @updateTable="refreshChange" />
    <ViewGoods :visible.sync="goodsVisible" :detail="detail" />
    <SyncGoods :visible.sync="syncDataVisible" :syncOrg="detail" :id="id" @updateTable="refreshChange" />
    <StockGoods ref="stockName" :visible.sync="stockDataVisible" :id="id" @updateTable="refreshChange" />
  </div>
</template>

<script>
import EditGoods from "./components/editGoods.vue";
import ViewGoods from "./components/viewGoods.vue";
import SyncGoods from "./components/syncGoods.vue";
import StockGoods from "./components/stockGoods.vue";
import * as api from "@/api/integralManagement/mall";
import { mapGetters } from "vuex";
function validator (value, tip, callback) {
  if (value === '') {
    callback(new Error(tip));
  } else if (/^\s*$/.test(value)) {
    callback(new Error(tip));
  } else {
    callback();
  }
}

export default {
  components: { EditGoods, ViewGoods, SyncGoods, StockGoods },
  data () {
    return {
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        index: true,
        indexLabel: "序号",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: false,
        dicFlag: true,
        menu: true,
        searchLabelWidth: 110,
        // dialogWidth: 900,
        // dialogClickModal: false,
        column: [
          {
            label: "商品名称",
            prop: "name",
            search: true,
            type: "input",
            maxlength: 30,
            showWordLimit: true,
          },
          {
            label: "商品分类",
            prop: "typeId",
            hide: true,
            showColumn: false,
            search: true,
            type: "select",
            dicUrl:
              "/api/blade-system/dict/dictionary?code=integral_goods_type",
            dicMethod: "get",
            props: { label: "dictValue", value: "dictKey" },
          },
          {
            label: "商品编号",
            prop: "codeNum",
            search: true,
            type: "input",
            maxlength: 20,
            showWordLimit: true,
            // rules: [
            //   {
            //     whitespace: true,
            //     message: "请输入商品编号",
            //     trigger: ["blur", "change"],
            //   },
            // ],
          },
          {
            label: "单价（积分）",
            prop: "price",
            type: "input",
            width: 100,
          },
          {
            label: "库存/已兑换",
            prop: "stock/sale",
            slot: true,
            width: 100,
          },
          {
            label: "库存小于等于",
            prop: "stock",
            hide: true,
            showColumn: false,
            search: true,
            type: "number",
            maxRows: 999999999,
            showWordLimit: true,
            precision: 0, //精度
          },
          {
            label: "商品属性",
            prop: "label",
            search: true,
            hide: true,
            showColumn: false,
            type: "select",
            dicUrl:
              "/api/blade-system/dict/dictionary?code=integral_goods_label",
            dicMethod: "get",
            props: {
              //对应select的属性
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            label: "商品属性",
            prop: "labelIds",
            slot: true,
          },
          {
            label: "上架状态",
            prop: "status",
            slot: true,
            width: 80,
          },
          {
            label: "上架时间",
            prop: "launchTime",
            width: 140,
          },
          {
            label: "上架时间",
            prop: "launchDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            startPlaceholder: "开始日期",
            endPlaceholder: "截止日期",
            searchSpan: 8,
            search: true,
            searchRange: true,
            hide: true,
            showColumn: false,
          },
        ],
      },
      data: [],
      search: {},
      selectionList: [],
      labelTypes: [], //属性标签字典
      detail: {}, //弹窗详情参数
      id: null, //商品id
      dialogType: "", //弹窗类型
      editGoodsVisible: false, //新增、编辑弹窗
      goodsVisible: false, //查看弹窗
      syncDataVisible: false, //同步弹窗
      stockDataVisible: false, //库存
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList () {
      return {
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: false,
      };
    },
  },
  created () {
    this.getDictionary();
  },
  methods: {
    delRow (id) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        api.remove(id).then((res) => {
          if (res.data.success) {
            this.refreshChange();
            this.$message({
              type: "success",
              message: res.data.msg,
            });
          } else {
            this.$message({
              type: "warning",
              message: res.data.msg,
            });
          }
        });
      });
    },
    upOrDown (type, id) {
      var newStatus = null;
      var ids = [];
      if (type === "up") {
        newStatus = 1;
      } else {
        newStatus = 2;
      }
      if (id) {
        ids = id;
      } else if (this.selectionList.length > 0) {
        let value = this.selectionList.map((item) => {
          return item.id;
        });
        ids = value.join(",");
      }
      if (id || this.selectionList.length > 0) {
        this.loading = true;
        api.changeStatus(ids, newStatus).then((res) => {
          if (res.data.success) {
            this.$message({
              type: "success",
              message: res.data.msg,
            });
          } else {
            this.$message({
              type: "warning",
              message: res.data.msg,
            });
          }
          this.loading = false;
          this.refreshChange();
        });
      } else {
        this.$message.warning("请选择至少一条数据");
      }
    },
    getSyncOrg (id) {
      api.getSync(id).then((res) => {
        this.detail = res.data.data;
        this.id = id;
      });
    },
    /**
     * @description: 获得商品详情
     * @param {*}
     * @return {*}
     * @author: wangyy553
     */
    getGoodDetail (id) {
      api.getDetail(id).then((res) => {
        this.detail = res.data.data;
      });
    },

    getStock (id) {
      this.id = id;
      this.$nextTick(() => {
        this.$refs.stockName.initData();
      });
    },
    /**
     * @description: 请求参数并打开对应弹窗
     * @param {String} type 弹窗类型
     */
    async openDialog (type, id) {
      this.dialogType = type;
      if (type == "edit") {
        await this.getGoodDetail(id);
        this.editGoodsVisible = true;
      } else if (type == "add") {
        this.detail = {};
        this.editGoodsVisible = true;
      } else if (type === "view") {
        await this.getGoodDetail(id);
        this.goodsVisible = true;
      } else if (type === "sync") {
        await this.getSyncOrg(id);
        this.syncDataVisible = true;
      } else if (type == "stock") {
        this.getStock(id);
        this.$nextTick(() => {
          this.stockDataVisible = true;
        });
      }
    },
    selectionChange (list) {
      this.selectionList = list;
    },
    selectionClear () {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    searchReset () {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange (params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    currentChange (currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange (pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange () {
      this.onLoad(this.page, this.query);
    },
    onLoad (page, params = {}) {
      this.loading = true;
      var obj = Object.assign(params, this.query);
      if (obj.launchDate) {
        obj.startTime = obj.launchDate[0];
        obj.endTime = obj.launchDate[1];
        delete obj.launchDate;
      }
      api.getList(page.currentPage, page.pageSize, obj).then((res) => {
        this.data = res.data.data.records;
        this.page.total = res.data.data.total;
        this.loading = false;
        this.selectionClear();
      });
    },
    getDictionary () {
      api.getDictionary({ code: "integral_goods_label" }).then((res) => {
        console.log('423', res.data.data);

        this.labelTypes = res.data.data || [];
      });
    },
    tagType (dic, labels) {
      let a = labels.find((item) => item == dic.dictKey);
      if (a) {
        return "";
      } else {
        return "info";
      }
    },
  },
};
</script>

<style></style>

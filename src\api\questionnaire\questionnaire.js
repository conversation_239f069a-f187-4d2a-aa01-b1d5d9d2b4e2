/*
 * @Author: zhouwj83
 * @Date: 2021-07-07 15:58:28
 * @LastEditors: zhouwj83
 * @LastEditTime: 2021-07-07 16:22:31
 * @Description: 
 */
import request from '@/router/axios';

export const getList = (current, size, params) => {
    return request({
      url: '/api/blade-questionnaire/questionnaire',
      method: 'get',
      params: {
        ...params,
        current,
        size,
      }
    })
}

export const getDetail = (id) => {
    return request({
      url: `/api/blade-questionnaire/questionnaire/${id}`,
      method: 'get'
    })
}

export const remove = (ids) => {
    return request({
      url: `/api/blade-questionnaire/questionnaire/${ids}`,
      method: 'delete'
    })
}

export const add = (row) => {
    return request({
      url: '/api/blade-questionnaire/questionnaire',
      method: 'post',
      data: row
    })
}
  
export const update = (row) => {
    return request({
      url: '/api/blade-questionnaire/questionnaire',
      method: 'put',
      data: row
    })
}

export const getAnswerList = (questionnaireId, current, size, params) => {
  return request({
    url: '/api/blade-questionnaire/answer/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
      questionnaireId
    }
  })
}

export const getAnswerDetail = (answerId) => {
  return request({
    url: `/api/blade-questionnaire/answer`,
    method: 'get',
    params: {
      answerId
    }
  })
}

export const getAnswerStatistics = (id) => {
  return request({
    url: `/api/blade-questionnaire/answer/statistics/${id}`,
    method: 'get'
  })
}

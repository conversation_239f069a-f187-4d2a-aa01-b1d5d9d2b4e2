import request from '@/router/axios';

export const getList = (parentId, params) => {
  return request({
    url: '/api/admin/user-configure/list',
    method: 'get',
    params: {
      ...params,
      parentId
    }
  })
}

export const getOrgantree = (parentId, params) => {
  return request({
    url: '/api/admin/user-configure/deptList',
    method: 'get',
    params: {
      ...params,
      parentId
    }
  })
}
// 可配置人员列表
export const getUserList = (parentId, params) => {
  return request({
    url: '/api/admin/user-configure/userList',
    method: 'get',
    params: {
      ...params,
      parentId
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/api/admin/user-configure/remove',
    method: 'delete',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/admin/user-configure/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/api/admin/user-configure/update',
    method: 'put',
    data: row
  })
}

// export const getDept = (id) => {
//   return request({
//     url: '/api/blade-system/dept/detail',
//     method: 'get',
//     params: {
//       id,
//     }
//   })
// }

// export const getDeptTree = (tenantId) => {
//   return request({
//     url: '/api/blade-system/dept/tree',
//     method: 'get',
//     params: {
//       tenantId,
//     }
//   })
// }

// export const getDeptLazyTree = (parentId) => {
//   return request({
//     url: '/api/blade-system/dept/lazy-tree',
//     method: 'get',
//     params: {
//       parentId
//     }
//   })
// }




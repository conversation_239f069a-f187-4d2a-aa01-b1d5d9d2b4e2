.dialogDetail{
    width: 100%;
    height: 20vh;
    position: absolute;
    background:#0f1b46;
    top:-22vh;

    .content {
        width: 100%;
        height:100%;
        margin-top: 2vh;

        .closeTag{
            display: block;
            position:absolute;
            right:0;
            top:-1vh;
            width: 20px;
            height:20px;
            font-size:20px;
            cursor: pointer;
        }

        .showPanel {
            width: 100%;
            height: 15vh;
            top: 4vh;

            .box {
                width: 100%;
                height: 30%;
                margin-bottom: 5px;
                float: left;

                .box-decorate {
                    width: 100%;
                    height: 100%;
                    z-index: -1;
                    float: left;
                }

                .box-panel {
                    float: left;
                    width: 100%;
                    height: 100%;
                    z-index: 9;
                    display: flex;
                    justify-content: space-around;
                    background-image: url("../../../../public/img/screen/center-li.png");
                    background-size: cover;
                    background-position: 100% 100%;

                    .panel_left {
                        width: 50%;
                        height: 100%;
                        display: flex;
                        align-items: center;

                        span:nth-child(1) {
                            display: block;
                            width: 50%;
                            height: 100%;
                            color: #fff;
                            font-size: 14px;
                            line-height: 5vh;
                        }

                        span:nth-child(2) {
                            margin-left: 20px;
                            font-size: 18px;
                            color: #fff;
                            font-family: bold;
                        }

                        span:nth-child(3) {
                            display: block;
                            width: 20%;
                            height: 100%;
                            color: #fff;
                            font-size: 14px;
                            line-height: 5vh;
                            margin-left: 5px;
                        }
                    }

                    .panel_right {
                        width: 50%;
                        height: 100%;
                        display: flex;
                        align-items: center;

                        span:nth-child(1) {
                            display: block;
                            width: 50%;
                            height: 100%;
                            color: #fff;
                            font-size: 14px;
                            line-height: 5vh;
                            font-family: bold;
                        }

                        span:nth-child(2) {
                            font-size: 18px;
                            color: #eb9658;
                            font-family: bold;
                            padding-left: 20px;
                        }

                        span:nth-child(3) {
                            font-size: 18px;
                            color: #eb9658;
                            font-family: bold;
                        }
                    }

                    .panel_desc {
                        width: 25%;
                        height: 100%;

                        span {
                            box-sizing: border-box;
                            display: block;
                            width: 100%;
                            height: 100%;
                            color: #fff;
                            font-size: 14px;
                            line-height: 5vh;
                            // padding-right:10px;
                        }
                    }

                    .panel_count {
                        width: 75%;
                        height: 100%;

                        .count {
                            display: block;
                            width: 10%;
                            height: 100%;
                            float: left;
                            color: #fff;
                            font-size: 14px;
                            line-height: 5vh;
                            padding-left: 5%;
                        }

                        .number-decorate {
                            width: 3vh;
                            height: 3vh;
                            margin-top: 0.5vh;
                            margin-right: 5px;
                            float: left;
                            align-items: center;
                            position: relative;

                            .number {
                                position: absolute;
                                height: 100%;
                                width: 100%;
                                color: #fff;
                                font-size: 2vh;
                                font-weight: bold;
                                left: 0;
                                text-align: center;
                            }
                        }

                        .unit {
                            display: block;
                            width: 8%;
                            height: 100%;
                            float: left;
                            color: #fff;
                            font-size: 14px;
                            line-height: 5vh;
                            padding-left: 10px;
                        }

                        // span:nth-child(2){
                        //     display: block;
                        //     width:8%;
                        //     height:100%;
                        //     float: left;
                        //     color:#fff;
                        //     font-size:14px;
                        //     line-height: 5vh;
                        //     padding-left:10px;
                        // }
                    }
                }
            }
        }
    }
}
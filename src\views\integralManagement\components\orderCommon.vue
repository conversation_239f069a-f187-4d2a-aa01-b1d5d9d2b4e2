<!--
 * @Description:订单管理&获奖订单
 * @Author: wangyy553
 * @Date: 2022-01-10 17:00:10
 * @LastEditors: linqh21
 * @LastEditTime: 2024-02-04 15:46:08
-->
<template>
  <div>
    <basic-container>
      <avue-tabs :option="tabsOption" @change="handleTabChange"></avue-tabs>
      <div>
        <avue-crud
          :option="moduleOption"
          :table-loading="loading"
          :data="data"
          :page.sync="page"
          :search.sync="search"
          :permission="permissionList"
          v-model="form"
          ref="crud"
          @search-change="searchChange"
          @search-reset="searchReset"
          @current-change="currentChange"
          @size-change="sizeChange"
          @refresh-change="refreshChange"
          @on-load="onLoad"
        >
          <template slot="menuLeft">
            <el-button
              v-if="permission[`integral_management_${moduleName}_export`]"
              type="primary"
              size="small"
              :disabled="disableButton"
              @click="exportData()"
            >
              <i :class="icon"></i> 导出
            </el-button>
          </template>
          <template slot-scope="scope" slot="menu">
            <el-button
              v-if="permission[`integral_management_${moduleName}_view`]"
              icon="el-icon-view"
              :size="scope.size"
              :type="scope.type"
              @click="openDialog('view', scope.row.id)"
            >
              查看
            </el-button>
            <el-button
              v-if="(scope.row.type !== 1 && scope.row.type !== 2 && scope.row.status === 0 && scope.row.allowWriteOddNum)||(scope.row.type === 1 && scope.row.awardTypeId !== 2 &&
                permission[`integral_management_${moduleName}_delivery`] &&
                (scope.row.status !== 0 || scope.row.allowWriteOddNum) &&
                scope.row.status !== 3) ||
                // 抽奖活动
                (scope.row.type === 2 && scope.row.lotteryPrizeTypeId !== 2 &&
                permission[`integral_management_${moduleName}_delivery`] &&
                (scope.row.status !== 0 || scope.row.allowWriteOddNum) &&
                scope.row.status !== 3
                )


              "
              icon="el-icon-box"
              :size="scope.size"
              :type="scope.type"
              @click="
                openDialog('deliver', scope.row.id, scope.row.allowWriteOddNum)
              "
            >
              发货
            </el-button>
            <el-button
              v-if="(scope.row.type !== 1 && scope.row.type !== 2 && ( scope.row.status === 0 || scope.row.status === 1))||(scope.row.type === 1 && scope.row.status !== 2 &&
                scope.row.status !== 3 &&
                (scope.row.status !== 0 || scope.row.allowWriteOddNum) &&
                scope.row.awardTypeId !== 2 &&
                permission[`integral_management_${moduleName}_back`]) ||
                // 抽奖活动
                (scope.row.type === 2 && scope.row.status !== 2 &&
                scope.row.status !== 3 &&
                (scope.row.status !== 0 || scope.row.allowWriteOddNum) &&
                scope.row.lotteryPrizeTypeId !== 2 &&
                permission[`integral_management_${moduleName}_back`]
                )

              "
              icon="el-icon-document-delete"
              :size="scope.size"
              :type="scope.type"
              @click="delRow(scope.row.id)"
            >
              退单
            </el-button>
          </template>
        </avue-crud>
        <el-dialog
          :fullscreen="isFullscreen"
          :visible.sync="dialogVisible"
          append-to-body="true"
          :close-on-click-modal="false"
          top="100px"
          width="60%"
          @close="close()"
        >
          <div slot="title" class="header">
            <div class="avue-crud__dialog__header">
              <span class="el-dialog__title">查看</span>
              <div class="avue-crud__dialog__menu">
                <i
                  @click="isFullScreen"
                  class="el-dialog__close el-icon-full-screen"
                ></i>
              </div>
            </div>
            <button
              type="button"
              aria-label="Close"
              class="el-dialog__headerbtn"
            >
              <i class="el-dialog__close el-icon el-icon-close"></i>
            </button>
          </div>
          <avue-form
            v-if="dialogType === 'view'"
            ref="detailForm"
            v-model="detail"
            :option="viewOption"
          >
            <template
              :slot="item.prop"
              v-for="(item, index) in viewOption.group[0].column"
            >
              <span :key="index">{{ detail[item.prop] }} </span>
            </template>
            <template
              :slot="item1.prop"
              v-for="(item1, index1) in viewOption.group[1].column"
            >
              <span :key="index1">{{ detail[item1.prop] }} </span>
            </template>

            <template slot="goodsName">
              <span>{{ detail.goodsName }}</span>
            </template>
            <template slot="awardName">
              <span>{{ detail.awardName }}</span>
            </template>
            <template slot="goodsCn">
              <span>{{ detail.goodsCn }}</span>
            </template>
            <template slot="awardCn">
              <span>{{ detail.awardCn }}</span>
            </template>
            <template slot="attachLink">
              <span><el-image :src="detail.attachLink"> </el-image></span>
            </template>
            <template slot="totalCost">
              <span>{{ detail.totalCost }}积分</span>
            </template>
            <template slot="usageRule">
              <span v-html="detail.usageRule">
                {{ detail.usageRule }}
              </span>
            </template>
            <template slot="validTime">
              <span>{{
                detail.startValidTime + "~" + detail.endValidTime
              }}</span>
            </template>
            <template v-if="this.MODULE === 'ordersManage'" slot="reminder">
              <span>{{ detail.reminder }}</span>
            </template>
            <template v-if="this.MODULE !== 'ordersManage'" slot="reminder">
              <span v-html="detail.reminder">>{{ detail.reminder }}</span>
            </template>
          </avue-form>
        </el-dialog>
      </div>
    </basic-container>
    <OrderDeliver
      ref="deliverName"
      :visible.sync="deliverVisible"
      :id="openId"
      :MODULE="MODULE"
      :allowWriteOddNum="allowWriteOddNum"
      @updateTable="refreshChange"
    />
  </div>
</template>

<script>
import * as manageApi from "@/api/integralManagement/ordersManage";
import * as ordersApi from "@/api/integralManagement/orders";
import { mapGetters } from "vuex";
import OrderDeliver from "./orderDeliver.vue";
import { debounce } from "lodash";
import { handleDownload } from "@/util/download";
// import website from "@/config/website";

export default {
  components: { OrderDeliver },
  props: {
    MODULE: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      type: {}, //选项卡参数
      tabsOption: {
        column: [
          {
            label: "全部",
            prop: "",
          },
          {
            label: "待发货",
            prop: "0",
          },
          {
            label: "已发货",
            prop: "1",
          },
          {
            label: "已完成",
            prop: "2",
          },
          {
            label: "已失效",
            prop: "3",
          },
        ],
      },
      form: {},
      query: {},
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      data: [],
      manageOption: {
        index: true,
        indexLabel: "序号",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: false,
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: false,
        dicFlag: true,
        menu: true,
        searchLabelWidth: 90,
        column: [
          {
            label: "下单时间",
            prop: "orderDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            startPlaceholder: "开始日期",
            endPlaceholder: "截止日期",
            searchSpan: 8,
            search: true,
            searchRange: true,
            hide: true,
            showColumn: false,
            display: false,
          },
          {
            label: "下单人账号",
            prop: "account",
            display: false,
            search: true,
            searchSpan: 5,
            maxlength: 20,
            showWordLimit: true,
            width: 90,
          },
          {
            label: "商品",
            prop: "goodsName",
            display: false,
          },
          {
            label: "商品名称",
            prop: "goodsName",
            display: false,
            hide: true,
            showColumn: false,
            search: true,
            searchSpan: 5,
            maxlength: 30,
            showWordLimit: true,
          },
          {
            label: "订单编号",
            prop: "codeNum",
            display: false,
            search: true,
            searchSpan: 5,
            maxlength: 20,
            showWordLimit: true,
          },
          {
            label: "订单状态",
            prop: "statusValue",
            display: false,
            width: 70,
          },
          {
            label: "下单时间",
            prop: "createTime",
            display: false,
            width: 130,
          },
          {
            label: "物流单号",
            prop: "oddNum",
            display: false,
          },
          {
            label: "收货地址",
            prop: "address",
            display: false,
          },
        ],
      },
      prizesOption: {
        index: true,
        indexLabel: "序号",
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: false,
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: false,
        dicFlag: true,
        menu: true,
        searchLabelWidth: 90,
        column: [
          {
            label: "获奖时间",
            prop: "orderDate",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            startPlaceholder: "开始日期",
            endPlaceholder: "截止日期",
            searchSpan: 8,
            search: true,
            searchRange: true,
            hide: true,
            showColumn: false,
          },
          {
            label: "获奖人账号",
            prop: "account",
            display: false,
            search: true,
            searchSpan: 5,
            maxlength: 20,
            showWordLimit: true,
            width: 90,
          },
          {
            label: "获奖类别",
            prop: "typeName",
            display: false,
            width: 90,
          },
          {
            label: "获奖物品",
            prop: "awardName",
            display: false,
            search: true,
            searchSpan: 5,
            maxlength: 120,
            showWordLimit: true,
          },
          {
            label: "订单编号",
            prop: "codeNum",
            display: false,
            search: true,
            searchSpan: 5,
            maxlength: 20,
            showWordLimit: true,
          },
          {
            label: "订单状态",
            prop: "statusValue",
            display: false,
            width: 70,
          },
          {
            label: "获奖时间",
            prop: "createTime",
            display: false,
            width: 130,
          },
          {
            label: "物流单号",
            prop: "oddNum",
            display: false,
          },
          {
            label: "收货地址",
            prop: "address",
            display: false,
          },
        ],
      },
      detailOption: {
        labelPosition: "right",
        labelSuffix: "：",
        labelWidth: 130,
        menuBtn: true,
        submitBtn: false,
        emptyBtn: false,
        menuPosition: "center",
        tabs: false,
        detail: false,
        group: [
          {
            prop: "group1",
            label: "下单人信息",
            column: [
              {
                label: "订单编号",
                prop: "codeNum",
              },
              {
                label: "下单人姓名",
                prop: "realName",
              },
              {
                label: "下单人账号",
                prop: "account",
              },
              {
                label: "下单人手机号",
                prop: "userPhone",
              },
              {
                label: "下单人所在村组",
                prop: "fullDeptName",
                span: 24,
              },
            ],
          },
          {
            label: "收件人信息",
            prop: "group2",
            column: [
              {
                label: "收件人",
                prop: "name",
              },
              {
                label: "收件人手机号",
                prop: "phone",
              },
              {
                label: "收件地址",
                prop: "address",
              },
            ],
          },
          {
            label: "商品信息",
            prop: "group3",
            column: [
              {
                label: "商品名称",
                prop: "goodsName",
              },
              {
                label: "商品编号",
                prop: "goodsCn",
                span: 24,
              },
              {
                label: "商品图片",
                prop: "attachLink",
                span: 24,
              },
              {
                label: "花费积分",
                prop: "totalCost",
                span: 24,
              },
              {
                label: "使用规则",
                prop: "usageRule",
                span: 24,
              },
              {
                label: "温馨提示",
                prop: "reminder",
                span: 24,
              },
            ],
          },
        ],
      },
      detailOption2: {
        labelPosition: "right",
        labelSuffix: "：",
        labelWidth: 130,
        menuBtn: true,
        submitBtn: false,
        emptyBtn: false,
        menuPosition: "center",
        tabs: false,
        detail: false,
        group: [
          {
            prop: "group1",
            label: "获奖人信息",
            display: true,
            column: [
              {
                label: "订单编号",
                prop: "codeNum",
                display: true,
              },
              {
                label: "获奖人姓名",
                prop: "realName",
              },
              {
                label: "获奖人账号",
                prop: "account",
              },
              {
                label: "获奖人手机号",
                prop: "userPhone",
              },
              {
                label: "获奖人所在村组",
                prop: "fullDeptName",
                span: 24,
              },
            ],
          },
          {
            label: "收件人信息",
            prop: "group2",
            display: true,
            column: [
              {
                label: "收件人",
                prop: "name",
              },
              {
                label: "收件人手机号",
                prop: "phone",
              },
              {
                label: "收件地址",
                prop: "address",
              },
            ],
          },
          {
            label: "奖品信息",
            prop: "group3",
            display: true,
            column: [
              {
                label: "奖品名称",
                prop: "awardName",
              },
              {
                label: "奖品编号",
                prop: "awardCn",
                span: 24,
              },
              {
                label: "奖品图片",
                prop: "attachLink",
                span: 24,
              },
              {
                label: "温馨提示",
                prop: "reminder",
                span: 24,
              },
            ],
          },
          {
            prop: "group3",
            display: false,
            column: [
              {
                label: "奖品名称",
                prop: "awardName",
                span: 24,
              },
              {
                label: "奖品编号",
                prop: "awardCn",
                span: 24,
              },
              {
                label: "有效期",
                prop: "validTime",
                span: 24,
              },
              {
                label: "温馨提示",
                prop: "reminder",
                span: 24,
              },
            ],
          },
        ],
      },
      isFullscreen: false,
      dialogVisible: false,
      detail: {},
      dialogType: "",
      openId: "",
      allowWriteOddNum: true, //发货按钮禁用
      deliverVisible: false,
      disableButton: false,
      icon: "el-icon-download el-icon--right",
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: false,
      };
    },
    moduleOption() {
      return this.MODULE === "ordersManage"
        ? this.manageOption
        : this.prizesOption;
    },
    viewOption() {
      return this.MODULE === "ordersManage"
        ? this.detailOption
        : this.detailOption2;
    },
    api() {
      return this.MODULE === "ordersManage" ? manageApi : ordersApi;
    },
    moduleName() {
      return this.MODULE === "ordersManage" ? "orders_manage" : "orders";
    },
  },
  created() {},
  methods: {
    delRow(id) {
      this.$confirm(
        "确定退回该订单吗，确定后，将退回积分给用户；如有物流信息，将会同步删除物流信息。",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.api.closeDeliver(id).then((res) => {
          if (res.data.success) {
            this.refreshChange();
            this.$message({
              type: "success",
              message: res.data.msg,
            });
          } else {
            this.$message({
              type: "warning",
              message: res.data.msg,
            });
          }
        });
      });
    },
    exportData() {
      this.$confirm(`是否导出订单数据?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        this.exportFun();
      });
    },
    /**
     * @description: 导出功能
     * @param {*}
     * @author: wangyy553
     */

    exportFun: debounce(async function () {
      this.icon = "el-icon-loading el-icon--right";
      this.disableButton = true;
      //请求文件
      var params = Object.assign({}, this.query);
      if (params.orderDate) {
        params.startTime = params.orderDate[0];
        params.endTime = params.orderDate[1];
        delete params.orderDate;
      }
      params.status = this.type.prop || "";
      // let params = {};
      const url =
        this.MODULE === "ordersManage"
          ? "/api/admin/integral-order/export"
          : "/api/admin/integral-award/order/export";
      const result = await handleDownload(url, params);
      console.log(params);
      if (result != null) {
        this.icon = "el-icon-download el-icon--right";
        this.disableButton = false;
      }
    }, 200),

    /**
     * @description: 选项卡改变
     * @param {object} column 选择项
     * @author: wangyy553
     */
    handleTabChange(column) {
      this.type = column;
      this.page.currentPage = 1;
      this.$refs.crud.searchReset();
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      var obj = Object.assign(params, this.query);
      if (obj.orderDate) {
        obj.startTime = obj.orderDate[0];
        obj.endTime = obj.orderDate[1];
        delete obj.orderDate;
      }
      obj.status = this.type.prop || "";
      this.api.getList(page.currentPage, page.pageSize, obj).then((res) => {
        this.data = res.data.data.records;
        this.page.total = res.data.data.total;
        this.loading = false;
      });
    },
    async openDialog(type, id, allowWriteOddNum) {
      this.dialogType = type;
      this.openId = id;
      this.allowWriteOddNum = allowWriteOddNum && true;
      if (type === "view") {
        await this.api.getDetail(id).then((res) => {
          this.detail = res.data.data;
          this.initDetail(res.data.data);
        });
        this.dialogVisible = true;
      } else if (type == "deliver") {
        this.$nextTick(() => {
          this.$refs.deliverName.initData();
          this.deliverVisible = true;
        });
      }
    },
    isFullScreen() {
      this.isFullscreen = !this.isFullscreen;
    },
    close() {
      this.dialogVisible = false;
      this.detail = {};
    },
    /**
     * @description: 详情红包和实物区别
     * @param {object} val
     * @author: wangyy553
     */
    initDetail(val) {
        if(val.type == 1) {
            if (val.awardTypeId == 2) {
                this.detailOption2.group[0].label = "";
                this.detailOption2.group[1].display = false;
                this.detailOption2.group[2].display = false;
                this.detailOption2.group[3].display = true;
            } else {
                this.detailOption2.group[0].label = "获奖人信息";
                this.detailOption2.group[1].display = true;
                this.detailOption2.group[2].display = true;
                this.detailOption2.group[3].display = false;
            }
            this.detailOption2.group[2] = {
                ...this.detailOption2.group[2],
                column: [{
                    label: "奖品名称",
                    prop: "awardName",
                },
                {
                    label: "奖品编号",
                    prop: "awardCn",
                    span: 24,
                },
                {
                    label: "奖品图片",
                    prop: "attachLink",
                    span: 24,
                },
                {
                    label: "温馨提示",
                    prop: "reminder",
                    span: 24,
                }]
            }
        }else {
            this.detailOption2.group[2] = {
                ...this.detailOption2.group[2],
                column: [{
                    label: "奖品名称",
                    prop: "awardName",
                },
                {
                    label: "奖品编号",
                    prop: "awardCn",
                    span: 24,
                    display: false
                },
                {
                    label: "奖品图片",
                    prop: "attachLink",
                    span: 24,
                    display: false
                },
                {
                    label: "温馨提示",
                    prop: "reminder",
                    span: 24,
                    display: false
                }]
            }
        }
    },
  },
};
</script>

<style lang='scss' scoped>
.tip-container {
  background: rgb(235, 245, 255);
  padding: 10px 30px;
  // height: 50px;
  border: 1px solid rgb(140, 197, 255);
  border-radius: 5px;
  color: #909399;
  margin-bottom: 20px;
}
</style>

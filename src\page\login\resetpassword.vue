<template>
  <el-form class="login-form"
           status-icon
           v-loading="formLoading"
           :rules="loginRules"
           ref="loginForm"
           :model="loginForm"
           label-width="0">
    <el-form-item prop="phone">
      <el-input size="small"
                @keyup.enter.native="handleLogin"
                v-model="loginForm.phone"
                auto-complete="off"
                placeholder="联系方式">
        <i slot="prefix" class="icon-shouji"/>
      </el-input>
    </el-form-item>
    <el-form-item prop="newPassword">
      <el-input size="small"
                @keyup.enter.native="handleLogin"
                :type="passwordType"
                auto-complete="off"
                v-model="loginForm.newPassword"
                placeholder="请输入密码">
        <i class="el-icon-view el-input__icon" slot="suffix" @click="showPassword"/>
        <i slot="prefix" class="icon-mima"/>
      </el-input>
    </el-form-item>
    <el-form-item prop="rePassword">
      <el-input size="small"
                @keyup.enter.native="handleLogin"
                :type="passwordType"
                v-model="loginForm.rePassword"
                auto-complete="off"
                placeholder="请确认密码">
        <i class="el-icon-view el-input__icon" slot="suffix" @click="showPassword"/>
        <i slot="prefix" class="icon-mima"/>
      </el-input>
    </el-form-item>
    <el-form-item prop="smsCode">
      <el-row :span="24">
        <el-col :span="16">
          <el-input
            @keyup.enter="handleLogin"
            v-model="loginForm.smsCode"
            auto-complete="off"
            :placeholder="$t('login.code')"
          >
            <template #prefix>
              <i class="icon-yanzhengma" />
            </template>
          </el-input>
        </el-col>
        <el-col :span="8">
          <div class="login-code">
            <div @click="handleSend" class="login-code-phone">
              {{ msgText }}
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form-item>
    
    <el-form-item>
      <el-button size="small"
                 type="primary"
                 @click.native.prevent="handleLogin"
                 class="btn-submit">确认重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { isvalidatemobile } from "@/util/validate";
import { mapGetters } from "vuex";
// import {sendLoginSms} from "@/api/user";
import { validCode,forgetPwd } from "@/api/user";
import {encrypt} from "@/util/sm2";
import { noSpace, checkChinese, isKeyBoardContinuousChar, passWordLimit } from '@/util/validate'
export default {
  name: "codelogin",
  data() {
    const validatePhone = (rule, value, callback) => {
      if (isvalidatemobile(value)[0]) {
        callback(new Error(isvalidatemobile(value)[1]));
      } else {
        callback();
      }
    };
        const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入密码'))
      } else {
        const keyBoard = isKeyBoardContinuousChar(value)
        const passWord = passWordLimit(value)
        const chinese = checkChinese(value)
        const isNoSpace = noSpace(value)
        // console.log(keyBoard, passWord, chinese)
        if (keyBoard === true) {
          callback(new Error('密码不能含有键盘排序'))
        } else if (passWord === false) {
          callback(new Error('至少包含大写字母、小写字母、数字、特殊字符中的三类字符'))
        } else if (isNoSpace === false) {
          callback(new Error('密码不能含有空格'))
        } else if (chinese === true) {
          callback(new Error('密码不能含有中文'))
        } else {
          callback()
        }
      }
    }

    const validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.loginForm.newPassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      tenantMode: this.website.tenantMode,
      msgText: '',
      msgTime: '',
      loginForm: {
        phone: '',
        smsCode: '',
        smsKey: '',
        newPassword:'',
        rePassword:'',
      },
      loginRules: {
        phone: [{ required: true, trigger:['blur','change'], validator: validatePhone }],
        smsCode: [{ required: true, trigger: ['blur','change'],message:"验证码不能为空" }],
        newPassword: [{ required: true, trigger: ['blur','change'],validator: validatePassword }],
        rePassword: [{ required: true, trigger: ['blur','change'],validator: validatePass2 }],
      },

      formLoading:false,
      passwordType:'password',
      // showSuccess:false,
    };
  },
  created() {
    this.getMsg();
  },
  mounted() {
    // this.initDate()
  },
  computed: {
    ...mapGetters(["tagWel"]),
    config() {
      return {
        MSGINIT: this.$t("login.msgText"),
        MSGSCUCCESS: this.$t("login.msgSuccess"),
        MSGTIME: 60
      };
    }
  },
  props: [],
  methods: {
    initDate(){
        // getDeptTreeNo().then(res => {
        //   this.countryList = res.data.data;
        //   this.formLoading = false;
        // }).finally(() => {
        //   this.formLoading = false;
        // });
      },
    showPassword() {
        this.passwordType === ""
          ? (this.passwordType = "password")
          : (this.passwordType = "");
    },
    handleSend() {
      this.$refs.loginForm.validateField('phone',valid => {
        // console.log(valid,!valid);
        if (!valid) {
          // console.log('发送验证码',encrypt(this.loginForm.phone));
          validCode(encrypt(this.loginForm.phone)).then(res => {
            const data = res.data;
            console.log(data);
            if (data.success) {
              this.loginForm.smsKey = data.data;
              this.msgText = this.msgTime + this.config.MSGSCUCCESS;
              const time = setInterval(() => {
                this.msgTime--;
                this.msgText = this.msgTime + this.config.MSGSCUCCESS;
                if (this.msgTime === 0) {
                  this.msgTime = this.config.MSGTIME;
                  this.msgText = this.config.MSGINIT;
                  clearInterval(time);
                }
              }, 1000);
              this.$message.success("验证码发送成功");
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    },
    handleLogin() {
      if(this.loginForm.smsKey == ""){
        this.$message.error("请先发送验证码");
        return
      }
      this.$refs.loginForm.validate(valid => {
        console.log(valid);
        if (valid) {
          let data ={
            phone:this.loginForm.phone,
            smsKey:this.loginForm.smsKey,
            smsCode:this.loginForm.smsCode,
            newPassword:this.loginForm.newPassword
          }
          forgetPwd(data).then(res => {
            const data = res.data;
            console.log(data);
            if (data.success) {
              this.$message.success("修改成功");
              this.$emit("returnUser")
            } else {
              this.$message.error(data.msg);
            }
          });
        }
      });
    },
    getMsg() {
      this.msgText = this.config.MSGINIT;
      this.msgTime = this.config.MSGTIME;
    },
    
  }
};
</script>

<style>
.msg-text {
  display: block;
  width: 60px;
  font-size: 12px;
  text-align: center;
  cursor: pointer;
}
.msg-text.display {
  color: #ccc;
}
</style>

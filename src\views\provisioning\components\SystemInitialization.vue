<template>
  <div class="step-container">
    <div class="form-title">
      <div class="title-bar"></div>
      <span>系统初始化</span>
    </div>
    <div class="title-divider"></div>

    <div class="step-content">
      <div class="box_card">
        <div class="import-item">
          <span class="import-label">组织架构、系统用户导入：</span>
          <div class="import-actions">
            <el-upload ref="uploadOrg" action="#" :disabled="isViewMode" :show-file-list="false" :file-list="orgFileList" :on-change="handleOrgUploadChange" :auto-upload="false">
              <el-button slot="trigger" type="primary" size="small" icon="el-icon-upload2" :disabled="isViewMode" v-show="!orgHasFile">导 入</el-button>
              <el-button type="warning" size="small" icon="el-icon-delete" :disabled="isViewMode" @click="deleteOrgFile" v-show="orgHasFile">删 除</el-button>
              <el-button size="small" style="margin-left: 20px;" icon="el-icon-download" @click="downloadTemplate('org')">模板下载</el-button>
            </el-upload>
          </div>
        </div>

        <div class="import-item">
          <span class="import-label">镇、村介绍导入：</span>
          <div class="import-actions">
            <el-upload ref="uploadTown" action="#" :disabled="isViewMode" :show-file-list="false" :file-list="townFileList" :on-change="handleTownUploadChange" :auto-upload="false">
              <el-button slot="trigger" type="primary" size="small" icon="el-icon-upload2" :disabled="isViewMode" v-show="!townHasFile">导 入</el-button>
              <el-button type="warning" size="small" icon="el-icon-delete" :disabled="isViewMode" @click="deleteTownFile" v-show="townHasFile">删 除</el-button>
              <el-button size="small" style="margin-left: 20px;" icon="el-icon-download" @click="downloadTemplate('town')">模板下载</el-button>
            </el-upload>
          </div>
        </div>
      </div>
    </div>

    <div class="actions-divider"></div>

    <div class="action-buttons">
      <div></div>
      <div class="step-buttons">
        <el-button @click="$emit('cancel')">{{ isViewMode ? '关闭' : '取消' }}</el-button>
        <el-button v-if="!isViewMode" @click="save">保存</el-button>
        <el-button @click="prev">上一步</el-button>
        <el-button v-if="isViewMode && !isEdit" type="primary" @click="next">下一步</el-button>
        <el-button v-if="!isViewMode" type="primary" @click="beforeSave">平台开通</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getToken } from '@/util/auth'
import { handleDownloadFile } from '@/util/download';
export default {
  props: {
    isViewMode: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      tenantForm: {},
      orgFileList: [],
      townFileList: [],
      orgHasFile: false,
      townHasFile: false,
      isEdit: false,
    }
  },
  created() {
    console.log(this.formData, "systemInitialization")
    if (this.formData) {
      if(this.formData.status==1){
        this.isEdit = false
      }else {
        this.isEdit = true
      }
      this.tenantForm = { ...this.formData };
      if (this.tenantForm.userDeptData && this.tenantForm.userDeptData.length > 0) {
        this.orgHasFile = true
      }
      if (this.tenantForm.userDeptFileList && this.tenantForm.userDeptFileList.length > 0) {
        this.orgHasFile = true
        this.orgFileList = this.tenantForm.userDeptFileList
      }
      if (this.tenantForm.villageTownData && this.tenantForm.villageTownData.length > 0) {
        this.townHasFile = true
      }
      // console.log(this.tenantForm.villageTownData.length ==0 && this.tenantForm.villageTownFileList && this.tenantForm.villageTownFileList.length >0)
      if (this.tenantForm.villageTownFileList && this.tenantForm.villageTownFileList.length > 0) {
        this.townHasFile = true
        this.townFileList = this.tenantForm.villageTownFileList
      }
      // this.tenantForm.regionCode = this.formData.allRegionCodes
    }
  },
  methods: {
    save() {
      console.log(this.orgFileList, this.townFileList, this.tenantForm)
      this.tenantForm.userDeptFileList = this.orgFileList
      this.tenantForm.villageTownFileList = this.townFileList
      this.$emit('save', this.tenantForm, "systemInitialization");
    },
    prev() {
      this.tenantForm.userDeptFileList = this.orgFileList
      this.tenantForm.villageTownFileList = this.townFileList
      this.$emit('prev', this.tenantForm, "systemInitialization");
    },

    beforeSave() {
      this.tenantForm.userDeptFileList = this.orgFileList
      this.tenantForm.villageTownFileList = this.townFileList
      this.$emit('submit', this.tenantForm, "systemInitialization");
    },

    next() {
      this.$emit('next', this.tenantForm, "systemInitialization");
    },

    getFormData() {
      return {};
    },

    downloadTemplate(type) {
      const templates = {
        'org': '/api/serviceActive/template/one',
        'town': '/api/serviceActive/template/two'
      };
      const url = `${templates[type]}?${this.website.tokenHeader}=${getToken()}`;
      handleDownloadFile(url);
    },

    deleteOrgFile() {
      this.$refs.uploadOrg.clearFiles();
      this.orgFileList = [];
      this.orgHasFile = false;
    },
    deleteTownFile() {
      this.$refs.uploadTown.clearFiles();
      this.townFileList = [];
      this.townHasFile = false;
    },
    handleOrgUploadChange(file, fileList) {
      // console.log(file, fileList, "file, fileList");
      const isExcel = file.raw.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel';
      const isLt10M = file.size / 1024 / 1024 <= 10;
      if (!isExcel) {
        this.$message.error('只能上传Excel文件!');
        this.$refs.uploadOrg.clearFiles();
        return;
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过10M!');
        this.$refs.uploadOrg.clearFiles();
        return;
      }
      this.orgFileList = fileList;
      this.orgHasFile = true
      this.$message.success('文件添加成功');
      // console.log(this.orgFileList, "this.orgFileList");
    },

    handleTownUploadChange(file, fileList) {
      // console.log(file, fileList, "file, fileList");
      const isExcel = file.raw.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel';
      const isLt10M = file.size / 1024 / 1024 <= 10;
      if (!isExcel) {
        this.$message.error('只能上传Excel文件!');
        this.$refs.uploadTown.clearFiles();
        return;
      }
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过10M!');
        this.$refs.uploadTown.clearFiles();
        return;
      }
      this.townFileList = fileList;
      this.townHasFile = true
      this.$message.success('文件添加成功');
      // console.log(this.orgFileList, "this.orgFileList");
    },
  }
};
</script>

<style lang="scss" scoped>
.form-title {
  display: flex;
  align-items: center;

  .title-bar {
    width: 4px;
    height: 20px;
    background-color: #409eff;
    margin-right: 8px;
  }

  span {
    font-size: 16px;
    font-weight: 500;
  }
}

.title-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 15px 0 20px 0;
}

.actions-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 20px -20px 20px -20px;
}

.action-buttons {
  display: flex;
  justify-content: end;
  align-items: center;
  width: 100%;

  .step-buttons {
    display: flex;
    gap: 10px;

    .el-button {
      min-width: 80px;
    }
  }
}
.step-content {
  padding: 40px 20px;
  min-height: 460px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 30px;

  .box_card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 40px 60px;
    width: 600px;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 30px;
  }

  .import-item {
    display: flex;
    align-items: center;
    gap: 20px;
    width: 100%;

    .import-label {
      font-size: 14px;
      color: #333;
      width: 180px;
      text-align: right;
      flex-shrink: 0;
    }

    .import-actions {
      display: flex;
      gap: 8px;

      .el-button {
        min-width: 80px;
      }
    }
  }
}
</style>

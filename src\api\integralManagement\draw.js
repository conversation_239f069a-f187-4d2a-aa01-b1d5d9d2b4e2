/*
 * @Descrip: 积分管理-积分抽奖管理接口
 * @Author: chenn26
 * @Date: 2022-01-13
 * @LastEditors:
 * @LastEditTime:
 */
import request from '@/router/axios';

/**
 * @description 获取积分抽奖列表
 * @param {number} current
 * @param {number} size
 * @param {object} params
 */
export const getDrawList = (current, size, params) => {
	return request({
		url: '/api/admin/integral-award/page',
		method: 'get',
		params: {
			...params,
			current,
			size,
		}
	})
}

/**
 * @description 获取积分抽奖列表详情
 * @param {number} id
 */
export const getDrawDetail = (id) => {
	return request({
		url: '/api/admin/integral-award/' + id,
		method: 'get'
	})
}

/**
 * @description 上传图片
 * @param {object} data
 */
export const uploadImg = (data) => {
	return request({
		url: '/api/blade-resource/oss/endpoint/put-file-attach',
		method: 'post',
		data
	})
}

/**
 * @description 新增
 * @param {object} data
 */
export const drawAdd = (data) => {
	return request({
		url: '/api/admin/integral-award',
		method: 'post',
		data: data
	})
}

/**
 * @description 编辑
 * @param {object} data
 */
export const drawUpdate = (data) => {
	return request({
		url: '/api/admin/integral-award',
		method: 'put',
		data: data
	})
}

/**
 * @description 删除
 * @param {number} id
 */
export const drawDelete = (id) => {
	return request({
		url: '/api/admin/integral-award/' + id,
		method: 'delete'
	})
}

/**
 * @description 上下架
 * @param {string} ids
 * @param {string} status
 */
export const toggleShelf = (ids, status) => {
	return request({
		url: '/api/admin/integral-award/shelf',
		method: 'put',
		params: {
			ids,
			status
		}
	})
}

/**
 * @description 抽奖设置-奖品列表
 * @param {number} current
 * @param {number} size
 */
export const getAwardList = (current, size) => {
	return request({
		url: '/api/admin/integral-award/rule/page',
		method: 'get',
		params: {
			current,
			size,
		}
	})
}

/**
 * @description 抽奖设置-添加奖品
 * @param {object} params
 */
export const addAward = (params) => {
	return request({
		url: '/api/admin/integral-award/rule/submit',
		method: 'put',
		params: {
			...params
		}
	})
}

/**
 * @description 抽奖设置-搜索奖品
 * @param {object} params
 */
export const getAwardByCondition = (params) => {
	return request({
		url: '/api/admin/integral-award/select/page',
		method: 'get',
		params: {
			...params
		}
	})
}

/**
 * @description 抽奖设置-删除奖品
 * @param {number} awardId
 */
export const deleteAward = (awardId) => {
	return request({
		url: '/api/admin/integral-award/rule/' + awardId,
		method: 'delete'
	})
}

/**
 * @description 获取字典
 * @param {string} dictType
 */
export const getDiction = (dictType) => {
	return request({
		url: '/api/blade-system/dict-biz/dictionary-tree?code=' + dictType,
		method: 'get'
	})
}

/**
 * @description 获取奖品出入库记录
 * @param {number} current
 * @param {number} size
 * @param {object} params
 */
export const getStockWareHouse = (current, size, params) => {
	return request({
		url: '/api/admin/integral-award/stock/page',
		method: 'get',
		params: {
			...params,
			current,
			size
		}
	})
}

/**
 * @description 奖品出入库
 * @param {Object} data
 */
export const putStockWareHouse = (data) => {
	return request({
		url: '/api/admin/integral-award/stock',
		method: 'put',
		data: data
	})
}

/**
 * @description 抽奖规则
 */
export const getDrawRule = () => {
	return request({
		url: '/api/user/integral-award/rule',
		method: 'get'
	})
}

/**
 * @description 抽奖设置
 * @param {Object} data
 */
export const drawSetting = (data) => {
	return request({
		url: '/api/admin/integral-award/rule',
		method: 'post',
		data: data
	})
}

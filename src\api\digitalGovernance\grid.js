import request from '@/router/axios';


export const remove = (id) => {
  return request({
    url: '/api/admin/grid/remove',
    method: 'post',
    params: {
      id,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/api/admin/grid/save',
    method: 'post',
    data: row
  })
}


export const update = (row) => {
  return request({
    url: '/api/admin/grid/update',
    method: 'post',
    data: row
  })
}

//树形结构
export const getGridTree = () => {
  return request({
    url: '/api/admin/grid/tree',
    method: 'get'
  })
}
//详情
export const detail = (params) => {
  return request({
    url: '/api/admin/grid/detail',
    method: 'get',
    params,
  })
}


export const getLazyList = (parentId, params) => {
  return request({
    url: '/api/admin/grid/pageChildList',
    method: 'get',
    params: {
      ...params,
      parentId
    }
  })
}






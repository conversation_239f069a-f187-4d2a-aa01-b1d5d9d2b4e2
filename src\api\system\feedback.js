/*
 * @Author: zhouwj83
 * @Date: 2021-07-07 17:12:01
 * @LastEditors: chenz76
 * @LastEditTime: 2022-01-07 17:28:14
 * @Description: 意见反馈
 */
import request from '@/router/axios'

/**
 * @description 获取意见反馈
 * @param {string} fid 
 */
export const getFeedback = (fid) => {
  return request({
    url: '/api/feedback/admin/detail?id=' + fid,
    method: 'get'
  })
}
/**
 * @description 删除意见反馈
 * @param {object} data 
 */
export const remove = (ids) => {
  return request({
    url: '/api/feedback/admin/delete?ids='+ids,
    method: 'post',
  })
}

/**
 * @description 意见反馈列表
 * @param {number} current 
 * @param {number} size 
 * @param {object} params 
 */
export const getList = (current, size, params) => {
  return request({
    url: '/api/feedback/admin/page',
    method: 'get',
    params: {
      content: params.content,
      current,
      size,
      status: params.status
    }
  })
}
/**
 * @description 保存意见反馈
 * @param {object} params 
 */
export const comment = (params) => {
  return request({
    url: '/api/feedback/admin/reply',
    method: 'post',
    data: params
  })
}

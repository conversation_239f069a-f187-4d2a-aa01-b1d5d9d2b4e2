<!--
 * @Author: chenn26
 * @Date: 2021-01-06
 * @LastEditors: linzq33
 * @LastEditTime: 2023-07-08 16:03:08
 * @Description: 便民服务-用工信息
-->
<template>
  <div>
    <el-row>
      <el-col :span="5" v-loading="treeLoading">
        <div class="box">
          <el-scrollbar>
            <basic-container>
              <avue-tree :option="treeOption" :data="treeData" @node-click="nodeClick" show-checkbox>
              </avue-tree>
            </basic-container>
          </el-scrollbar>
        </div>
      </el-col>
      <el-col :span="19">
        <basic-container v-loading="loading">
          <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page"
            :permission="permissionList" :before-open="beforeOpen" v-model="form" ref="crud"
            @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
            @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
            <template slot="status" slot-scope="scope">
              <span>{{ scope.row.status === 0 ? '已上架' : '未上架' }}</span>
            </template>
            <template slot="checkStatus" slot-scope="scope">
              <span v-if="scope.row.checkStatus === 0">未通过</span>
              <span v-if="scope.row.checkStatus === 1">已通过</span>
            </template>
            <template slot-scope="scope" slot="menu">
              <div class="cell">
                <button v-if="permission.employInfo_view" type="button"
                  class="el-button el-button--text el-button--small" @click="$refs.crud.rowView(scope.row)">
                  <i class="el-icon-view"></i>
                  <span>查看</span>
                </button>
                <button v-if="scope.row.status != 0 && permission.employInfo_edit" type="button"
                  class="el-button el-button--text el-button--small" @click="update(scope.row)">
                  <i class="el-icon-edit"></i>
                  <span>编辑</span>
                </button>
                <button v-if="scope.row.checkStatus != 1 && permission.employInfo_check" type="button"
                  class="el-button el-button--text el-button--small" @click="downOrRemove('check', scope.row.id)">
                  <i class="el-icon-s-check"></i>
                  <span>审核</span>
                </button>
                <button v-if="scope.row.status != 0 && permission.employInfo_delete" type="button"
                  class="el-button el-button--text el-button--small" @click="downOrRemove('remove', scope.row.id)">
                  <i class="el-icon-delete"></i>
                  <span>删除</span>
                </button>

              </div>
            </template>
            <template slot="menuLeft">
              <el-button type="primary" size="small" icon="el-icon-plus" v-if="permission.employInfo_up"
                @click="downOrRemove('save')">新 增</el-button>
              <el-button type="default" size="small" plain icon="el-icon-delete" v-if="permission.employInfo_down"
                @click="downOrRemove('down')">下 架</el-button>
              <el-button type="danger" size="small" plain icon="el-icon-delete" v-if="permission.employInfo_delete"
                @click="downOrRemove('remove')">批量删除</el-button>
            </template>
          </avue-crud>
        </basic-container>
      </el-col>
    </el-row>
    <Detail v-if="dialogFormVisible" :detail="detail" :disabled="detDisabled" :treeData="treeData" />
    <!-- 审核弹窗 -->
    <el-dialog :visible.sync="showCheck" :close-on-press-escape="false" :close-on-click-modal="false"
      :append-to-body="true" title="审核" width="30%" @close="close('false')">
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px">
        <el-form-item label="审核结果" prop="operation">
          <el-radio-group v-model="ruleForm.operation">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="0">驳回</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="ruleForm.operation === 0" label="驳回原因" prop="reason">
          <el-input :rows="3" v-model.trim="ruleForm.reason" type="textarea" placeholder="请输入驳回原因" maxlength="30"
            show-word-limit />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="close(false)">取 消</el-button>
        <el-button size="mini" type="primary" @click="check('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getDetail, down, remove, getList, infoCheck, getTreeList } from "@/api/infoRelease/employInfo";
import { mapGetters } from "vuex";
import Detail from './detail';
export default {
  components: { Detail },
  data () {
    return {
      form: {},
      query: {},
      detail: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      welfareList: [],
      dialogFormVisible: false,
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        border: true,
        index: true,
        viewBtn: true,
        editBtn: true,
        delBtn: true,
        selection: true,
        searchMenuSpan: 6,
        menu: true,
        menuFixed: false,
        menuWidth: 250,
        labelWidth: 120,
        column: [
          {
            label: "招聘岗位",
            prop: "job",
            type: "input",
            search: true,
            maxlength: 20,
          },
          { label: "有效期限", prop: "validPeriod", hide: true },
          { label: "招聘类型", prop: "recruitmentTypeValue" },
          { label: "招工单位", prop: "recruitmentUnit", hide: true },
          { label: "工资待遇", prop: "pay", hide: true },
          { label: "职位描述", prop: "description", hide: true },
          { label: "工资类型", prop: "salaryTypeValue", hide: true },
          { label: "联系人", prop: "contactPerson", hide: true },
          { label: "联系电话", prop: "contactPhone", hide: true },
          { label: "联系人职务", prop: "contactPost", hide: true },
          {
            label: '公开范围',
            prop: 'publicArea',
            type: 'tree',
            span: 24,
            multiple: true,
            collapseTags: true,
            maxCollapseTags: 3,
            collapseTagsTooltip: true,
            dicData: [],
            props: { label: "title" },
            hide: true
          },
          { label: "福利待遇", prop: "welfareValue", hide: true, span: 24, },
          { label: "工作地点", prop: "address", hide: true, span: 24, },
          { label: "发布人姓名", prop: "publisherName", viewDisplay: false },
          { label: "发布组织", prop: "publishDept", viewDisplay: false },
          { label: "有效期限", prop: "validPeriod", viewDisplay: false },
          { label: "审核结果", prop: "checkStatus", slot: true, viewDisplay: false },
          {
            label: "状态",
            prop: "status",
            type: "input",
            slot: true,
            viewDisplay: false
          },
          // 审核过程
          {
            label: '处理流程',
            prop: 'checkList',
            type: 'dynamic',
            labelWidth: 100,
            hide: true,
            span: 24,
            children: {
              align: 'center',
              type: 'form',
              index: false,
              headerAlign: 'center',
              column: [
                {
                  label: '审核人员',
                  prop: 'createUserName',
                  span: 24,
                  formslot: true
                },
                {
                  label: '审核结果',
                  prop: 'operation',
                  type: 'select',
                  dicData: [
                    { label: "转办", value: 1 },
                    { label: "办结", value: 2 },
                  ],
                  span: 12,
                  formslot: true
                },
                {
                  label: '审核意见',
                  prop: 'reason',
                  formslot: true,
                  type: 'textarea',
                  span: 24,
                  maxRows: 3,
                  minRows: 1
                },
                {
                  label: '审核时间',
                  prop: 'createTime',
                  type: 'date',
                  format: 'yyyy-MM-dd HH:mm:ss',
                  valueFormat: 'yyyy-MM-dd HH:mm:ss',
                  span: 12,
                  formslot: true
                },

              ]
            }
          }
        ]
      },
      data: [],
      treeLoading: false,
      detDisabled: false, // 是否查看
      treeData: [],
      treeOption: {
        addBtn: false,
        menu: false,
        size: 'small',
        props: {
          labelText: '标题',
          label: 'title',
          value: 'value',
          children: 'children'
        }
      },
      deptId: '',
      showCheck: false,
      ruleForm: {
        operation: 1,
        reason: ''
      },
      rules: {
        operation: [
          { required: true, message: '请选择', trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
      },
    };
  },
  mounted () {
    this.getDeptTree();
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList () {
      return {
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        editBtn: false
      };
    },
    ids () {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    isShowDetail (isShowDialog, isRefresh) {
      this.dialogFormVisible = isShowDialog
      if (isRefresh) {
        this.refreshChange();
      }
    },
    // 审核
    check (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          infoCheck(this.ruleForm).then(res => {
            this.$message({
              type: 'success',
              message: '操作成功!'
            })
            this.$refs[formName].resetFields()
            this.onLoad(this.page);
            this.close()
          })
        } else {
          return false
        }
      })
    },
    close () {
      this.showCheck = false
      this.ruleForm = {
        operation: 1,
        reason: ''
      }
    },

    update (row) {
      this.detDisabled = false
      this.detail = row
      this.isShowDetail(true)
    },
    downOrRemove (type, rowId) {
      // 用工信息-新增编辑页
      if (type == 'save') {
        this.isShowDetail(true)
        return;
      } else if (type == 'check') {
        this.showCheck = true
        this.ruleForm.id = rowId
        return;
      } else if (type == 'view') {
        this.detDisabled = true
        this.detail.id = rowId
        this.isShowDetail(true)
        return;
      }
      let param = ''
      if (rowId) { // 行删除/下架
        param = rowId
      } else { // 批量删除/下架
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        param = this.ids
      }
      let tips = ''
      let message = ''
      if (type === 'down') {
        tips = '确定将选择数据下架?'
        message = '所选数据已下架'
      } else {
        tips = '确定将选择数据删除?'
        message = '删除成功！'
      }
      this.$confirm(tips, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return type === 'down' ? down(param) : remove(param);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: message
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen (done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    selectionChange (list) {
      this.selectionList = list;
    },
    selectionClear () {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    searchReset () {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange (params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    currentChange (currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange (pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange () {
      this.onLoad(this.page, this.query);
    },
    onLoad (page, params = {}) {
      this.detail = {}
      this.loading = true;
      
      this.query.deptId = this.deptId; // 默认企业id空值
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    nodeClick (data) {
      this.deptId = data.id;
      this.page.currentPage = 1
      this.onLoad(this.page, undefined)
    },
    getDeptTree () {
      this.treeLoading = true;
      getTreeList().then(res => {
        this.treeData = res.data.data
        const publicArea = this.findObject(this.option.column, 'publicArea')
        publicArea.dicData = res.data.data;
        this.treeLoading = false;
      })
    },
  }
};
</script>

<style></style>

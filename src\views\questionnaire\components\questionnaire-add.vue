<template>
  <basic-container>
    <el-dialog :fullscreen="isFullscreen" :visible="showAddDialog" append-to-body="true" :close-on-click-modal="false"
      top="100px" width="60%" @close="handleClose">
      <div slot="title" class="header">
        <div class="avue-crud__dialog__header">
          <span class="el-dialog__title">{{ pageTitle }}</span>
          <div class="avue-crud__dialog__menu">
            <i @click="isFullScreen" class="el-dialog__close el-icon-full-screen"></i>
          </div>
        </div>
        <button type="button" aria-label="Close" class="el-dialog__headerbtn">
          <i class="el-dialog__close el-icon el-icon-close"></i>
        </button>
      </div>
      <el-row v-if="!showLimitDialog">
        <el-col :span="4">
          <h2>添加问题</h2>
          <el-divider></el-divider>
          <div>
            <el-button type="text" icon="el-icon-circle-check" @click="addQuestion(1)" size="medium">单选题</el-button>
          </div>
          <div>
            <el-button type="text" icon="el-icon-document-checked" @click="addQuestion(2)">多选题</el-button>
          </div>
          <div>
            <el-button type="text" icon="el-icon-tickets" @click.prevent="addQuestion(3)">简答题</el-button>
          </div>
        </el-col>
        <el-col :span="13">
          <div ref="question" class="question-form-flow">
            <avue-form ref="questionnaireForm" :defaults.sync="questionnaireDefaults" :option="questionnaireOption"
              v-model="form" :upload-before="formUploadBefore" :upload-after='formUploadAfter'>
              <template slot="title">
                <div class="inputDeep">
                  <el-input placeholder="请输入标题" v-model.trim="form.title" maxlength="50"></el-input>
                </div>
                <el-divider></el-divider>
              </template>
              <template slot="description">
                <div class="inputDeep">
                  <el-input placeholder="请输入说明" v-model.trim="form.description" maxlength="200"></el-input>
                </div>
              </template>
              <template slot="subjectList">
                <div v-if="form.subjectList.length !== 0">
                  <div v-for="(item, num) in form.subjectList" :key="num" style="margin-bottom: 8px">
                    <el-card @click.native="addQuestionForm(item, num)">
                      <el-row>
                        <el-col :span="20" class="questionForm">
                          <el-form-item :required="item.isRequired === 1 ? true : false" label-width="100%"
                            label-position="left">
                            <span slot="label">
                              {{ num + 1 }}.{{ item.title }}
                              <el-tag v-if="item.type !== 3" :type="item.type === 1 ? '' : 'warning'" effect="plain"
                                size="mini">
                                {{ item.type === 1 ? '单选' : '多选' }}
                              </el-tag>
                            </span>
                          </el-form-item>
                          <div v-if="item.type === 1">
                            <div v-for="(option, index) in item.options" :key="index">
                              <el-radio v-model="option.selected" :label="true">
                                <span class="word_break">{{ option.title }}</span>
                                <el-image v-if="option.attachLink" :src="option.attachLink"
                                  style="width: 100px; height: 100px" fit="container"></el-image>
                              </el-radio>
                            </div>
                          </div>
                          <div v-else-if="item.type === 2">
                            <div v-for="(option, index) in item.options" :key="index">
                              <el-checkbox>
                                <span class="word_break">{{ option.title }}</span>
                                <el-image v-if="option.attachLink" :src="option.attachLink"
                                  style="width: 100px; height: 100px" fit="container"></el-image>
                              </el-checkbox>
                            </div>
                          </div>
                          <div v-else>
                            <el-input type="textarea" :placeholder="item.prompt" maxlength="50"></el-input>
                          </div>
                        </el-col>
                        <el-col :span="4" style="text-align: right">
                          <div>
                            <el-button type="text" icon="el-icon-delete-solid" @click.stop="deleteQuestion(num)"
                              size="medium"></el-button>
                          </div>
                          <div>
                            <el-button icon="el-icon-arrow-up" circle v-if="num !== 0" size="mini"
                              @click="upQuestion(item)">
                            </el-button>
                          </div>
                          <div>
                            <el-button icon="el-icon-arrow-down" circle size="mini"
                              v-if="num !== form.subjectList.length - 1" @click="downQuestion(item)">
                            </el-button>
                          </div>
                        </el-col>
                      </el-row>
                    </el-card>
                  </div>
                </div>
                <div><span></span></div>

              </template>
              <template slot="menuForm" slot-scope="{row}">
                <el-button v-if="pageType!==4 && pageType!==5" @click="saveOption('saveDraft',row)" :icon="draftIcon"
                  :disabled="draftDisabled">保存到草稿</el-button>
                <el-button v-else @click="handleClose">取消</el-button>
                <el-button type="primary" @click="saveOption('completeQuest',row)">完成</el-button>
              </template>
            </avue-form>
          </div>
        </el-col>
        <el-col :span="7" class="componentForm question-form-flow" style="padding-right: 8px">
          <div v-if="isShowComponentForm">
            <h2>{{ questionType[componentOptions.type] }}</h2>
            <el-divider></el-divider>
            <!-- <avue-form
                            ref="componentForm"
                            :defaults="defaults"
                            :option="componentFormOption1"
                            v-model="componentOptions"
                        >
                           
                        </avue-form> -->

            <el-form :model="componentOptions" ref="dynamicValidateForm" label-width="20px">
              <el-form-item label="题目" prop="title" label-width="60px" :rules="[
                { required: true, message: '题目不能为空' },
              ]">
                <el-input v-model.trim="componentOptions.title" size="small" maxlength="200" show-word-limit>
                </el-input>
              </el-form-item>
              <el-form-item v-if="componentOptions.type !== 3" class="error_width" prop="options" label="选项"
                label-width="60px" :rules="[
                  { message: componentOptions.type === 1 ? '注意：单选题最少创建2个选项，最多创建5个选项' : '注意：多选题最少创建2个选项，最多创建10个选项', type: 'array', min: 2, max: componentOptions.type === 1 ? 5 : 10 }
                ]">
              </el-form-item>
              <div v-if="componentOptions.type !== 3">
                <el-form-item v-for="(option, index) in componentOptions.options" :key="index"
                  :prop="'options[' + index + ']'" :rules="[
                    { validator: validate2, trigger: 'blur' }
                  ]">
                  <el-row>
                    <el-col :span="18" style="padding-right: 5px">
                      <el-input v-model="option.title" size="small" maxlength="200" show-word-limit></el-input>
                    </el-col>
                    <el-col :span="4" style="padding-right: 5px">
                      <el-upload @click.native="getFlagIndex(index)" class="avatar-uploader"
                        action="/api/blade-resource/oss/endpoint/put-file-attach" :headers="headers"
                        :show-file-list="false" :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload"
                        :on-remove="handleRemove">
                        <img v-if="option.attachLink" :src="option.attachLink" class="avatar"
                          style="width: 40px; height: 40px">
                        <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                      </el-upload>
                    </el-col>
                    <el-col :span="2">
                      <el-button @click="removeOption(option)" type="danger" icon="el-icon-delete" circle size="small"
                        v-show="componentOptions.options.length !== 1"></el-button>
                    </el-col>
                  </el-row>
                </el-form-item>
                <el-button type="text" @click="addOption" class="ml-20p">添加选项</el-button>
              </div>
              <el-form-item label-width="80px" label="提示文字" v-else>
                <el-input v-model="componentOptions.prompt" size="small" maxlength="50"></el-input>
              </el-form-item>
              <el-form-item label-width="60px" label="必填">
                <el-switch v-model="componentOptions.isRequired" :active-value="1" :inactive-value="0">
                </el-switch>
              </el-form-item>
            </el-form>
          </div>
        </el-col>
      </el-row>
      <div v-else>
        <avue-form ref="limitForm" :option="limitOption" v-model="limitForm">
          <template slot="menuForm">
            <el-button :icon="submitIcon" :disabled="disabledBtn" type="primary"
              @click="saveQuestionnaire">完成</el-button>
          </template>
        </avue-form>
      </div>
    </el-dialog>
  </basic-container>
</template>
<script>
import { saveQuestionnaire, updateDraft, saveToDraft, updateTemplate, addTemplate  } from '@/api/questionnaire/survey'
import { getToken } from '@/util/auth'
import { mapGetters } from 'vuex'
import {Base64} from 'js-base64';

export default {
  props: {
    showAddDialog: {
      type: Boolean,
      default: true,
    },
    pageType: {
      type: Number,
      default: 1
    },
    draftDetail: {
      type: Object
    }
  },
  inject: ['getDraftNum', 'getPage', 'onLoad'],
  computed: {
    ...mapGetters(['userInfo', 'permission']),
    pageTitle(){
      if(this.pageType==1 || this.pageType==3){
        return '添加问卷'
      }else if(this.pageType==2){
        return '编辑问卷'
      }else if(this.pageType ==4){
        return '添加模版'
      }else{
        return '编辑模版'
      }
    }
  },
  mounted () {
    if (this.pageType === 2 ||this.pageType === 3 ||this.pageType ===5) {
      this.form = this.draftDetail
    }
  },
  data () {
    var validateArray = (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error('注意：至少创建1个问题'));
      } else if (value.length > 100) {
        callback(new Error('注意：最多只能创建100个问题'));
      } else {
        callback();
      }
    }
    return {
      validate2: (rule, value, callback) => {
        if (value.title === '' && value.attachLink === '') {
          callback(new Error('选项不能为空'));
        } else {
          callback();
        }
      },

      isNotVillageAdmin: false,  // 是否村管理员 true
      isShowComponentForm: false,
      headers: {},
      indexFlag: '', // 添加选项图片index
      questionIndexFlag: '', // 当前在编辑的问题index
      // pageTitle: this.pageType === 1 ? '添加问卷' : '编辑问卷',
      showLimitDialog: false,
      questionType: {
        1: '单选题',
        2: '多选题',
        3: '简答题'
      },
      isFullscreen: false,
      form: {
        subjectList: []
      },
      componentOptions: {},
      questionnaireDefaults: {},
      questionnaireOption: {
        labelWidth: 10,
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            prop: 'attachLink',
            span: 24,
            type: 'upload',
            listType: 'picture-img',
            accept: ".jpeg,.jpg,.png",
            tip: '仅支持上传png/jpg/jpeg格式图片',
            loadText: '图片上传中…请稍等',
            action: '/api/blade-resource/oss/endpoint/put-file-attach',
            propsHttp: {
              res: 'data',
              url: 'link',
              name: 'attachId'
            },
          },
          {
            prop: 'title',
            span: 24,
            formslot: true,
            size: 'medium',
            maxlength: 50,
            rules: [
              {
                required: true,
                message: '请输入标题',
                trigger: 'blur'
              }
            ]
          },
          {
            prop: 'description',
            span: 24,
            formslot: true,
            maxlength: 200,

          },
          {
            prop: 'subjectList',
            span: 24,
            formslot: true,
            rules: [
              {
                validator: validateArray,
                type: 'array',
                trigger: 'change'

              }
            ]
          }
        ]
      },
      limitOption: {
        submitBtn: false,
        emptyBtn: false,
        labelWidth: 160,
        column: [
          {
            label: '每人限填一次',
            prop: 'writeTimes',
            type: 'select',
            span: 24,
            dicData: [
              {
                label: '1',
                value: 1
              },
              {
                label: '2',
                value: 2,
              },
              {
                label: '3',
                value: 3
              },
              {
                label: '4',
                value: 4
              },
              {
                label: '5',
                value: 5
              },
              {
                label: '无限制',
                value: 0
              }
            ],
            rules: [
              {
                required: true,
                message: '请选择限填次数',
                trigger: 'blur'
              }
            ]

          },
          {
            label: '填表结束时间',
            prop: 'finishTime',
            type: 'datetime',
            span: 24,
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            pickerOptions: {
              disabledDate (time) {
                return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
              },
            }
          },
          {
            label: '是否允许匿名',
            prop: 'isAnonymity',
            type: 'switch',
            span: 24,
            dicData: [
              {
                label: '否',
                value: 0
              },
              {
                label: '是',
                value: 1
              },
            ],
            value: 0,
            rules: [
              {
                required: true,
                message: '请选择是否允许匿名',
                trigger: 'blur'
              }
            ]
          },
          {
            label: '填写范围',
            prop: 'fillType',
            type: 'checkbox',
            span: 24,
            dicUrl: "/api/blade-system/dict/dictionary?code=survey_fill_type",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            rules: [
              {
                required: true,
                message: '请选择填写范围',
                trigger: 'change'
              }
            ]
          },
          {
            label: '其余村干部可见数据',
            prop: 'powerType',
            type: 'switch',
            span: 24,
            display: this.$store.getters.permission.is_village_admin == true,
            dicData: [
              {
                label: '否',
                value: 0
              },
              {
                label: '是',
                value: 1
              },
            ],
            value: 0,
            rules: [
              {
                required: true,
                message: '请选择其余村干部可见数据',
                trigger: 'blur'
              }
            ]
          }
        ]
      },
      limitForm: {},
      submitIcon: '',
      draftIcon: '',
      draftDisabled: false,
      disabledBtn: false,

    }
  },
  methods: {
    formUploadAfter(res,done){
      console.log(res,'formUploadAfter')
      this.form.attachId=res.attachId
      done()
    },
    saveOption(option,row){
      console.log(this.form,'fff')
      if(option == 'saveDraft'){
        switch(this.pageType){
          case 1:
          case 3:
            this.saveDraft(row)
            break;
          case 2:
            this.updateDraft(row)
          break;
          default:
            this.saveDraft(row)
          break;

        }
      }else if (option == 'completeQuest'){
        switch(this.pageType){
          case 1:
          case 2:
          case 3:
            this.saveQuestion()
          break;
          case 4:
          case 5:
            this.saveTemplate()
          break;
          default:
            this.saveQuestion()
          break;
        }
      }
    },
    addQuestion (type) {
      // 第一次添加问题或编辑问卷第一次添加问题时，无需验证上一个问题是否添加正确
      let addItem = {}
      if (this.form.subjectList.length === 0 || JSON.stringify(this.componentOptions) === '{}') {
        //    this.questionnaireDefaults.questionsForm.display = true
        if (type !== 3) {
          addItem =
          {
            title: type === 1 ? '单选题' : '多选题',
            options: [{ title: '', attachLink: '' }],
            isRequired: 0,
            type: type,
            attachLink: ''
          }
        } else {
          addItem =
          {
            title: '简答题',
            isRequired: 0,
            type: type,
            prompt: '',
          }
        }
        this.isShowComponentForm = true
        this.form.subjectList.push(addItem)
        this.questionIndexFlag = this.form.subjectList.length - 1
        this.componentOptions = addItem
      } else {
        this.$refs.dynamicValidateForm.validate((valid) => {
          if (valid) {
            if (type !== 3) {
              addItem =
              {
                title: type === 1 ? '单选题' : '多选题',
                options: [{ title: '', attachLink: '' }],
                isRequired: 0,
                type: type,
                attachLink: ''
              }
            } else {
              addItem =
              {
                title: '简答题',
                isRequired: 0,
                type: type,
                prompt: ''
              }
            }
            this.isShowComponentForm = true
            this.form.subjectList.push(addItem)
            this.questionIndexFlag = this.form.subjectList.length - 1
            this.componentOptions = addItem
          }
        })
      }
      if (this.form.subjectList.length === 1) {
        this.$refs.questionnaireForm.clearValidate('subjectList')
      }
      this.$refs.question.scrollTop = this.$refs.question.scrollHeight

    },
    deleteQuestion (index) {
      this.form.subjectList.splice(index, 1)
      if (this.form.subjectList.length === 0) {
        //   this.questionnaireDefaults.questionsForm.display = false
        //  this.$set(this.componentOptions, 0, {})
        this.componentOptions = {}
        this.isShowComponentForm = false
      }
      if (this.questionIndexFlag === index) {
        this.componentOptions = {}
        this.isShowComponentForm = false
      }
      if (this.form.subjectList.length === 100) {
        this.$refs.questionnaireForm.clearValidate('subjectList')
      }
    },
    upQuestion (item) {
      let index = this.form.subjectList.indexOf(item)
      this.form.subjectList.splice(index, 1)
      this.form.subjectList.splice(index - 1, 0, item)
    },
    downQuestion (item) {
      let index = this.form.subjectList.indexOf(item)
      this.upQuestion(this.form.subjectList[index + 1])
    },
    isFullScreen () {
      this.isFullscreen = !this.isFullscreen
    },
    addQuestionForm (item, index) {
      this.questionIndexFlag = index
      this.isShowComponentForm = true
      this.componentOptions = item
    },
    handleClose () {
      if (this.showLimitDialog) {
        this.showLimitDialog = false
        this.pageTitle = '添加问卷'
      } else {
        this.$emit('update:showAddDialog', false)
      }

    },
    saveTemplate(){
        this.updateDraft()
    },
    saveDraft () {
      if (JSON.stringify(this.componentOptions) === '{}') {
        this.$refs.questionnaireForm.validate((valid, done) => {
          if (valid) {
            this.draftDisabled = true
            this.draftIcon = 'el-icon-loading'
            saveToDraft(this.form).then(res => {
              if (res.data.code === 200) {
                this.$message({
                  type: "success",
                  message: "操作成功!"
                });
                this.getDraftNum()
                this.onLoad(this.getPage())
                this.handleClose()
                this.draftDisabled = false
                this.draftIcon = ''
                done()
              } else {
                this.draftDisabled = false
                this.draftIcon = ''
              }

            })
              .catch(() => {
                this.draftDisabled = false
                this.draftIcon = ''
              })
          }
        })
      } else {
        this.$refs.dynamicValidateForm.validate((val) => {
          if (val) {
            this.$refs.questionnaireForm.validate((valid, done) => {
              if (valid) {
                this.draftDisabled = true
                this.draftIcon = 'el-icon-loading'
                saveToDraft(this.form).then(res => {
                  if (res.data.code === 200) {
                    this.$message({
                      type: "success",
                      message: "操作成功!"
                    });
                    this.getDraftNum()
                    this.handleClose()
                    this.draftDisabled = false
                    this.draftIcon = ''
                    done()
                  } else {
                    this.draftDisabled = false
                    this.draftIcon = ''
                  }

                })
                  .catch(() => {
                    this.draftDisabled = false
                    this.draftIcon = ''
                  })
              }
            })
          }
        })
      }


    },
    updateDraft () {
      let updateApi = this.pageType===4 ? addTemplate: this.pageType===5?updateTemplate :updateDraft
      if (JSON.stringify(this.componentOptions) === '{}') {
        this.$refs.questionnaireForm.validate((valid, done) => {
          if (valid) {
            this.draftDisabled = true
            this.draftIcon = 'el-icon-loading'
            
            updateApi(this.form).then(res => {
              if (res.data.code === 200) {
                this.$message({
                  type: "success",
                  message: "操作成功!"
                });
                this.getDraftNum()
                this.onLoad(this.getPage())
                this.handleClose()
                this.draftDisabled = false
                this.draftIcon = ''
                done()
              } else {
                this.draftDisabled = false
                this.draftIcon = ''
              }

            })
              .catch(() => {
                this.draftDisabled = false
                this.draftIcon = ''
              })
          }
        })
      } else {
        this.$refs.dynamicValidateForm.validate((val) => {
          if (val) {
            this.$refs.questionnaireForm.validate((valid, done) => {
              if (valid) {
                this.draftDisabled = true
                this.draftIcon = 'el-icon-loading'
                updateApi(this.form).then(res => {
                  if (res.data.code === 200) {
                    this.$message({
                      type: "success",
                      message: "操作成功!"
                    });
                    this.getDraftNum()
                    this.onLoad(this.getPage())
                    this.handleClose()
                    this.draftDisabled = false
                    this.draftIcon = ''
                    done()
                  } else {
                    this.draftDisabled = false
                    this.draftIcon = ''
                  }

                })
                  .catch(() => {
                    this.draftDisabled = false
                    this.draftIcon = ''
                  })
              }
            })
          }
        })

      }

    },
    saveQuestion () {
      if (JSON.stringify(this.componentOptions) === '{}') {
        this.$refs.questionnaireForm.validate((valid, done) => {
          if (valid) {
            this.pageTitle = '表单设置'
            this.showLimitDialog = true
            done()
          }
        })
      } else {
        this.$refs.dynamicValidateForm.validate((val) => {
          if (val) {
            this.$refs.questionnaireForm.validate((valid, done) => {
              if (valid) {
                this.pageTitle = '表单设置'
                this.showLimitDialog = true
                done()
              }
            })
          }
        })

      }


    },
    saveQuestionnaire () {
      this.$refs.limitForm.validate((valid, done) => {
        if (valid) {
          this.disabledBtn = true
          this.submitIcon = 'el-icon-loading'
          const data = Object.assign(this.form, this.limitForm)
          console.log('774', data);
          data.fillType = this.form.fillType.join(',')
          saveQuestionnaire(data).then(res => {
            if (res.data.code === 200) {
              this.$message({
                type: "success",
                message: "操作成功!"
              })
              this.disabledBtn = false
              this.submitIcon = ''
              this.onLoad(this.getPage())
              this.$emit('update:showAddDialog', false)
              if (this.pageType === 2) {
                this.getDraftNum()
              }
            } else {
              this.disabledBtn = false
              this.submitIcon = ''
            }
          })
            .catch(() => {
              this.disabledBtn = false
              this.submitIcon = ''
            })
        }
        done()
      })

    },
    addOption () {
      // const _length = this.componentOptions.options.length
      // if( _length!=0 && this.componentOptions.options[_length-1].title === '') {
      //     return
      // }
      this.componentOptions.options.push({
        title: '',
        attachLink: ''
      });
      if (this.componentOptions.options.length === 2) {
        this.$refs.dynamicValidateForm.clearValidate('options')
      }
    },
    removeOption (item) {
      var index = this.componentOptions.options.indexOf(item)
      if (index !== -1) {
        this.componentOptions.options.splice(index, 1)
      }
      if (this.componentOptions.type === 1 && this.componentOptions.options.length === 5) {
        this.$refs.dynamicValidateForm.clearValidate('options')
      }
      if (this.componentOptions.type === 2 && this.componentOptions.options.length === 10) {
        this.$refs.dynamicValidateForm.clearValidate('options')
      }

    },
    handleAvatarSuccess (res) {
      console.log('782', res);
      
      //  this.componentOptions.options[this.indexFlag].attachLink = res.data.link;
      this.$set(this.componentOptions.options[this.indexFlag], 'attachLink', res.data.link) // 避免没有实时更新
      // this.$set(this.componentOptions.options[this.indexFlag], 'attachId', res.data.attachId) // 避免没有实时更新
    },
    beforeAvatarUpload (file) {
      const isJPEG = file.type === 'image/jpeg';
      const isPNG = file.type === 'image/png';
      const isBMP = file.type === 'image/bmp'
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isPNG && !isBMP && !isJPEG) {
        this.$message.error('上传图片格式不正确!');
      }
      if (!isLt10M) {
        this.$message.error('上传图片大小不能超过 10MB!');
      }
      this.headers[this.website.tokenHeader] = 'bearer '+  getToken()
      this.headers['Authorization'] = `Basic ${Base64.encode(`${this.website.clientId}:${this.website.clientSecret}`)}`;
      this.headers['Blade-Requested-With'] = 'BladeHttpRequest';
      console.log(this.website,'this.website')
      return (isJPEG || isBMP || isPNG) && isLt10M
    },
    // 获取上传选项图片的index
    getFlagIndex (index) {
      this.indexFlag = index
    },
    handleRemove () {
      this.componentOptions.options[this.indexFlag].attachLink = ''
    },
    formUploadBefore (file, done, loading) {
      const isJpgOrPng =
        file.type === "image/jpeg" ||
        file.type === "image/png"
      if (!isJpgOrPng) {
        this.$message.error("上传图片格式不正确!");
        loading();
        return;
      }
      const isLt10M = file.size < 10 * 1024 * 1024;
      if (!isLt10M) {
        this.$message.error("上传图片大小不能超过 10MB!");
        loading();
        return;
      }
      done()
    }
  }
}
</script>
<style scoped>
.inputDeep>>>.el-input__inner {
  border: 0
}

.questionForm>>>.el-form-item__label {
  text-align: left;
}

.questionForm>>>.el-form-item {
  margin-bottom: 0;
}

.componentForm>>>.el-upload {
  display: block;
}

.error_width>>>.el-form-item__error {
  margin-left: -50px;
}

.question-form-flow {
  height: 55pc;
  overflow-y: auto;
}

.ml-20p {
  margin-left: 20px;
}

.word_break {
  white-space: normal;
  display: inline-block;
  vertical-align: middle;
}
</style>

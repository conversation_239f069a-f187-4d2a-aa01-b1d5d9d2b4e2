/*
 * @Author: zhouwj83
 * @Date: 2021-06-22 10:26:17
 * @LastEditors: linzq33
 * @LastEditTime: 2022-07-06 16:21:11
 * @Description: 户籍管理
 */
import request from '@/router/axios';

/**
 * @description 获取户籍管理列表
 * @param {number} current 
 * @param {number} size 
 * @param {object} params 
 */
export const getList = (current, size, params) => {
  return request({
    url: '/api/census/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

/**
 * @description 获取户籍管理详情
 * @param {number} id 
 * @param {boolean} isHide  
 */
export const getDetail = (id, isHide) => {
  return request({
    url: '/api/census/detail',
    method: 'get',
    params: {
      id,
      isHide
    }
  })
}

/**
 * @description 移除户籍管理
 * @param {string} ids
 */
export const remove = (ids) => {
  return request({
    url: '/api/census/remove',
    method: 'delete',
    params: {
      ids,
    }
  })
}
/**
 * @description 添加户籍管理
 * @param {object} row
 */
export const add = (row) => {
  return request({
    url: '/api/census/submit',
    method: 'post',
    data: row
  })
}
/**
 * @description 更新户籍管理
 * @param {object} row
 */
export const update = (row) => {
  return request({
    url: '/api/census/submit',
    method: 'post',
    data: row
  })
}

/**
 * @description 获取成员详情
 * @param {string} id
 * @param {boolean} isView
 */
export const getVillagerDetail = (id, isView) => {
  return request({
    url: '/api/villager/detail',
    method: 'get',
    params: {
      id,
      isView
    }
  })
}

/**
 * @description 获取成员管理列表
 * @param {number} current 
 * @param {number} size 
 * @param {object} params 
 */
export const getMemberList = (id) => {
  return request({
    url: '/api/census/all-families',
    method: 'get',
    params: {
      id
    }
  })
}

/**
 * @description 删除村民
 * @param {string} ids
 */
export const removeVillager = (ids) => {
  return request({
    url: '/api/villager/remove',
    method: 'delete',
    params: {
      ids,
    }
  })
}
/**
 * @description 添加村民
 * @param {object} row
 */
export const addVillager = (row) => {
  return request({
    url: '/api/villager/submit',
    method: 'post',
    data: row
  })
}
/**
 * @description 更新村民
 * @param {object} row
 */
export const updateVillager = (row) => {
  return request({
    url: '/api/villager/submit',
    method: 'post',
    data: row
  })
}
// 成员列表
export const getVillagerList = (current, size, params) => {
  return request({
    url: '/api/villager/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

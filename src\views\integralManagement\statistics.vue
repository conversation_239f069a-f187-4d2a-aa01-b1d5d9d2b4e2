<!--
 * @Author: wangyy553
 * @Date: 2021-10-18 11:41:09
 * @LastEditTime: 2022-01-21 16:26:30
 * @LastEditors: wangyy553
 * @Description: 积分管理
-->
<template>
  <el-row>
    <el-col :span="5">
      <div class="box">
        <el-scrollbar>
          <basic-container>
            <avue-tree
              :option="treeOption"
              :data="treeData"
              @node-click="nodeClick"
              :filter-node-method="filterNodeMethod"
            />
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :span="19">
      <basic-container>
        <avue-crud
          :option="option"
          :table-loading="loading"
          :data="data"
          :page.sync="page"
          v-model="form"
          ref="crud"
          @on-load="onLoad"
          @refresh-change="refreshChange"
          @current-change="currentChange"
          @size-change="sizeChange"
          @search-change="searchChange"
          @search-reset="searchReset"
          @sort-change="sortChange"
        >
          <template slot-scope="scope" slot="menu">
            <el-button
              icon="el-icon-view"
              :size="scope.size"
              :type="scope.type"
              @click="openview(scope.row)"
            >
              查看
            </el-button>
          </template>
          <template slot="menuLeft" slot-scope="scope">
            <el-button
              :size="scope.size"
              type="primary"
              :disabled="disableButton"
              @click="exportReg()"
            >
              <i :class="icon"></i>导 出</el-button
            ></template
          >
        </avue-crud>
        <integral-record
          :visible.sync="visible"
          :userId="openedUserId"
          ref="view"
        ></integral-record>
      </basic-container>
    </el-col>
  </el-row>
</template>

<script>
import { getDataList } from "@/api/integralManagement/statistics";
import integralRecord from "./components/integralRecord.vue";
import { getDeptTree } from "@/api/system/dept";
import { handleDownload } from "@/util/download";
import { debounce } from "lodash";
import { mapGetters } from "vuex";

export default {
  components: { integralRecord },
  data() {
    return {
      visible: false,
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      treeDeptId: "",
      treeData: [],
      treeOption: {
        nodeKey: "id",
        addBtn: false,
        menu: false,
        size: "small",
        props: {
          labelText: "标题",
          label: "title",
          value: "value",
          children: "children",
        },
        // defaultExpandAll:true,
      },
      option: {
        index: true,
        indexLabel: "序号",
        indexWidth: 70,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: false,
        editBtn: false,
        addBtn: false,
        viewBtn: false,
        delBtn: false,
        saveBtn: false,
        updateBtn: false,
        cancelBtn: false,
        menu: true,
        dialogWidth: 900,
        dialogClickModal: false,
        column: [
          {
            label: "姓名",
            prop: "realName",
            width: 100,
          },
          {
            label: "账号",
            prop: "account",
            search: true,
            maxlength: 20,
            width: 100,
          },
          {
            label: "总积分",
            prop: "totalIntegral",
            sortable: true,
            width: 110,
          },
          {
            label: "今日获取积分",
            prop: "todayIntegral",
            sortable: true,
            width: 130,
          },
          {
            label: "所属机构",
            prop: "fullDeptName",
          },
        ],
      },
      data: [],
      openedUserId: null,
      query: {},
      form: {},
      descs: "",
      ascs: "",
      disableButton: false,
      icon: "el-icon-download el-icon--right",
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  created() {
    this.initData();
  },
  methods: {
    initData() {
      getDeptTree(this.userInfo.tenant_id).then((res) => {
        this.treeData = res.data.data;
      });
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const deptId = this.treeDeptId || "";
      getDataList(
        deptId,
        page.currentPage,
        page.pageSize,
        this.descs,
        this.ascs,
        Object.assign(params, this.query)
      ).then((res) => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
      });
    },
    refreshChange() {
      this.onLoad(this.page);
    },
    /**
     * @description:导出
     * @author: wangyy553
     */
    exportReg() {
      this.$confirm("是否导出积分数据?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        this.exportFun();
      });
    },
    exportFun: debounce(async function () {
      this.icon = "el-icon-loading el-icon--right";
      this.disableButton = true;
      //请求文件
      var params = {};
      params.account = this.query.account || "";
      params.deptId = this.treeDeptId || "";
      // let params = {};
      const url = "/api/integral/admin/export";
      const result = await handleDownload(url, params);
      console.log(params);
      if (result != null) {
        this.icon = "el-icon-download el-icon--right";
        this.disableButton = false;
      }
    }, 200),
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    /**
     * @description: 打开弹窗
     * @param {object} row
     * @author: wangyy553
     */
    openview(row) {
      this.openedUserId = row.userId;
      this.$nextTick(() => {
        this.$refs.view.onload();
        this.visible = true;
      });
    },

    filterNodeMethod(value, data) {
      if (!value) return true;
      return data.title.indexOf(value.trim()) !== -1;
    },
    nodeClick(data) {
      this.treeDeptId = data.id;
      this.page.currentPage = 1;
      this.descs = "";
      this.ascs = "";
      this.$refs.crud.clearSort();
      this.searchReset();
    },
    sortChange(val) {
      if (val.order === "ascending") {
        this.ascs = val.prop;
        this.descs = "";
      } else if (val.order === "descending") {
        this.descs = val.prop;
        this.ascs = "";
      } else {
        this.descs = "";
        this.ascs = "";
      }
      this.onLoad(this.page);
    },
  },
};
</script>

<style scoped>
.box {
  height: 800px;
}
.box .el-scrollbar__wrap {
  overflow: scroll;
}
</style>

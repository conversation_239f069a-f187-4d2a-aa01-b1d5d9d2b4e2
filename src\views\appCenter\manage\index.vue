<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" ref="crud" v-model="form" :page.sync="page"
      :permission="permissionList" :before-open="beforeOpen" :before-close="beforeClose" @row-del="rowDel"
      @row-update="rowUpdate" @row-save="rowSave" @search-change="searchChange" @search-reset="searchReset"
      @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange"
      :upload-after="uploadAfter" :upload-before="uploadBefore" :upload-error="uploadError"
      @refresh-change="refreshChange" @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger" size="small" icon="el-icon-delete" v-if="permission.appmanage_delete" plain
          @click="handleDelete">批量删除
        </el-button>
      </template>
      <template slot-scope="{row}" slot="isOpen">
        <el-button plain size="mini" v-if="row.isOpen === 1" title="点击禁用"
          @click="openAndClose(row.id, 0)">启用</el-button>
        <el-button plain size="mini" type="danger" v-else title="点击启用" @click="openAndClose(row.id, 1)">禁用</el-button>
      </template>
      <!-- 图标 -->
      <template slot-scope="{row}" slot="iconUrl">
        <div style="text-align:center">
          <el-image :src="row.iconUrl" style="height: 96px;" :preview-src-list="[row.iconUrl]" />
        </div>
      </template>
      <!-- 行操作按钮 -->
      <template slot-scope="{ type, size, row, index }" slot="menu">
        <el-button :type="type" :size="size" @click="$refs.crud.rowEdit(row, index)" icon="el-icon-edit" v-if="
          row.isOpen === 0 && permissionList.editBtn
        ">
          编 辑
        </el-button>
        <el-button :type="type" :size="size" @click="rowDel(row)" v-if="permissionList.delBtn" icon="el-icon-delete">
          删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getAppList, appDel, appUpdate, appSave, appOpenAndClose, getAppDet } from "@/api/appCenter/";
import { mapGetters } from "vuex";
import { deepClone } from '@/util/util'
function validator (value, tip, callback) {
  if (value === '') {
    callback(new Error(tip));
  } else if (/^\s*$/.test(value)) {
    callback(new Error(tip));
  } else {
    callback();
  }
}
export default {
  data () {
    return {
      form: {
        status: 1
      },
      query: {},
      loading: true,
      selectionList: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        tip: false,
        simplePage: true,
        searchShow: true,
        height: "auto",
        searchMenuSpan: 6,
        dialogWidth: "60%",
        border: true,
        index: true,
        selection: true,
        viewBtn: true,
        delBtn: false, // 默认是有删除、编辑按钮
        editBtn: false,
        menuWidth: 300,
        dialogClickModal: false,
        column: []
      },
      column: [
        {
          label: "应用名称",
          prop: "name",
          search: true,
          maxlength: 100,
          rules: [
            {
              required: true,
              validator: (rule, value, callback) => validator(value, '请输入应用名称', callback),
              trigger: ['blur', 'change']
            }
          ]
        },
        {
          label: "应用类型",
          prop: "type",
          type: "select",
          search: true,
          dicData: [
            { label: "小程序", value: 3 },
            { label: "H5", value: 2 },
            { label: "APP", value: 4 },
            { label: "WEB", value: 1 }
          ],
          rules: [
            {
              required: true,
              message: "请选择应用类型",
              trigger: "change"
            }
          ],
          change: ({ column, value }) => {
            this.handleAppType(value)
          }
        },
        // 特殊字段
        {
          label: "应用图标",
          prop: "iconUrl",
          slot: true,
          display: false,
        },
        {
          label: "应用排序",
          prop: "sort",
          type: "number",
          max: 999,
          min: 1,
          rules: [
            {
              required: true,
              message: "请输入应用排序",
              trigger: "blur"
            }
          ]
        },
        {
          label: "是否启用",
          prop: "status",
          type: "radio",
          dicData: [
            { label: "是", value: 1 },
            { label: "否", value: 0 }
          ],
          hide: true,
          rules: [
            {
              required: true,
              message: "请选择是否启用",
              trigger: "change"
            }
          ],
        },
        {
          label: "最近更新时间",
          prop: "updateTime",
          display: false,
        },
        {
          label: "状态",
          prop: "isOpen",
          slot: true,
          display: false,
        },
        {
          label: '应用图标',
          prop: 'iconlink',
          type: 'upload',
          loadText: '上传中，请稍等',
          listType: 'picture-img',
          accept: '.jpeg,.jpg,.png',
          span: 24,
          propsHttp: {
            res: 'data',
            url: 'link',
          },
          action: "/api/blade-resource/oss/endpoint/put-file-attach",
          rules: [
            {
              required: true,
              message: '请选择应用图标',
              trigger: 'change'
            }
          ],
          hide: true,
        },
      ],
      // 动态字段
      jumpField: {
        label: "跳转地址",
        prop: "jumpAddress",
        type: 'input',
        maxlength: 100,
        span: 24,
        rules: [
          {
            required: true,
            validator: (rule, value, callback) => {
              const urlregex = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
              if (value === '') {
                callback(new Error('请输入跳转地址'));
              } else if (!urlregex.test(value)) {
                callback(new Error('请输入正确的跳转地址'));
              } else {
                callback();
              }
            },
            trigger: ['blur', 'change']
          }
        ],
        hide: true,
      },
      APPIDField: {
        label: "小程序ID",
        prop: "miniProgramId",
        type: 'input',
        maxlength: 100,
        rules: [
          {
            validator: (rule, value, callback) => validator(value, '请输入小程序ID', callback),
            trigger: ['blur', 'change']
          }
        ],
        hide: true,
      },
      noteField: {
        label: "指引说明",
        prop: "guidanceNote",
        type: 'textarea',
        maxlength: 500,
        span: 24,
        rules: [
          {
            validator: (rule, value, callback) => validator(value, '请输入指引说明', callback),
            trigger: ['blur', 'change']
          }
        ],
        hide: true,
      },
      isJumpField: {
        label: "是否跳转",
        prop: "isJump",
        type: "radio",
        dicData: [
          { label: "是", value: 1 },
          { label: "否", value: 0 }
        ],
        hide: true,
        rules: [
          {
            required: true,
            message: "请选择是否跳转",
            trigger: "change"
          }
        ],
        change: ({ column, value }) => {
          this.handleH5Jump(value)
        }
      },
      data: []
    };
  },
  watch: {
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList () {
      return {
        addBtn: this.vaildData(this.permission.appmanage_add, false),
        viewBtn: this.vaildData(this.permission.appmanage_view, false),
        delBtn: this.vaildData(this.permission.appmanage_delete, false),
        editBtn: this.vaildData(this.permission.appmanage_edit, false)
      };
    },
    ids () {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created () {
    // 赋值默认列表参数
    this.option.column = deepClone(this.column)
  },
  methods: {
    rowSave (row, done, loading) {
      const params = {
        ...row,
        isOpen: row.status
      };

      appSave(params).then((res) => {
        // 获取新增数据的相关字段
        const data = res.data.data;
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        // 数据回调进行刷新
        this.onLoad(this.page, this.query);
        done();
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowUpdate (row, index, done, loading) {
      const params = {
        ...row,
        isOpen: row.status
      };
      appUpdate(params).then(() => {
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        // 数据回调进行刷新
        this.onLoad(this.page, this.query);
        done();
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowDel (row, index, done) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return appDel(row.id);
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          // 数据回调进行刷新
          this.onLoad(this.page, this.query);
          done(row);
        });
    },
    handleDelete () {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return appDel(this.ids);
        })
        .then(() => {
          // 刷新表格数据并重载
          this.data = [];
          this.$refs.crud.refreshTable();
          if (this.$refs.crud.toggleSelection) this.$refs.crud.toggleSelection();
          // 表格数据重载
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    searchReset () {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange (params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange (list) {
      this.selectionList = list;
    },
    selectionClear () {
      this.selectionList = [];
      if (this.$refs.crud.toggleSelection) {
        this.$refs.crud.toggleSelection();
      }
    },
    beforeOpen (done, type) {
      if (["add", "edit"].includes(type)) {

      }
      if (["edit", "view"].includes(type)) {
        getAppDet(this.form.id).then((res) => {
          this.form = res.data.data;

          this.form.iconlink = res.data.data.iconUrl
          this.form.status = res.data.data.isOpen
        });
        // 判断应用类型，显示对应特殊字段'
        this.handleAppType(this.form.type)
      }
      done();
    },
    handleAppType (type) {
      this.option.column = deepClone(this.column)
      if (this.$refs.crud.clearValidate) {
        this.$refs.crud.clearValidate();
      }

      if (type == 1) {
        // web, 跳转地址字段
        this.option.column.splice(2, 0, this.jumpField)
      } else if (type == 2) {
        this.option.column.splice(2, 0, this.isJumpField)
        // this.option.column.splice(3, 0, this.jumpField)
        this.option.column.splice(3, 0, this.noteField)
      } else if (type == 3) {
        // 小程序，Appid
        this.option.column.splice(2, 0, this.APPIDField)
      } else if (type == 4) {
        // app，指引说明
        this.option.column.splice(2, 0, this.noteField)
      }
    },
    // 判断h5是否跳转
    handleH5Jump (type) {
      if (type) {
        this.option.column.splice(3, 0, this.jumpField)
      } else {
        // 移除
        const column = this.findObject(this.option.column, "jumpAddress");
        if (column) {
          this.option.column.splice(this.option.column.indexOf(column), 1)
        }
      }
    },
    beforeClose (done) {
      done();
    },
    currentChange (currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange (pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange () {
      this.onLoad(this.page, this.query);
    },
    onLoad (page, params = {}) {
      this.loading = true;
      getAppList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.loading = false;
        this.page.total = data.total;
        this.data = data.records;
        this.selectionClear();
      });
    },
    /**
     * 上传前回调，控制大小类型，限制只能上传一张
     */
    uploadBefore (file, done, loading) {
      const sizeOK = file.size <= 1024 * 1024 * 50 // 10m
      const typeOK = file.type === 'image/png' || file.type === 'image/jpg' || file.type === 'image/jpeg' // 限制类型
      if (!sizeOK) {
        this.$message.warning('图标不能超过50MB')
        loading()
        return
      }
      if (!typeOK) {
        this.$message.warning('文件类型只能是png/jpg/jpeg')
        loading()
        return
      }
      if (this.form.imgUrl && this.form.imgUrl.length > 0) {
        this.$message.warning('图片仅能上传一张，请删除已上传的图片后再操作')
        loading()
      } else {
        // 如果你想修改file文件,由于上传的file是只读文件，必须复制新的file才可以修改名字，完后赋值到done函数里,如果不修改的话直接写done()即可
        // var newFile = new File([file], file.name, { type: file.type });
        done()
      }
    },
    uploadAfter (res, done) {
      // handleLink是form展示的[{label,value}],handleIds是记录每次上传完的id, handleLinks是存储提交需要的数据
      if (!res || !res.attachId) {
        this.$message.error("上传失败");
        loading();
      } else {
        this.$message.success("上传成功");
        this.form.iconId = res.attachId
        this.$nextTick(() => {
          this.$refs.crud.validateField('iconlink');
        });
        done()
      }
    },
    uploadError (error) {
      this.$message.success('上传失败!' + error)
    },
    openAndClose (id, status) {
      const tip = status ? '请确认是否将该应用启用？' : '请确认是否将该应用禁用？'
      this.$confirm(tip, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        appOpenAndClose(id).then(res => {
          this.onLoad(this.page, this.query);
        })
      });

    }
  }
};
</script>

<style scoped>
:global(.avue-upload__avatar) {
  line-height: 148px !important;
}
</style>

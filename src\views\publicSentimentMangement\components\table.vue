<!--
 * @Description: 民情管理
 * @Author: lins14
 * @Date: 2021-10-28 10:34:50
 * @LastEditors: linzq33
 * @LastEditTime: 2025-07-16 11:36:34
-->
<template>
  <basic-container>
    <avue-crud ref="crud" :option="option" :table-loading="loading" :data="data" :page.sync="page"
      :permission="permissionList" v-model="form" :before-open="beforeOpen" @row-del="deleteC"
      @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
      @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <template slot="menuLeft">
        <el-button v-if="activeName === '2'" type="danger" size="small" icon="el-icon-delete" plain
          @click="deleteC(2)">批量删除</el-button>
      </template>
      <template slot-scope="scope" slot="attachVOSListForm">
        <p style="display: none">{{ scope }}</p>
        <p v-for="(item, i) in form.attachVOSList" :key="i" class="copy-link" @click="downLoad(item)">
          {{ item.name }}
        </p>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList, getDetail, del } from '@/api/publicSentimentMangement/index'
import { mapGetters } from 'vuex'

export default {
  props: {
    activeName: {
      type: String,
      default: '0'
    }
  },
  data () {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchSpan: 6,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: true,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        selection: true,
        menuWidth: 160,
        labelWidth: 100,
        dialogWidth: 880,
        dialogClickModal: false,
        column: [
          {
            label: '标题',
            prop: 'title',
            type: 'textarea',
            maxlength: 100,
            span: 24,
            search: true,
            required: true
          },
          {
            label: '诉求人',
            prop: 'accuser',
            span: 12,
            search: true
          },
          {
            label: '联系电话',
            prop: 'phone',
            span: 12,
            required: true
          },
          {
            label: '诉求时间',
            prop: 'updateTimeRange',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            searchRange: true,
            hide: true,
            display: false,
            span: 12,
            search: true
          },
          {
            label: '诉求时间',
            prop: 'startTime',
            type: 'date',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            // overHidden: true,
            span: 12,
            search: false
          },
          {
            label: '是否公开',
            type: 'select',
            prop: 'isOpened',
            span: 12,
            dicData: [
              { label: "是", value: 1 },
              { label: "否", value: 0 },
            ],
            dataType: 'string',
            search: true
          },
          {
            label: '诉求详情',
            prop: 'detail',
            type: 'textarea',
            span: 24,
            search: false,
            maxRows: 3,
            minRows: 1,
            hide: true
          },
          {
            label: '图片',
            prop: 'imageVOSList',
            type: 'upload',
            dataType: 'array',
            listType: 'picture-card',
            span: 24,
            display: true,
            disabled: true,
            hide: true
          },
          {
            label: '附件',
            prop: 'attachVOSList',
            span: 24,
            display: true,
            hide: true
          },
          {
            label: '处理流程',
            prop: 'accessList',
            type: 'dynamic',
            labelWidth: 0,
            hide: true,
            viewDisplay: false,
            span: 24,
            children: {
              align: 'center',
              type: 'form',
              index: false,
              headerAlign: 'center',
              column: [
                {
                  label: '处理人',
                  prop: 'userName',
                  span: 24,
                  formslot: true
                },
                {
                  label: '处理操作',
                  prop: 'operation',
                  type: 'select',
                  dicData: [
                    { label: "转办", value: 1 },
                    { label: "办结", value: 2 },
                  ],
                  span: 12,
                  formslot: true
                },
                {
                  label: '处理时间',
                  prop: 'updateTime',
                  type: 'date',
                  format: 'yyyy-MM-dd HH:mm:ss',
                  valueFormat: 'yyyy-MM-dd HH:mm:ss',
                  span: 12,
                  formslot: true
                },
                {
                  label: '处理意见',
                  prop: 'detail',
                  formslot: true,
                  type: 'textarea',
                  span: 24,
                  maxRows: 3,
                  minRows: 1
                }
              ]
            }
          }
        ]
      },
      endTime: {
        label: '办结时间',
        prop: 'endTime',
        type: 'date',
        format: 'yyyy-MM-dd HH:mm:ss',
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        span: 24,
        viewDisplay: false
      },
      data: [],
      deptId: '',
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'permission']),
    permissionList () {
      return {}
      //   return {
      //     // addBtn: this.vaildData(this.permission.oss_add),
      //     viewBtn: this.vaildData(this.permission.oss_view),
      //     delBtn: this.vaildData(this.permission.oss_delete),
      //     addBtn: false,
      //     // editBtn: this.vaildData(this.permission.oss_edit)
      //     // editBtn: false,
      //   };
    },
    ids () {
      const ids = []
      this.selectionList.forEach(ele => {
        ids.push(ele.id)
      })
      return ids.join(',')
    }
  },
  watch: {
    activeName (newV, oldV) {
      this.$refs.crud.searchReset()
      const accessList = this.findObject(this.option.column, 'accessList')
      if (newV === '2') {
        this.option.delBtn = true
        this.option.column.splice(5, 0, this.endTime)
        accessList.viewDisplay = true
      } else if (oldV == 2) {
        this.option.delBtn = false
        accessList.viewDisplay = false
        this.option.column.splice(5, 1)
      }
      this.page.currentPage = 1
      this.onLoad(this.page)
    }
  },
  methods: {
    searchReset () {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange (params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange (list) {
      this.selectionList = list
    },
    selectionClear () {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    /**
     * 开启前回调，通过详情接口获取处理记录
     */
    beforeOpen (done, type) {
      if (['edit', 'view'].includes(type)) {
        getDetail(this.form.id).then(res => {
          if (res.data.code === 200) {
            this.form = res.data.data
            const imageVOSList = []
            this.form.imageList.forEach(fivos => {
              imageVOSList.push(fivos.link)
            })
            this.form.imageVOSList = imageVOSList
            // 处理附件文件
            const attachVOSList = []
            this.form.fileList.forEach(favos => {
              attachVOSList.push({
                name: favos.originalName,
                link: favos.link
              })
            })
            this.form.attachVOSList = attachVOSList
          }
        }).finally(() => {
          done()
        })
      }
    },
    currentChange (currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange (pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange () {
      this.onLoad(this.page, this.query)
    },
    /**
     * 删除
     */
    deleteC (n) {
      let notText = ''
      let p = ''
      if (n === 2) {
        // 批量删除
        notText = '选中数据'
        if (this.selectionList.length === 0) {
          this.$message.warning('请选择至少一条数据')
          return false
        }
        p = this.ids
      } else {
        // 单一删除
        notText = '该数据'
        p = n.id
      }
      this.$confirm(`此操作将删除${notText}, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          del(p).then(res => {
            if (res.data.code === 200) {
              this.$message({
                type: 'success',
                message: '操作成功'
              })
              this.$refs.crud.closeDialog()
              this.onLoad(this.page)
            } else {
              this.$message.error('操作失败!' + res.data.msg)
            }
          })
        })
        .catch(() => { })
    },
    /**
     * 下载
     */
    downLoad (item) {
      const a = document.createElement('a')
      fetch(item.link)
        .then(res => res.blob())
        .then(blob => {
          // 将链接地址字符内容转变成blob地址
          a.href = URL.createObjectURL(blob)
          a.download = item.name // 下载文件的名字
          document.body.appendChild(a)
          a.click()
        })
    },
    onLoad (page, params = {}) {
      this.loading = true
      if (!params.title) {
        params.title = ''
      }
      switch (this.activeName) {
        case '0':
          // 待处理
          this.query.status = 0
          break
        case '1':
          // 处理中
          this.query.status = 1
          break
        case '2':
          // 已办结
          this.query.status = 2
          break
        default:
          break
      }
      if (this.query.updateTimeRange) {
        // 处理诉求时间
        params.startTime = this.query.updateTimeRange[0]
        params.endTime = this.query.updateTimeRange[1]
      }
      const p = Object.assign(params, this.query)
      delete p.updateTimeRange
      getList(page.currentPage, page.pageSize, p, this.deptId).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    }
  }
}
</script>
<style>
.copy-link {
  text-decoration: underline;
  color: #0000cc;
}
</style>

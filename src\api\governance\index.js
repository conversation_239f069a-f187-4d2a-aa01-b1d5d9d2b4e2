/*
 * @Description: 随手拍和曝光台
 * @Author: lim126
 * @Date: 2021-09-13 10:51:41
 * @LastEditors: linqh21
 * @LastEditTime: 2025-03-28 14:30:05
 * @FilePath: \src\api\governance\index.js
 ** @module index
 */
import request from '@/router/axios'

/**
 * @function
 * @desc 获取随手拍列表分页
 * @param {number} current
 * @param {number} size
 * @param {{}} params
 * @returns {Promise<ResponseData>}
 */
export const getPatList = (current, size, params) => {
  return request({
    url: '/api/snapshot/page',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  })
}
/**
 * @desc 根据id获取随手拍详情
 * @param {string} id 随手拍id
 * @returns {Promise<ResponseData>}
 */
export const getPatDetail = id => {
  return request({
    url: '/api/snapshot/detail',
    method: 'get',
    params: {
      id
    }
  })
}

/**
 * @desc 受理随手拍
 * @param {string} id 随手拍id
 * @param {number} status 前端默认写1,将状态由未受理0改为受理中1
 * @returns {Promise<ResponseData>}
 */
export const acceptPat = (id, status) => {
  return request({
    url: `/api/snapshot/accept/${id}`,
    method: 'post',
    params: {
      // id,
      status
    }
  })
}

/**
 * @desc 随手拍办结
 * @param {string} id 随手拍id
 * @param {{}} data 办结必填信息
 * @returns {Promise<ResponseData>}
 */
export const rowUpdate = (id, data) => {
  return request({
    url: `/api/snapshot/update/${id}`,
    method: 'post',
    data
  })
}
/**
 * @desc 获取字典曝光台信息类别
 * @returns {Promise<ResponseData>}
 */
export const getGovernanceCategory = () => {
  return request({
    url: '/api/blade-system/dict-biz/dictionary?code=lightshot_type',
    method: 'get'
  })
}
/**
 * @desc 获取曝光台信息列表
 * @param {number} current
 * @param {number} size
 * @param {{}} params 查询信息
 * @param {number} typeKey 信息类别
 * @returns {Promise<ResponseData>}
 */
export const getExposureList = ( params) => {
  return request({
    url: '/api/lighthouse/list',
    method: 'get',
    params,
  })
}

/**
 * @description 获取曝光台详情
 * @param {number} id 曝光台id
 * @returns {Promise<ResponseData>}
 */
export const getExposureDetail = id => {
  return request({
    url: '/api/lighthouse/detail',
    method: 'get',
    params: {
      id
    }
  })
}

/**
 * @description 添加曝光台
 * @param {{}} data 曝光台信息数据
 * @returns {Promise<ResponseData>}
 */
export const addExposure = data => {
  return request({
    url: '/api/lighthouse/submit',
    method: 'post',
    data
  })
}

/**
 * @description 更新曝光台
 * @param {number} id 曝光台id
 * @param {{}} data 曝光台信息数据
 * @returns {Promise<ResponseData>}
 */
export const updateExposure = (id, data) => {
  return request({
    url: `/api/lighthouse/update/${id}`,
    method: 'post',
    data
  })
}

/**
 * @description 移除曝光台
 * @param {number} ids 曝光台id,原本设计可以多项删除
 * @returns {Promise<ResponseData>}
 */
export const removeExposure = ids => {
  return request({
    url: '/api/lighthouse/remove',
    method: 'post',
    params: {
      ids
    }
  })
}

/**
 * @description: 曝光台发布 取消发布
 * @param {object} params
 * @author: wangyy553
 */
 export const updateList = (params) => {
  return request({
    url: '/api/lighthouse/release',
    method: 'post',
    params,
  })
}
/**
 * @description: 获取机构树形列表
 * @param {string} tenantId
 */
 export const getDeptTree = (tenantId) => {
  return request({
    url: '/api/blade-system/dept/treeFive',
    method: 'get',
    params: {
      tenantId,
    }
  })
}
/**
 * @description: 获取5级树形结构
 * @param {String} tenantId
 */
export const getDeptTreeFive = (tenantId) => {
  return request({
    url: '/api/blade-system/dept/treeFive',
    method: 'get',
    params: {
      tenantId
    }
  })
}

// 处理人列表
export const getHandlerList = () => {
  return request({
    url: '/api/snapshot/handler',
    method: 'get',
  })
}

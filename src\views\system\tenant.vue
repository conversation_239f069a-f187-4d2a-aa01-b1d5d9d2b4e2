<!--
 * @Date: 2025-06-24 09:38:28
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-21 14:48:35
 * @Description:
 * @FilePath: \src\views\system\tenant.vue
-->
<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" ref="crud" v-model="form" :page.sync="page" :permission="permissionList" :before-open="beforeOpen"  @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <template slot="menuLeft">
        <el-tooltip class="item" effect="dark" content="给租户配置账号额度、过期时间等授权信息" placement="top">
          <el-button size="small" plain type="info" v-if="userInfo.role_name.includes('administrator') && !recycleMode" icon="el-icon-setting" @click="handleSetting">批量授权配置
          </el-button>
        </el-tooltip>
      </template>
      <template slot="menuRight">
        <el-tooltip class="item" effect="dark" content="将自定义的菜单集合定制为租户绑定的菜单产品包" placement="top">
          <el-button size="small" plain v-if="userInfo.role_name.includes('administrator') && !recycleMode" icon="el-icon-set-up" @click="handlePackageSetting">推荐包管理
          </el-button>
        </el-tooltip>
      </template>
      <template #menu="scope">
        <el-button v-if="userInfo.role_name.includes('administrator') && !recycleMode" type="text" icon="el-icon-notebook-1" size="small" @click.stop="handleRowPackage(scope.row)">功能清单
        </el-button>
      </template>
      <template slot-scope="{row}" slot="accountNumber">
        <el-tag>{{ row.accountNumber > 0 ? row.accountNumber : '不限制' }}</el-tag>
      </template>
      <template slot-scope="{row}" slot="expireTime">
        <div v-if="row.expireTime">
          <el-tag v-if=" new Date(row.expireTime) < new Date()" type="danger">{{ row.expireTime ? row.expireTime.substring(0,10) : '不限制' }}</el-tag>
          <el-tag v-else type="success">{{ row.expireTime ? row.expireTime.substring(0,10) : '不限制' }}</el-tag>
        </div>
        <el-tag v-else>{{ '不限制'}}</el-tag>
      </template>
    </avue-crud>
    <el-dialog title="租户授权配置" append-to-body :visible.sync="box" width="450px">
      <avue-form :option="settingOption" v-model="settingForm" @submit="handleSubmit" />
    </el-dialog>
    <el-dialog title="功能清单" append-to-body :visible.sync="packageBox" @close="handlePackageFormReset">
      <avue-form v-if="packageBox" ref="formPackage" :option="packageOption" v-model="packageForm" @submit="handlePackageSubmit" @reset-change="handlePackageFormReset">
        <template slot="webMenuIds">
          <el-tree :data="menuWebIdTree" v-loading="menuWebIdTreeLoading" default-expand-all show-checkbox node-key="id" ref="menuWebIdTree" :default-checked-keys="webMenuIds" :props="{ label: 'title', children: 'children' }" highlight-current style="border: 1px solid #ebeef5; border-radius: 4px; padding: 8px; height: 300px; overflow-y: auto;" @check="menuWebIdTreeChange" />
        </template>
      </avue-form>

    </el-dialog>
    <!-- <el-drawer title="租户数据源管理" append-to-body :visible.sync="datasourceSettingBox" size="1000px">
      <tenant-datasource></tenant-datasource>
    </el-drawer> -->
    <el-drawer title="租户推荐包管理" append-to-body :visible.sync="packageSettingBox" size="1000px">
      <tenant-package></tenant-package>
    </el-drawer>
  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  setting,
  // packageInfo,
  tenantWebMenuIds,
  setTenantWebMenuIds
} from "@/api/system/new_tenant";
// import { getDetail as packageDetail } from "@/api/system/tenantpackage";
import { mapGetters } from "vuex";
import { getMenuTree } from "@/api/system/menu";
// import { validatenull } from "@/util/validate";
// import { myMessage } from '@/util/myMessage.js';
export default {
  data() {
    return {
      form: {},
      selectionList: [],
      query: {},
      loading: true,
      box: false,
      datasourceBox: false,
      datasourceSettingBox: false,
      packageBox: false,
      packageSettingBox: false,
      recycleMode: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        selection: true,
        addBtn: false,
        viewBtn: true,
        editBtn: false,
        menuWidth: 380,
        dialogWidth: 900,
        dialogClickModal: false,
        delBtn: false,
        column: [
          {
            label: "租户名称",
            prop: "regionName",
            search: true,
            span: 24,
            minLength: 2,
            maxlength: 20,
            rules: [{
              required: true,
              message: "请输入租户名称",
              trigger: "blur"
            }]
          },
          {
            label: "联系人",
            prop: "adminName",
            search: true,
            maxlength: 20,
            rules: [{
              required: true,
              message: "请输入联系人",
              trigger: "blur"
            }]
          },
          {
            label: "联系电话",
            prop: "contactNumber",
            maxlength: 20,
            rules: [
              {
                required: true,
                message: "请输入联系电话",
                trigger: "blur"
              },
              {
                pattern: /^(?!.*-$)^\d+(-\d+)*$/,
                message: "请输入正确的联系电话",
                trigger: "blur"
              },
            ]
          },
          {
            label: "联系地址",
            prop: "address",
            span: 24,
            minRows: 2,
            maxlength: 250,
            type: "textarea",
            hide: true,
          },
          {
            label: "账号额度",
            prop: "accountNumber",
            slot: true,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "过期时间",
            prop: "expireTime",
            slot: true,
            addDisplay: false,
            editDisplay: false,
            format: "yyyy-MM-dd",
          },
          {
            label: "开通时间",
            prop: "openTime",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "激活时间",
            prop: "activeTime",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "部门ID",
            prop: "deptId",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display:false,
            hide:true,
          },
        ]
      },
      data: [],
      settingForm: {},
      settingOption: {
        column: [
          {
            label: "账号额度",
            labelTip: '代表租户可创建的最大额度，若不限制则默认为-1',
            prop: "accountNumber",
            type: "number",
            span: 24,
            max: 99999,
          },
          {
            label: "过期时间",
            labelTip: '代表租户可使用的最后日期，若不限制则默认为空',
            prop: "expireTime",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            span: 24,
          },
        ]
      },

      packageForm: {},
      packageOption: {
        // labelWidth: 140,
        column: [
          // {
          //   label: "部门ID",
          //   prop: "deptId",
          //   addDisplay: false,
          //   editDisplay: false,
          //   viewDisplay: false,
          //   display:false,
          //   hide:true,
          // },
          {
            label: "菜单",
            prop: "webMenuIds",
            span: 24,
            formslot: true,
            rules: [
              {
                required: true,
                message: "请选择菜单",
                trigger: "blur"
              }
            ]
          },
          // {
          //   label: "小程序菜单预览",
          //   prop: "appMenuIds",
          //   span: 12,
          //   formslot: true,
          // },

        ]
      },
      menuWebIdTree: [],
      webMenuIds:[],
      menuWebIdTreeLoading:false,
      currentDeptId:'',
      // menuMiniIdTree: [],

      // areaData: [],
      // areaDataProps: {
      //     label: 'title',
      //     value: 'value',
      //     leaf: 'hasChildren'
      // }
    };
  },
  watch: {
    // 'packageForm.packageId'() {
    //   if (!validatenull(this.packageForm.packageId)) {
    //     packageDetail(this.packageForm.packageId).then(res => {
    //       this.initData();
    //       this.packageForm.webMenuIds = res.data.data.webMenuIds;
    //       // this.packageForm.appMenuIds = res.data.data.appMenuIds;
    //     });
    //   } else {
    //     this.packageForm.menuMiniId = [];
    //     // this.packageForm.menuWebId = [];
    //     //  this.initData();
    //   }
    // }
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.tenant_add, false),
        viewBtn: this.vaildData(this.permission.tenant_view, false),
        delBtn: this.vaildData(this.permission.tenant_delete, false),
        editBtn: this.vaildData(this.permission.tenant_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.deptId);
      });
      return ids;
    },
    // tenantId() {
    //   return this.selectionList[0].tenantId;
    // }
  },
  mounted() {
    // this.initData();
  },
  methods: {

    initData() {
      this.menuWebIdTreeLoading = true,
      getMenuTree(1).then(res => {
        this.menuWebIdTree = res.data.data;
        this.menuWebIdTreeLoading = false
      }).finally(() => {
        this.menuWebIdTreeLoading = false
      });
      // getMenuTree(2).then(res => {
      //   this.menuMiniIdTree = res.data.data;
      // });
    },

    beforeOpen(done, type) {
      if (["view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          const data = res.data.data;
          if (!(data.accountNumber > 0)) {
            data.accountNumber = "不限制";
          }
          if (!data.expireTime) {
            data.expireTime = "不限制";
          }
          this.form = data;
        });
      }
      done();
    },
    handlePackageFormReset() {
      // this.webMenuIds = [];
      // this.packageForm.webMenuIds = '';
      this.$refs.menuWebIdTree.setCheckedKeys([]);
      // this.appMenuIds = [];
      // this.menuWebIdTree = [];
      // this.menuMiniIdTree = [];
    },
    // beforeFormPackageOpen(done, type){
    //   this.initData();
    //   console.log("sss");
    //   done();
    // },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },

    handleSetting() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.selectionList.length === 1) {
        getDetail(this.selectionList[0].id).then(res => {
          const data = res.data.data;
          this.settingForm.accountNumber = data.accountNumber;
          this.settingForm.expireTime = data.expireTime.substring(0,10);
        });
      } else {
        this.settingForm.accountNumber = -1;
        this.settingForm.expireTime = '';
      }
      this.box = true;
    },

    handleRowPackage(row) {
      // console.log(row.deptId);
      this.currentDeptId = row.deptId;
      this.initData();
      tenantWebMenuIds(row.deptId).then(res => {
        const data = res.data.data;
        // console.log(data, "data");
        this.webMenuIds = data ? data: [];
        this.packageForm.webMenuIds = data ? data: [];
        this.packageBox = true;

      });

      //更新字典远程数据
      // setTimeout(() => {
      //   const form = this.$refs.formPackage;
      //   form.updateDic('packageId');
      // }, 10);
    },
    // handlePackage() {
    //   if (this.selectionList.length === 0) {
    //     this.$message.warning("请选择至少一条数据");
    //     return;
    //   }
    //   if (this.selectionList.length !== 1) {
    //     this.$message.warning("只能选择一条数据");
    //     return;
    //   }
    //   if (this.selectionList.length === 1) {
    //     packageInfo(this.selectionList[0].id).then(res => {
    //       const data = res.data.data;
    //       this.packageForm.menuId = data.menuId;
    //     });
    //   } else {
    //     this.packageForm.menuId = '';
    //   }
    //   this.packageBox = true;
    //   //更新字典远程数据
    //   setTimeout(() => {
    //     const form = this.$refs.formPackage;
    //     form.updateDic('packageId');
    //   }, 10);
    // },

    handlePackageSetting() {
      this.packageSettingBox = true;
    },
    handleSubmit(form, done, loading) {
      // console.log(form, "form",this.ids,"this.ids");
      if (form.expireTime) form.expireTime = form.expireTime + " 23:59:59";
      setting(this.ids, form).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "配置成功!"
        });
        done();
        this.box = false;
      }, error => {
        window.console.log(error);
        done();
      });
    },
    menuWebIdTreeChange() {
      this.$nextTick(() => {
        this.packageForm.webMenuIds = this.$refs.menuWebIdTree.getCheckedKeys()
      })

    },
    // handleDatasourceSubmit(form, done, loading) {
    //   datasource(form.tenantId, form.datasourceId).then(() => {
    //     this.$message({
    //       type: "success",
    //       message: "配置成功!"
    //     });
    //     done();
    //     this.datasourceBox = false;
    //   }, error => {
    //     window.console.log(error);
    //     loading();
    //   });
    // },
    handlePackageSubmit(form, done, loading) {
      // console.log(form, "form");
      // return
      let data= {
        deptId:this.currentDeptId,
        menuIds:form.webMenuIds
      }
      setTenantWebMenuIds(data).then(() => {
        this.$message({
          type: "success",
          message: "配置成功!"
        });
        this.onLoad(this.page);
        done();
        this.handlePackageFormReset()
        this.packageBox = false;
        this.currentDeptId = ""
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(
        page.currentPage,
        page.pageSize,
        Object.assign(params, this.query, {
          status: this.recycleMode ? -1 : 1,
        })
      ).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      }).finally(() => {
        this.loading = false;
      });
    },
  }
};
</script>

<style>
</style>

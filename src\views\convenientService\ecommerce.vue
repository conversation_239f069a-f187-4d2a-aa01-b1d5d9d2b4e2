<template>
<el-row ref="test">
    <el-col :span="5" v-loading="treeLoading">
      <div class="box">
        <el-scrollbar>
          <basic-container>
            <avue-tree :option="treeOption" :data="treeData" @node-click="nodeClick" show-checkbox>
            </avue-tree>
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :span="19">
      <basic-container>
        <avue-crud
          ref="crud"
          :data="data"
          :option="option"
          :table-loading="loading"
          :page.sync="page"
          :permission="permissionList"
          v-model="form" 
          :before-open="beforeOpen"
          @row-del="rowDel"
          @row-update="rowUpdate"
          @row-save="rowSave"
          @search-change="searchChange" 
          @search-reset="searchReset" 
          @selection-change="selectionChange" 
          @current-change="currentChange" 
          @size-change="sizeChange" 
          @refresh-change="refreshChange" 
          @on-load="onLoad" 
          class="directSupply-dialog"
          :upload-before='uploadBefore'
          :upload-after='uploadAfter'
          :upload-error='uploadError'
          :upload-delete="uploadDelete"
          :upload-exceed="uploadExceed"
        >
          <!-- 左侧菜单按钮 -->
          <template slot="menuLeft">
            <el-button size="small" icon="el-icon-document-add" plain @click="updateStatus(1)" v-if="permissionList.publishBtn">
              发布
            </el-button>
            <el-button size="small" icon="el-icon-document-delete" plain @click="updateStatus(0)" v-if="permissionList.cancelBtn">
              取消发布
            </el-button>
            <el-button type="danger" size="small" icon="el-icon-delete" plain @click="handleDelete" v-if="permissionList.delBtn">
              删除
            </el-button>
          </template>

          <!-- 状态显示 -->
          <template slot-scope="{ row }" slot="status">
            <el-tag type="danger" v-if="row.status === 0">未发布</el-tag>
            <el-tag type="success" v-if="row.status === 1">已发布</el-tag>
          </template>

          <!-- 行操作按钮 -->
          <template slot-scope="{ type, size, row, index }" slot="menu">
            <el-button :type="type" :size="size" @click.stop="$refs.crud.rowView(row, index)">
              查看
            </el-button>
            <el-button :type="type" :size="size" @click="$refs.crud.rowEdit(row, index)" v-if="
                row.status === 0 &&
                permissionList.editBtn
              ">
              编辑
            </el-button>
            <el-button :type="type" :size="size" @click="rowDel(row)" v-if="
                row.status === 0 &&
                permissionList.delBtn
              ">
              删除
            </el-button>
            <el-button :type="type" :size="size" @click.stop="openRecord(row)">
              学习记录
            </el-button>
          </template>
        </avue-crud>
        <ecommerceRecord :dialogVisible.sync="recordVisible" :recordId='recordId' />
      </basic-container>
    </el-col>
</el-row>
</template>

<script>
// import { globalEditor } from '@/components/Editor' // 全局富文本组件
import { mapGetters } from "vuex";
import { getDeptTree } from "@/api/infoRelease/partyLead"
import { validateFile } from "./util"
import * as funObj from '@/api/infoRelease/ecommerce'
import ecommerceRecord from './components/ecommerceRecord.vue';

export default {
  components:{ecommerceRecord},
  data() {
    return {
      moduleKey:'ecommerce',
      //表格数据
      data: [],
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],

      option: {
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        selection: true,
        labelWidth: 120,
        dialogWidth: 1200,
        dialogClickModal: false,
        viewBtn: false,
        viewBtnText: "预览",
        delBtn: false, //默认是有删除、编辑按钮
        editBtn: false,
        addBtn:true,
        dialogCustomClass: "directSupply-dialog",
        column: [
          {
            label: '标题',
            prop: 'title',
            type:'input',
            span: 24,
            maxlength:100,
            search: true,
            rules: [{ required: true, message: '标题不能为空' }]
          },
          {
            label: '状态',
            prop: 'status',
            slot: true,
            dicData: [
              { label: '已发布', value: 1 },
              { label: '未发布', value: 0 }
            ],
            display:false,
          },
          { label: '发布人', 
            prop: 'releaseUserName',
            display:false
          },
          {
            label: '发布时间',
            prop: 'updateTime',
            type: 'date',
            format: 'yyyy-MM-dd',
            valueFormat: "yyyy-MM-dd",
            search:true,
            searchRange: true,
            searchSpan: 8,
            display:false,
            hide: true,
            showColumn: false,
          },
          {
            label: '发布时间',
            prop: 'releaseTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            display:false
          },
          {
            label: '课程形式',
            prop: 'type',
            type: 'select',
            value: '1',
            dataType: 'string',
            span: 24,
            hide:true,
            showColumn:false,
            clearable:false,
            dicData: [
              { label: '视频', value: '1' },
              { label: '文章', value: '0' },
            ],
            control:(val,form)=>{
              console.log(val,'val')
              if(val==='1'){
                return {
                  videosLinks:{
                    display:true
                  },
                  description:{
                    display:true
                  },
                  content:{
                    display:false
                  }
                }
              }else{
                return {
                  videosLinks:{
                    display:false
                  },
                  description:{
                    display:false
                  },
                  content:{
                    display:true
                  }
                }
              }
            }
          },
          // 视频课程字段
          {
            label: '视频上传',
            prop: 'videosLinks',
            type: 'upload',
            hide:true,
            showColumn:false,
            display:true,
            multiple: true,
            fileSize:500*1024,
            limit: 9,
            accept:'.mp4',
            span: 24,
            action: '/api/blade-resource/oss/endpoint/put-file-attach',
            propsHttp: {
              res: 'data',
              url: 'link',
              name: 'originalName'
            },
            listType: 'picture-card',
            loadText: '图片上传中…请稍等',
            tip: '最多上传10个视频，支持格式：mp4，单个不超过500M',
            rules: [
              { required: true, message: '请上传视频' },
            ]
          },
          {
            label: '简介',
            prop: 'description',
            type: 'textarea',
            maxlength:500,
            span: 24,
            hide:true,
            showColumn:false,
             display:true
          },
          // 文章课程字段
          {
            label: '内容',
            prop: 'content',
            maxlength:1000,
            span: 24,
            hide:true,
            showColumn:false,
            display:false,
            component: "AvueUeditor",
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            customConfig: {
              excludeMenus: [
                "code",
                "todo",
                "fontName",
                "video",
                "table",
                "source",
                "fullScreen",
              ],
              uploadImgMaxLength: 1, //限制单次图片上传张数
            }, //wangEditor编辑的配置
            propsHttp: {
              res: "data",
              url: "link",
            },
            rules: [
              { required: true, message: '请输入内容' },
              ]
          },
          {
            label: '封面图片',
            prop: 'coverLink',
            type: 'upload',
            listType: 'picture-img',
            limit: 1,
            span: 24,
            fileSize: 10*1024,
            accept:'.png,.jpg,.jpeg',
            hide:true,
            showColumn:false,
            action: '/api/blade-resource/oss/endpoint/put-file-attach',
            propsHttp: {
              res: 'data',
              url: 'link',
              name: 'originalName'
            },
            tip: '只能上传1张png/jpg/jpeg图片，不超过10M',
            rules: [
              { required: true, message: '请上传封面图片' },
            ]
          },
          {
            label: '来源链接',
            prop: 'source',
            span: 24,
            hide:true,
            showColumn:false,
            maxlength:100,
          },
           {
            label: "发布范围",
            prop: "deptIds",
            filter: true,
            type: "tree",
            checkStrictly: true,
            multiple: true,
            dicUrl: "/api/blade-system/dept/tree",
            dicMethod: "get",
            props: {
              label: "title",
              value: "value",
            },
            expandOnClickNode: false,
            span: 24,
            rules: [{ required: true, message: "请选择发布范围", type: "array", trigger: ['blur', 'change'] }],
          },
          {
            label: '附件',
            prop: 'attachLinks',
            type: 'upload',
            span: 24,
            hide:true,
            showColumn:false,
            limit: 9,
            fileSize: 10*1024,
            tip: '仅支持上传图片、视频、音频、office 文件、txt,最多只能上传9个文件，单个文件不超过 10MB;',
            action: '/api/blade-resource/oss/endpoint/put-file-attach',
            propsHttp: {
              res: 'data',
              url: 'link',
              name: 'attachId',
            },
            props:{
              value:'link',
              label:'originalName'
            }
          }
        ]
      },
      edit_delist: [0],//可删除编辑状态值
      //文件上传列表
      attachList:{
        videosLinks:[],
        attachLinks:[],
        coverLink:[]
      },
      // 组织树
      deptId: "",
      treeLoading: false,
      treeOption: {
        nodeKey: "id",
        addBtn: false,
        menu: false,
        size: "small",
        props: {
          label: "title",
          value: "value",
          children: "children",
        },
      },
      treeData: [],
      //学习记录
      recordVisible:false,
      recordId:''
    }
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    // 优化后的权限计算属性
    permissionList() {
      const prefix = this.moduleKey;
      return Object.entries({
        addBtn: "_add",
        viewBtn: "_view",
        editBtn: "_edit",
        delBtn: "_delete",
        publishBtn: "_publish",
        cancelBtn: "_cancel",
      }).reduce((acc, [key, suffix]) => {
        acc[key] = this.vaildData(this.permission[`${prefix}${suffix}`], false);
        return acc;
      }, {});
    },
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created () {
    console.log(this.permission);
    this.treeLoading = true
    this.initTreeData(this.userInfo.tenant_id)
  },
  methods: {
    //
    openRecord(row){
      this.recordId=row.id
      this.recordVisible=true
    },
      // 发布
    async updateStatus(status) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (
        this.selectionList.findIndex((item) => item.status === 1) !== -1 &&
        status === 1
      ) {
        this.$message.warning("存在已发布的数据,已发布的数据无法再次发布");
        return;
      }
      if (
        this.selectionList.findIndex((item) => item.status === 0) !== -1 &&
        status === 0
      ) {
        this.$message.warning("存在未发布的数据,未发布的数据无法取消发布");
        return;
      }
      let res = null;
      if (status) {
        res = await funObj.release({ ids: this.ids });
      } else {
        res = await funObj.unRelease({ ids: this.ids });
      }
      if (res && res.data && res.data.success) {
        this.$message.success(`成功${status === 1 ? "发布" : "取消发布"}`);
        this.onLoad(this.page, this.query);
      }
    },
    // 获得传参值
    valueForm(row){
        var submitData = {
        title:row.title,
        type:row.type,
        description:row.description,
        content:row.content,
        source:row.source
      }
      if(row.id){
        submitData.id=row.id;
      }
      if(row.coverLink&&row.coverLink!==''){
        submitData.coverAttachId=this.attachList.coverLink[0]
      }
      if(row.videosLinks && row.videosLinks.length>0){
        submitData.videosIds=this.attachList.videosLinks.join(',')
      }
      if(row.attachLinks && row.attachLinks.length>0){
        submitData.attachIds=this.attachList.attachLinks.join(',')
      }
      if(row.deptIds && row.deptIds.length>0){
        submitData.deptIds=row.deptIds.join(',')
        }
      console.log('submitData', submitData,row);
      return submitData
    },
    // 初始化表单
    initForm(row){
      this.form = {
        id:row.id,
        title:row.title,
        type:row.type,
        description:row.description,
        content:row.content,
        source:row.source,
        coverLink:row.coverLink,
        deptIds:row.deptIds,
        videosLinks:[],
        attachLinks:[],
      }
      this.attachList={
        videosLinks:[],
        attachLinks:[],
        coverLink:[]
      }
      if(row.coverLink&&row.coverLink!==''){
        this.attachList.attachList=[row.coverAttachId]
      }
      if(row.videosLinks && row.videosLinks.length>0){
        row.videosLinks.forEach(item=>{
          this.attachList.videosLinks.push(item.id)
          this.form.videosLinks.push(item.link)
        })
      }
      if(row.attachLinks && row.attachLinks.length>0){
        row.attachLinks.forEach(item=>{
          this.attachList.attachLinks.push(item.id)
          this.form.attachLinks.push(item.link)
        })
      }
      // if(row.deptId && row.deptId.length>0){
      //   submitData.deptId=row.deptId.join(',')
      // }
      console.log('submitData', this.form);

    },
    // 新增
    rowSave(row, done, loading) {
      if (!this.form.attachList) {
        this.form.attachList = [];
      }
      let submitData=this.valueForm(this.form)
      funObj.save(submitData).then(
        async () => {
          this.$message.success(`成功新增`);
          await this.onLoad(this.page, this.query);
          done();
        },
        () => {
          loading();
        }
      );
    },
    // 修改
    rowUpdate(row, index, done, loading) {
      let submitData=this.valueForm(row)
      funObj.update(submitData).then(
        async () => {
          this.$message.success(`成功修改`);
          await this.onLoad(this.page, this.query);
          done();
        },
        () => {
          loading();
        }
      );
    },
    // 删除
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funObj.remove({
            ids: row.id,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // 重置
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    // 搜索
    searchChange(params, done) {
      this.page.currentPage = 1;
      const deptId = this.query.deptId
      this.query = params;
      this.query.deptId = deptId
      this.onLoad(this.page, params);
      done();
    },
    // 勾选
    selectionChange(list) {
      this.selectionList = list;
    },
    // 重置勾选
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    // 多选删除
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.selectionList.findIndex((item) => item.status === 1) !== -1) {
        this.$message.warning("存在已发布的数据,已发布的数据无法删除");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funObj.remove({
            ids: this.ids,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    // 打开前回调
    async beforeOpen(done, type) {
      if (type === "add") {
        this.form = {};
        done();
        return;
      }
      if (["edit", "view"].includes(type)) {
       funObj.detail({
          id: this.form.id,
        }).then((res) => {
          this.initForm(res.data.data);
          done();
        });
      }
    },
    uploadError(error,column) {
      if(error==='size'){
        this.$message.error('上传文件过大！')
      }
    },
    uploadAfter(res, done, loading, column){
      if (!res || !res.attachId) {
        this.$message.error("上传失败");
        loading();
      }else{
        if(!this.attachList[column.prop]){
          this.attachList[column.prop]=[]
        }
        this.attachList[column.prop].push(res.attachId);
        console.log(this.attachList,'aftterUpload',res)
        // res.name=res.originalName
        done()
      }
    },
    uploadDelete(file,column){
      return this.$confirm("是否确定移除该项？").then(() => {
         this.attachList[column.prop].splice(file.uid, 1);
          console.log(this.attachList[column.prop],'uploadDelete')
      });
    },
    // 文件上传前校验
    uploadBefore(file, done, loading,column) {
      if (column.prop == "videosLinks") {
        if (validateFile.call(this, "video", 500, file)) {
          done();
        } else {
          loading();
        }
      } else if(column.prop == "coverLink") {
        if (validateFile.call(this, "onlyImage", 10, file)) {
          done();
        } else {
          loading();
        }
      }
      else {
        if (validateFile.call(this, "all", 10, file)) {
          done();
        } else {
          loading();
        }
      }
    },
    // 上传限制
    uploadExceed(limit) {
      this.$message.error(`最多只能上传${limit}个文件`);
    },
    
    // 当前页切换
    currentChange (currentPage) {
      this.page.currentPage = currentPage;
    },
    // 页面显示条数切换
    sizeChange (pageSize) {
      this.page.pageSize = pageSize;
    },
    // 刷新
    refreshChange () {
      this.onLoad(this.page, this.query);
    },
    // 首次加载
    async onLoad (page, params = {}) {
      this.loading = true;
      let query = {
        ...params,
        current: this.page.currentPage,
        size: this.page.pageSize,
      };
      if (query.updateTime) {
        if (Array.isArray(query.updateTime)) {
          query.beginDate = query.updateTime[0];
          query.endDate = query.updateTime[1];
        }
        delete query.updateTime;
      }
      // needtochange
      let res = await funObj.getList(query);
      if (res && res.data && res.data.success) {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        if (this.page.currentPage > 1 && this.page.currentPage > data.pages) {
          this.page.currentPage = data.pages
          this.onLoad()
        }
        this.selectionClear();
      }
    },
    // 树
    filterNodeMethod (value, data) {
      if (!value) return true
      return data.deptName.indexOf(value.trim()) !== -1
    },
    nodeClick (data) {
      this.query.deptId = data.id
      this.deptId = data.id
      this.page.currentPage = 1
      this.onLoad(this.page, this.query)
    },
    async initTreeData (tenantId) {
      this.treeData = (await getDeptTree(tenantId)).data.data
      this.treeLoading = false
    },
  }
}
</script>

<style scoped>
.el-dropdown-link {
  cursor: pointer;
  color: #409EFF;
}
</style>
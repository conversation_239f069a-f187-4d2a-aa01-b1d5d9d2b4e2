<!--
 * @Author: linzq33
 * @Date: 2025-07-16 19:53:26
 * @LastEditors: 
 * @LastEditTime: 
 * @Description: 用工信息-新增编辑页
-->

<template>
  <el-dialog :fullscreen="isFullscreen" visible="true" append-to-body="true" :close-on-click-modal="false" top="100px"
    width="60%" @close="close()">
    <div slot="title" class="header">
      <div class="avue-crud__dialog__header">
        <span class="el-dialog__title">{{ title }}</span>
        <div class="avue-crud__dialog__menu">
          <i @click="isFullScreen" class="el-dialog__close el-icon-full-screen"></i>
        </div>
      </div>
    </div>
    <avue-form ref="form" v-model="obj" :option="option">
      <template slot="welfare">
        <el-checkbox-group v-model="checkList" @change="isShowWelfare">
          <el-checkbox v-for="(item, index) in welfareList" :label="item.dictKey" :key="index">{{ item.dictValue
          }}</el-checkbox>
          <el-input v-model.trim="welfareOther" v-if="showWelfare" placeholder="请输入" style="width: 30%"
            maxlength="200"></el-input>
        </el-checkbox-group>
      </template>
    </avue-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="close()">取 消</el-button>
      <el-button size="small" type="primary" @click="submit">确 定</el-button>
    </div>
    <div>
      <!-- <div id="searchResultPanel" class="searchResultPanel"></div> -->
      <div class="adr" v-if="!showInput" @click="toggleShowInput">
        {{ address }}
      </div>
      <div class="adr1" v-else>
        <el-button type="primary" size="small" @click="toggleShowInput">关闭搜索</el-button>
        <!-- <el-input
          v-model="searchText"
          maxlength="20"
          @keyup.enter.native="searchAddress"
        >
          <el-button
            slot="append"
            icon="el-icon-search"
            @click="searchAddress"
          ></el-button>
        </el-input> -->
        <el-select v-model="searchText" filterable remote clearable reserve-keyword placeholder="请输入关键词"
          :remote-method="searchAddress" @change="selectChange" :loading="loading">
          <el-option v-for="item in searchResult" :key="item.id" :label="item.name" :value="item.location">
            <span style="float: left;width:30%;" :title="item.name">{{ item.name }}</span>
            <span style="float: right; width:70%;color: #8492a6; font-size: 13px;"
              :title="`${item.cityname}` + '/' + `${item.adname}` + '/' + `${item.address}`">{{ item.cityname }} / {{
                item.adname }}
              /{{ item.address }}</span>
          </el-option>
          <div class="loadmore" v-loading="loading">
            <span style="cursor:pointer;" @click="searchAddress(searchText, true)">加载更多</span>
          </div>
        </el-select>
      </div>
      <div id="mapContent" class="baiduMapContent" v-loading="mapLoading" element-loading-text="正在获取位置信息..."></div>
    </div>
  </el-dialog>
</template>
<script>
import { add, getDetail, update } from "@/api/infoRelease/employInfo";
import axios from "axios";
import { mapGetters } from "vuex";
import qs from "qs";
import { debounce } from "lodash";
import AMapLoader from "@amap/amap-jsapi-loader";

export default {
  name: "Deatil",
  props: {
    detail: {
      type: Object,
      required: true,
    },
    treeData: {
      type: Object,
      required: false,
    },
  },
  data () {
    const validateName = (rule, value, callback) => {
      if (value) {
        var isChineseName = /^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,15}$/; //中文姓名（支持带点）
        if (isChineseName.test(value)) {
          callback();
        } else {
          callback("请输入 正确中文姓名!");
        }
      } else {
        if (rule.required === true) {
          callback("请输入 联系人!");
        } else {
          callback();
        }
      }
    };

    const validateSalary = (rule, value, callback) => {
      const numberPart = '(?:(?:0|[1-9]\\d{0,7})(?:\\.\\d{1,2})?|99999999\\.99)';
      const pattern = new RegExp(`^${numberPart}-${numberPart}$`);
      if (!value) {
        callback("请输入工资待遇!");
      } else if (!pattern.test(value)) {
        callback("请输入0.00-99999999.99的工资待遇");
      } else {
        callback();
      }
    };
    const validateDate = (rule, value, callback) => {
      const time = new Date(value)
      if (!value) {
        callback("请选择有限期限!");
      } else if (time < new Date()) {
        callback("有限期限已过期，请重新选择");
      } else {
        callback();
      }
    };
    return {
      loading: false,
      searchResult: [],
      searchPage: {
        page_size: 10,
        page_num: 1
      },
      title: JSON.stringify(this.detail) == "{}" ? "添加用工信息" : "用工信息修改",
      obj: {},
      latlng: {},
      showWelfare: false,
      welfareList: [],
      welfareOther: "",
      checkList: [],
      address: "",
      searchText: "",
      showInput: false,
      mapLoading: true,
      isFullscreen: false,
      province: "",
      city: "",
      district: "",
      regionCode: "",
      marker: "",
      option: {
        column: [
          {
            type: "select",
            label: "招聘职位",
            dicUrl: "/api/blade-system/dict/dictionary?code=job",
            dicMethod: "get",
            span: 12,
            display: true,
            filterable: true,
            allowCreate: true,
            props: { label: "dictValue", value: "dictValue" },
            prop: "job",
            rules: [
              {
                required: true,
                message: "请选择招聘职位",
                trigger: "change",
              },
            ],
          },
          {
            type: "date",
            label: "有效期限",
            span: 12,
            display: true,
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            prop: "validPeriod",
            pickerOptions: {
              disabledDate (time) {
                return time.getTime() < Date.now() - 8.64e7;
              },
            },
            rules: [
              {
                required: true,
                validator: validateDate,
                trigger: "blur",
              },
            ],
          },
          {
            type: "select",
            label: "招聘类型",
            dicUrl: '/api/blade-system/dict/dictionary?code=recruitment_type',
            dicMethod: "get",
            span: 12,
            props: { label: "dictValue", value: "dictKey" },
            prop: "recruitmentType",
            rules: [
              {
                required: true,
                message: "请选择招聘类型",
                trigger: "blur",
              },
            ],
          },
          {
            type: "input",
            label: "招工单位",
            span: 12,
            display: true,
            prop: "recruitmentUnit",
            maxlength: 30,
            showWordLimit: true,
          },
          {
            type: "input",
            label: "工资待遇",
            span: 12,
            display: true,
            placeholder: "请输入 工资待遇，如：500-600",
            prop: "pay",
            maxlength: 20,
            showWordLimit: true,
            rules: [
              {
                required: true,
                whitespace: true,
                message: "请输入工资待遇",
                trigger: "blur",
                validator: validateSalary,
              },
            ],
          },
          {
            type: "input",
            label: "职位描述",
            span: 12,
            display: true,
            prop: "description",
            maxlength: 200,
            showWordLimit: true,
          },
          {
            type: "select",
            label: "工资类型",
            dicUrl: "/api/blade-system/dict/dictionary?code=salary_type",
            dicMethod: "get",
            span: 12,
            display: true,
            props: { label: "dictValue", value: "dictKey" },
            prop: "salaryType",
            rules: [
              {
                required: true,
                message: "请选择工资类型",
                trigger: "blur",
              },
            ],
          },
          {
            type: "input",
            label: "联系人",
            span: 12,
            display: true,
            prop: "contactPerson",
            maxlength: 15,
            showWordLimit: true,
            rules: [
              {
                required: true,
                whitespace: true,
                message: "请填写正确的联系人姓名",
                trigger: "blur",
                validator: validateName,
              },
            ],
          },
          {
            type: "input",
            label: "联系电话",
            span: 12,
            display: true,
            prop: "contactPhone",
            maxlength: 11,
            rules: [
              {
                required: true,
                trigger: "blur",
                message: "请填写正确的联系电话",
                whitespace: true,
              },
            ],
          },
          {
            type: "input",
            label: "联系人职务",
            span: 12,
            display: true,
            prop: "contactPost",
            maxlength: 20,
            showWordLimit: true,
          },
          {
            label: '公开范围',
            prop: 'publicArea',
            type: 'tree',
            span: 24,
            multiple: true,
            collapseTags: true,
            maxCollapseTags: 3,
            collapseTagsTooltip: true,
            dicData: [],
            props: { label: "title" },
            rules: [
              {
                required: true,
                trigger: "change",
                message: "请选择公开范围",
              },
            ],
          },
          {
            prop: "welfare",
            // type: "checkbox",
            label: "福利待遇",
            dicUrl: "/api/blade-system/dict/dictionary?code=welfare",
            dicMethod: "get",
            span: 24,
            // display: true,
            formslot: true,
            // props: { label: "dictValue", value: "dictKey" },
            dicFormatter: (res) => {
              this.welfareList = res.data;
              // return res.data;
            },
            // dataType: 'string'
          },
        ],
        labelPosition: "right",
        labelSuffix: "：",
        labelWidth: 100,
        gutter: 0,
        menuBtn: true,
        submitBtn: false,
        submitText: "确定",
        emptyBtn: false,
        emptyText: "取消",
        menuPosition: "center",
        tabs: false,
        detail: false,
      },
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
  },
  watch: {
    address: function (val) {
      if (val !== "") {
        this.$nextTick(() => {
          this.searchText = val;
        });
      }
    },
  },
  mounted () {
    setTimeout(() => {
      if (JSON.stringify(this.detail) != "{}") {
        this.getDetails();
      } else {
        // 新增
        this.renderGDMap();
      }
    }, 500);
    const publicArea = this.findObject(this.option.column, 'publicArea')
    publicArea.dicData = this.treeData;
  },
  methods: {
    getDetails () {
      getDetail(this.detail.id).then((res) => {
        if (res.data.success) {
          let data = res.data.data;
          if (data) {
            this.checkList = data.welfare.split(",");
            this.isShowWelfare(this.checkList);
            this.welfareOther = data.welfareOther;
            this.pay = `${data.payMin}-${data.payMax}`;
          }
          this.obj = data;
          this.renderGDMap(data.longitude, data.latitude);
        }
      });
    },
    close () {
      this.$parent.isShowDetail(false, false);
    },
    submit () {
      if (!this.address) {
        this.$message({
          type: "warning",
          message: "请在地图上选择位置信息！",
        });
        return;
      }
      console.log('405', this.obj);
      
      this.$refs["form"].validate((valid) => {
        if (!valid) return;
        
        let param = {
          address: this.address,
          description: this.obj.description,
          job: this.obj.job,
          payMin: this.obj.pay.split("-")[0],
          payMax: this.obj.pay.split("-")[1],
          recruitmentUnit: this.obj.recruitmentUnit,
          contactPhone: this.obj.contactPhone,
          contactPerson: this.obj.contactPerson,
          contactPost: this.obj.contactPost,
          recruitmentType: this.obj.recruitmentType,
          salaryType: this.obj.salaryType,
          status: this.obj.status || 0,
          validPeriod: this.obj.validPeriod,
          welfare: this.checkList.join(","),
          welfareOther: this.welfareOther,
          regionCode: this.regionCode,
          publicArea: this.obj.publicArea,
          ...this.latlng,
        };
        if (JSON.stringify(this.detail) == "{}") {
          add(param).then((res) => {
            if (res.data.success) {
              this.$parent.isShowDetail(false, true);
              this.$message({
                type: "success",
                message: res.data.msg,
              });
            }
          });
        } else {
          param.id = this.obj.id;
          update(param).then((res) => {
            if (res.data.success) {
              this.$parent.isShowDetail(false, true);
              this.$message({
                type: "success",
                message: res.data.msg,
              });
            } else {
              this.$message({
                type: "warning",
                message: res.data.msg,
              });
            }
          });
        }
      });
    },

    /**
     * 初始化高德地图
     */
    renderGDMap (lng, lat) {
      this.mapLoading = false;
      let that = this;

      window._AMapSecurityConfig = {
        securityJsCode: "d747acb84a39e989d49e8d9aca53b573", // 安全密钥
      };

      AMapLoader.load({
        key: "b3aa6f7ba8d5a0fc1a51a0ab420ddf7b", // 申请好的Web端开发者Key，首次调用 load 时必填
        version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
        plugins: [
          "AMap.ToolBar",
          "AMap.Scale",
          "AMap.OverView",
          "AMap.MapType",
          "AMap.Geolocation",
          "AMap.Geocoder"
        ], // 需要使用的插件列表
      })
        .then((AMap) => {
          const container = document.querySelector("#mapContent");
          let map;

          if (lng && lat) {
            map = new AMap.Map(container, {
              center: [lng, lat],
              viewMode: "2D", // 是否为3D地图模式
              zoom: 16, // 初始化地图级别// 初始化地图中心点位置
            });
            that.marker = new AMap.Marker({
              map: map,
              position: [lng, lat],
            });
            that.getAddressAndAdcode([lng, lat]);
          } else {
            map = new AMap.Map(container, {
              viewMode: "2D", // 是否为3D地图模式
              zoom: 16, // 初始化地图级别
              center: [117.402167, 26.33612978], // 初始化地图中心点位置
            });
          }

          // 添加工具条控件、比例尺控件和地图类型控件
          map.addControl(new AMap.ToolBar());
          map.addControl(new AMap.Scale());

          // 添加地图点击事件
          map.on("click", function (e) {
            that.latlng = {
              longitude: e.lnglat.lng,
              latitude: e.lnglat.lat,
            };
            if (that.marker) {
              that.marker.setMap(null);
            }
            that.marker = new AMap.Marker({
              map: map,
              position: e.lnglat,
            });
            that.getAddressAndAdcode(e.lnglat);
          });
        })
        .catch((e) => {
          console.error("地图加载失败", e);
        });
    },
    getAddressAndAdcode (lnglat) {
      let that = this;
      var geocoder = new window.AMap.Geocoder({
        city: "010", //城市设为北京，默认：“全国”
        radius: 1000, //范围，默认：500
      });
      geocoder.getAddress(lnglat, function (status, result) {
        if (status === "complete" && result.regeocode) {
          that.address = result.regeocode.formattedAddress;
          that.regionCode = result.regeocode.addressComponent.adcode;
        } else {
          console.log.error("根据经纬度查询地址失败");
        }
      });
    },
    isFullScreen () {
      this.isFullscreen = !this.isFullscreen;
    },
    toggleShowInput () {
      this.searchText = "";
      this.address = "";
      this.showInput = !this.showInput;
      this.renderGDMap("", "");
    },
    searchAddress: debounce(function (query, isLoad = false) {
      this.loading = true;
      this.searchText = query;
      let params = {
        key: "50c9524d26153d133224b601d31f771d",
        keywords: query,
        page_size: this.searchPage.page_size,
        page_num: isLoad ? ++this.searchPage.page_num : this.searchPage.page_num
      };
      axios
        .get("/restapi/v5/place/text?" + qs.stringify(params))
        .then((res) => {
          if (isLoad) {
            this.searchResult = [
              ...this.searchResult,
              ...res.data.pois
            ]
          } else {
            this.searchResult = res.data.pois
            this.searchPage.page_num = 1
          }
          this.loading = false;
        });
    }, 100),
    selectChange (val) {
      if (val !== "") {
        this.renderGDMap(val.split(",")[0], val.split(",")[1]);
        this.latlng = {
          longitude: val.split(",")[0],
          latitude: val.split(",")[1]
        };
        this.searchResult = [];
      } else {
        this.searchResult = [];
      }
    },
    isShowWelfare (val) {
      if (val.length === 0) {
        this.showWelfare = false;
        this.welfareOther = "";
      } else {
        try {
          val.forEach((item) => {
            if (
              this.welfareList.filter(
                (item1) => item1.dictKey === item && item1.dictValue === "其他"
              ).length > 0
            ) {
              this.showWelfare = true;
              throw new Error("LoopInterrupt");
            } else {
              this.showWelfare = false;
              this.welfareOther = "";
            }
          });
        } catch (e) {
          if (e.message !== "LoopInterrupt") throw e;
        }
      }
      // console.log(2222,this.showWelfare)
    },
  },
};
</script>
<style lang="scss" scoped>
.adr {
  line-height: 40px;
  padding-left: 5px;
  height: 40px;
  font-size: 18px;
  width: 100%;
  border: 1px solid #908f8d;
  background-color: #f1f0ec;
  margin-bottom: 5px;
}

.adr1 {
  line-height: 40px;
  height: 40px;
  font-size: 18px;
  width: 100%;
  border: 1px solid #908f8d;
  margin-bottom: 5px;
  display: flex;
  justify-content: space-between;
}

.baiduMapContent {
  height: 300px;
}

.dialogStyle {
  padding: 16px 24px;
  min-height: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  word-wrap: break-word;
}

.header {
  padding: 0px 10px;
}

::v-deep .el-select {
  width: 100% !important;
}

.loadmore {
  text-align: center;
  font-size: 13px;
  margin: 5px;
  color: #8492a6
}

.el-select-dropdown .el-scrollbar .el-scrollbar__view .el-select-dropdown__item>span {
  padding: 0 10px;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
/**
 * @Author: zhengjh43
 * @Date: 2024-01-22 15:32:36
 * @LastEditors: zhengjh43
 * @LastEditTime: 2024-01-25 14:49:55
 * @Explain: 积分录入
 */
import request from "@/router/axios";

/**
 * @description: 获得积分录入分页
 * @param {int}current
 * @param {int}size
 * @param {object}params
 */
export const getList = params => {
  return request({
    url: "/api/admin/integral-input/page",
    method: "get",
    params
  });
};

/**
 * @description: 积分录入详情
 * @param {int} id
 */
export const detail = params => {
  return request({
    url: "/api/admin/integral-input/detail",
    method: "get",
    params
  });
};

/**
 * @description: 新增
 * @param {object} data
 */
export const add = data => {
  return request({
    url: "/api/admin/integral-input/save",
    method: "post",
    data
  });
};

/**
 * @description: 修改
 * @param {object} data
 */
export const update = data => {
  return request({
    url: "/api/admin/integral-input/update",
    method: "post",
    data
  });
};

/**
 * @description: 批量删除
 * @param {int}id
 */
export const remove = ({ id }) => {
  let formData = new FormData();
  formData.append("ids", id);
  return request({
    url: "/api/admin/integral-input/remove",
    method: "post",
    data: formData
  });
};

/**
 * @description: 批量审核
 * @param {int}id
 */
export const sendAudit = ({ id }) => {
  let formData = new FormData();
  formData.append("ids", id);
  return request({
    url: "/api/admin/integral-input/send-audit",
    method: "post",
    data: formData
  });
};

/**
 * @description: 撤回
 * @param {int}id
 */
export const recall = ({ id }) => {
  let formData = new FormData();
  formData.append("id", id);
  return request({
    url: "/api/admin/integral-input/recall",
    method: "post",
    data: formData
  });
};

/**
 * @description: 积分发放
 * @param {object} data
 */
export const grantIntegral = data => {
  let formData = new FormData();
  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      formData.append(key, data[key]);
    }
  }
  return request({
    url: "/api/admin/integral-input/grant-integral",
    method: "post",
    data: formData
  });
};

/**
 * @description: 部门当月是否已进行积分发放
 * @param {int} deptId
 */
export const isGrant = params => {
  return request({
    url: "/api/admin/integral-input/is-grant",
    method: "get",
    params
  });
};

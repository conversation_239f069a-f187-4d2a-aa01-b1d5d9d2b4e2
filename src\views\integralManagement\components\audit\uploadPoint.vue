<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" ref="crud" :search.sync="search" v-model="form"
      :page.sync="page" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
      @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="primary" size="small" :icon="icon" @click="handleAudit" :disabled="selectionList.length < 1">批量审核
        </el-button>
      </template>
      <template slot-scope="{ row }" slot="status">
        <div v-if="row.status == 1" class="offLine-dot"></div>
        <div v-if="row.status == 2" class="onLine-dot"></div>
        <div v-if="row.status == 3" class="unSign-dot"></div>
        <span v-if="row.status == 1">未审核</span>
        <span v-if="row.status == 2">已通过</span>
        <span v-if="row.status == 3">已驳回</span>
      </template>
      <template slot-scope="{type,size,row,index}" slot="menu">
        <el-button :size="size" :type="type" @click="showModel(row, index)">查看</el-button>
      </template>
    </avue-crud>
    <el-dialog :visible.sync="auditModel" title="查看" :modal="false" :destroy-on-close="true" append-to-body
      width="800px">
      <el-form ref="form_show" :model="form_show" label-width="80px">
        <el-form-item label="申报主题">
          <el-input v-model="form_show.title" :disabled='true'></el-input>
        </el-form-item>
        <el-form-item label="申报内容">
          <el-input type="textarea" v-model="form_show.content" :disabled='true'></el-input>
        </el-form-item>
        <el-form-item label="适用规则">
          <el-input v-model="form_show.ruleTitle" :disabled='true'></el-input>
        </el-form-item>
        <el-form-item label="奖励分">
          <el-input v-model="form_show.ruleIntegral" :disabled='true'></el-input>
        </el-form-item>
        <el-form-item label="附件">
          <div class="file-image_list">
            <div class="file-item" v-for="(item, index) in form_show.pics" :key="index">
              <el-image :src="item.link" :preview-src-list="[item.link]" :z-index="9999"></el-image>
              <div class="file-name" :title="'点击下载该图片:\n' + item.originalName" @click="download(item)">
                {{ item.originalName }}
              </div>
            </div>
          </div>
          <div class="file-office_file_list">
            <div class="file-item" v-for="(item, index) in form_show.offices" :key="index">
              <i class="file-icon el-icon-document"></i>
              <div class="file-name" :title="'点击下载该文件:\n' + item.originalName" @click="download(item)">{{
                item.originalName
                }}</div>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="申报人">
          <el-input v-model="form_show.declarer" :disabled='true'></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input type="textarea" v-model="form_show.remark" :disabled='true'></el-input>
        </el-form-item>
        <el-form-item v-if="form_show.status !== 1" label="状态">
          <el-input v-model="form_show.statusName" :disabled='true'></el-input>
        </el-form-item>
        <el-form-item v-if="form_show.status == 3" label="驳回理由">
          <el-input type="textarea" v-model="form_show.rejectReason" :disabled='true'></el-input>
        </el-form-item>
      </el-form>
      <span v-if="form_show.status == 1" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="showConfirmModel(true)">通 过</el-button>
        <el-button @click="showConfirmModel(false)">驳 回</el-button>
      </span>
      <el-dialog width="30%" :visible.sync="showConfirm" :modal="false" :destroy-on-close="true" append-to-body>
        <div v-if='passFlag'>是否确认此条数据通过?</div>
        <div v-else>
          <div style="margin-bottom:15px">请填写驳回理由:</div>
          <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="textarea">
          </el-input>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="confirm">确 定</el-button>
          <el-button @click="showConfirm = false">取 消</el-button>
        </span>
      </el-dialog>
    </el-dialog>
    <el-dialog :visible.sync="handleAuditModel" title="批量审核" :modal="false" :destroy-on-close="true" append-to-body
      width="800px">
      <div style="margin-bottom:20px">
        <el-radio v-model="passFlag" :label="true">通 过</el-radio>
        <el-radio v-model="passFlag" :label="false">驳 回</el-radio>
      </div>
      <div v-if="!passFlag">
        <div style="margin-bottom:15px">请填写驳回理由:</div>
        <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="textarea">
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="confirm(1)">确 定</el-button>
        <el-button @click="handleAuditModel = false">取 消</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList, getDetail, declare
} from "@/api/integralManagement/audit.js";
import { mapGetters } from "vuex";
import { downloadFileBlob } from "@/util/util";
export default {
  props: {
    deptId: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      icon: "el-icon-download el-icon--right",
      disableButton: false,
      form: {},
      selectionList: [],
      query: {},
      loading: true,
      parentId: '',
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      search: {},
      option: {
        tip: false,
        selectable: (row, index) => {
          return row.status == 1;
        },
        simplePage: true,
        searchShow: true,
        searchMenuSpan: 6,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        border: true,
        index: true,
        selection: true,
        menu: true,
        column: [{
          label: "申报主题",
          prop: "title",
          search: true,
          searchSpan: 6,
          type: "input",
          maxlength: 30,
          // showWordLimit: true,
        },
        {
          label: "适用规则",
          prop: "ruleTitle",
          search: true,
          searchSpan: 6,
          type: "input",
          maxlength: 30,
        }, {
          label: "奖励分",
          prop: "ruleIntegral",
          type: "string",
        }, {
          label: "申报人",
          prop: "declarer",
          type: "string",
        }, {
          label: "审核状态",
          prop: "status",
          showColumn: false,
          search: true,
          searchSpan: 6,
          type: "select",
          // searchValue: "",
          dicUrl:
            "/api/blade-system/dict/dictionary?code=integral_declare_audit_status",
          dicMethod: "get",
          props: {
            //对应select的属性
            label: "dictValue",
            value: "dictKey",
          },
        }, {
          label: "审核时间",
          prop: "auditTime",
          type: "string",
        }]
      },
      data: [],
      form_show: {},
      auditModel: false,
      showConfirm: false,
      passFlag: true,
      textarea: '',
      handleAuditModel: false,
    };
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    ids () {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids;
    }
  },
  watch: {
    deptId: {
      handler: function (val, oldVal) {
        if (val == oldVal) {
          return
        }
        this.onLoad();
      },
      immediate: true
    }
  },
  methods: {

    searchReset () {
      this.search = {};
      this.page.currentPage = 1;
      this.onLoad();
    },
    searchChange (params, done) {
      this.search = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange (list) {
      this.selectionList = list;
    },
    selectionClear () {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },

    currentChange (currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange (pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange () {
      this.page.currentPage = 1;
      this.search.level = "2"
      this.onLoad(this.page, this.search);
    },
    async onLoad () {
      this.loading = true;
      this.search.auditStatus = this.search.status
      const result = await new Promise((resolve) => {
        getList(this.page.currentPage, this.page.pageSize, this.deptId, this.search).then(res => {
          console.log(res)
          resolve(res);

        });
      });
      this.$nextTick(() => {
        this.loading = false;
        this.data = result.data.data.records;
        this.page.total = result.data.data.total;
        this.loading = false;
        this.selectionClear();
      })
    },
    showModel (row) {

      console.log(row)
      getDetail(row.id).then(res => {
        console.log(res)
        this.form_show = res.data.data;
        this.auditModel = true;
      })
    },
    showConfirmModel (flag) {
      this.passFlag = flag;
      this.showConfirm = true
    },
    confirm (flag = 0) {
      let temp = {
        ids: flag == 1 ? this.ids : [this.form_show.id],
        isPass: this.passFlag,
        rejectReason: this.textarea
      }
      if (!this.passFlag) {
        if (this.textarea == "") {
          this.$message({
            message: '请填写驳回理由',
            type: 'warning'
          });
          return;
        }
        if (this.textarea.length > 200) {
          this.$message({
            message: '驳回理由不能超过200个字符',
            type: 'warning'
          });
          return;
        }
      }
      declare(temp).then(res => {
        console.log(res)
        if (res.data.code == 200) {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.auditModel = false;
          this.showConfirm = false;
          this.handleAuditModel = false;
          this.textarea = "";
          this.passFlag = true;
          this.onLoad();
        }
      }).finally(() => {
        this.showConfirm = false;
        this.this.textarea = "";
        this.passFlag = true;
      })
    },

    handleAudit () {
      this.handleAuditModel = true;
    },
    download (item) {
      downloadFileBlob(item.link, item.originalName)
    },
  }
};
</script>

<style lang="scss" scoped>
.unSign-dot {
  height: 8px;
  width: 8px;
  background-color: #ff0000;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
}

.onLine-dot {
  height: 8px;
  width: 8px;
  background-color: #4fff08;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
}

.offLine-dot {
  height: 8px;
  width: 8px;
  background-color: #cccccc;
  border-radius: 50%;
  display: inline-block;
  margin-right: 5px;
}

.file-item {
  width: 100px;
  display: inline-flex;
  flex-direction: column;
  text-align: center;
  margin: 5px;
}

.file-icon::before {
  font-size: 80px;
}

.file-name {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &:hover {
    cursor: pointer;
    color: #409eff;
  }
}

.file-image_list {
  .el-image {
    width: 100px;
    height: 100px;
  }
}
</style>

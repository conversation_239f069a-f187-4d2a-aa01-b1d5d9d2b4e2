<!--
 * @Description: 福建省级地图-公共组件
 * @Author: chenz76
 * @Date: 2021-12-28 15:37:06
 * @LastEditors: chenz76
 * @LastEditTime: 2022-01-04 11:11:51
-->
<template>
  <div :id="id" :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import tdTheme from "./theme.json"; // 引入默认主题
import fujian<PERSON>son from "../map/fujian";
import resizeMixins from "@/util/resizeMixins";
import * as echarts from "echarts/lib/echarts";
import "echarts/lib/chart/map";
import "echarts/lib/chart/bar";
import "echarts/lib/chart/pie";
import "echarts/lib/chart/effectScatter";
import "echarts/lib/component/geo";
import "echarts/lib/component/tooltip";
import "echarts/lib/component/title";
import "echarts/lib/component/legend";
export default {
  name: "echart",
  mixins: [resizeMixins],
  props: {
    className: {
      type: String,
      default: "centreLeft2Chart",
    },
    id: {
      type: String,
      default: "centreLeft2Chart",
    },
    width: {
      type: String,
      default: "100%",
    },
    height: {
      type: String,
      default: "40vh",
    },
    options: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    options: {
      handler(options) {
        // 设置true清空echart缓存
        this.chart.setOption(options, true);
      },
      deep: true,
    },
  },
  mounted() {
    echarts.registerTheme("tdTheme", tdTheme); // 覆盖默认主题
    echarts.registerMap("福建", fujianJson);
    this.initData();
  },
  destroyed() {
    if (this.chart) {
      // 重要  释放之前的图表实例， 否则改变的主题无效果
      this.chart.dispose();
      this.chart = null;
    }
  },
  methods: {
    initData() {
      this.chart = echarts.init(this.$el, "tdTheme");
      this.chart.setOption(this.options, true);
    },
  },
};
</script>

<style>
</style>

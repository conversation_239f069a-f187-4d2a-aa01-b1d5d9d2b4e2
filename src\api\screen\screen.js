/*
 * @Description: 大屏前台
 * @Author: chenz76
 * @Date: 2021-10-27 09:18:58
 * @LastEditors: chenn26
 * @LastEditTime: 2023-01-18 09:28:19
 */
import request from '@/router/axios';

/**
 * @description: 右上-劳务用工统计
 * @param {*}
 * @return {*}
 * @author: chenz76
 */
export const getLabor = () => {
  return request({
    url: '/api/big-screen/labor/statistic',
    method: 'get',
  })
}

/**
 * @description: 右下右-信箱统计
 * @param {*}
 * @return {*}
 * @author: chenz76
 */
export const getMailbox = (params) => {
  return request({
    url: '/api/big-screen/mailbox/statistic',
    method: 'get',
    params:params
  })
}

/**
 * @description: 右中-产品集市统计
 * @param {*}
 * @return {*}
 * @author: chenz76
 */
export const getMarket = () => {
  return request({
    url: '/api/big-screen/market/statistic',
    method: 'get',
  })
}

/**
 * @description: 右下左-民情统计
 * @param {*}
 * @return {*}
 * @author: chenz76
 */
export const getPublicMind = (params) => {
  return request({
    url: '/api/big-screen/public-mind/statistic',
    method: 'get',
    params:params
  })
}

/**
 * @description: 中下-三务公开
 * @param {
 * current	当前页	query	false
 * integer(int32)
 * module	模块名	query	false
 * string
 * size	每页的数量	query	false
 * integer(int32)
 * status	游客是否可见（可见为1，不可见为0）	query	false
 * integer(int32)
 * title	标题	query	false
 * string
 * type
 * }
 * @return {*}
 * @author: chenz76
 */
export const getThreeAffairs = (params) => {
  return request({
    url: '/api/big-screen/three-affairs/list',
    method: 'get',
    params: params
  })
}

/**
 * @description: 中下-获取所有三务公开数据
 * @param {
 * current	当前页	query	false
 * integer(int32)
 * size	每页的数量	query	false	}
 * @return {*}
 * @author: chenz76
 */
export const getThreeAffairsAll = (params) => {
  return request({
    url: '/api/big-screen/three-affairs/all',
    method: 'get',
    params: params
  })
}

/**
 * @description: 中上-乡村信息
 * @param {*}
 * @return {*}
 * @author: chenz76
 */
export const getVillage = (defaultDeptId) => {
  return request({
    url: '/api/big-screen/village/party-info',
    method: 'get',
    params:{
      deptId: defaultDeptId
    }
  })
}

/**
 * @description 中中2-获取通讯合作社数据
 * @param {*} params 
 * @returns 
 */
export const getCommunicationData = (params) => {
  return request({
    url: '/api/communicate/get-month',
    method: 'get',
    params: params
  })
}

/**
 * @description 中中2-判断本村是否有数据
 * @param {*} params 
 * @returns 
 */
export const hasData = () => {
  return request({
    url: '/api/communicate/is-data',
    method: 'get'
  })
}

/**
 * @description 中中2-获取有数据的月份列表
 * @returns 
 */
export const getDateList = () => {
  return request({
    url: '/api/communicate/date-list',
    method: 'get'
  })
}

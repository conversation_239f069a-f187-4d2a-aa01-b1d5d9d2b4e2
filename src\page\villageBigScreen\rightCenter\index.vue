<!--
 * @Description: 右中-产品集市
 * @Author: chenz76
 * @Date: 2021-10-26 14:42:02
 * @LastEditors: chenz76
 * @LastEditTime: 2022-01-06 10:39:15
-->
<template>

  <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column; padding: 1vh 1vw; padding-left: 0.5vw; box-sizing: border-box;">
    <div style="flex: 0 0 100%; position: relative; display: flex; flex-direction: column;">
      <img class="img-common" src="/img/bigScreen/modulebg.png" mode="scaleToFill" />

      <div style="display: flex; flex-direction: row; padding-right: 1vw;">
        <div class="head-idx" style="position: relative; width: 3vh; height: 3vh; font-weight: bold;">
          <img class="img-common" src="/img/bigScreen/idx.png" mode="scaleToFill" />
          05
        </div>
        <div style="position: relative; width: 100%; height: 100%; display: flex; align-items: center;">
          <img class="img-common" src="/img/bigScreen/subTitlebg.png" mode="scaleToFill" />
          <div class="head-title" style="margin-left: 1vw;">乡村治理</div>
        </div>
      </div>
      <div style="display: flex; flex-direction: row; justify-content: space-between; padding: 0 1vw; margin-top: 2vh; margin-bottom: 1vh;">
        <div class="btn" @click="onTap(1)">
          <img class="img-common" :src="selectedType==1?'/img/bigScreen/btnSelected.png':'/img/bigScreen/btnNormal.png'" mode="scaleToFill" />
          <div class="head-idx" style="font-size: 1.2vh; font-weight: Medium;">民情反馈</div>
        </div>
        <div class="btn" @click="onTap(2)">
          <img class="img-common" :src="selectedType==2?'/img/bigScreen/btnSelected.png':'/img/bigScreen/btnNormal.png'" mode="scaleToFill" />
          <div class="head-idx" style="font-size: 1.2vh; font-weight: Medium;">随手拍</div>
        </div>
        <div class="btn" @click="onTap(3)">
          <img class="img-common" :src="selectedType==3?'/img/bigScreen/btnSelected.png':'/img/bigScreen/btnNormal.png'" mode="scaleToFill" />
          <div class="head-idx" style="font-size: 1.2vh; font-weight: Medium;">四治三清</div>
        </div>
      </div>
        <div class="content-value" style="height: 18.5vh;">
          <ScrollBoard :config="config" ref="scrollBoard" />
        </div>
      </div>
  </div>
</template>
<script>
import * as echarts from "echarts/lib/echarts";
import "echarts/lib/chart/bar";
import "echarts/lib/chart/pie";
import "echarts/lib/component/tooltip";
import "echarts/lib/component/title";
import "echarts/lib/component/legend";
import { debounce } from "lodash";
import { getMarket } from "@/api/screen/screen";
import { ScrollBoard } from '@jiaminghi/data-view'
export default {
  components: {
    ScrollBoard,
  },

  data() {
    return {
      myChart: null,
      config: {
        header: [
          "<span>序号</span>",
          "<span>事件类型</span>",
          "<span>标题</span>",
          "<span>状态</span>",
        ],
        rowNum: 7,
        headerBGC: "",
        headerHeight: 30,
        columnWidth: [540, 380],
        evenRowBGC: "#102359",
        oddRowBGC: "",
        data: [],
      },
    };
  },
  mounted() {
    this.getMarket();
  },
  created() {
    this.setResize();
  },
  destroyed() {
    let self = this;
    window.removeEventListener("resize", function () {
      if (self.myChart) {
        self.myChart = null;
      }
    });
  },
  methods: {
    async getMarket() {
      this.processData([{eventType: 'hehe', title: 'hahahahahha', status:'处理完'},
  {eventType: 'hehe', title: 'hahahahahha', status:'处理完'},
{eventType: 'hehe', title: 'hahahahahha', status:'处理完'},]);
      // const result = await new Promise((resolve) => {
      //   getMarket().then((res) => {
      //     if (res && res.data.code === 200) {
      //       resolve(res.data.data);
      //     }
      //   });
      // });
      // this.$nextTick(() => {
      //   this.renderEcharts(result);
      // });
    },
    processData(data) {
      // 将数据转换为 dv-scroll-board 需要的格式
      const formattedData = data.map((item, index) => {
        return [
          `<span style="color:#fff;">${index + 1}</span>`,
          `<span style="color:#fff;">${item.eventType || ''}</span>`,
          `<span style="color:#fff;" title="${item.title || ''}">${item.title || ''}</span>`,
          `<span style="color:#fff;">${item.status || ''}</span>`
        ];
      });

      // 更新配置
      this.config = {
        ...this.config,
        data: formattedData
      };
    },
    renderEcharts(marketData) {
      const marketName = marketData.map((item) => {
        return item.name;
      });
      const marketValue = marketData.map((item) => {
        return item.value;
      });
      const maxNum6 = Math.max(...marketValue);
      const maxData = marketValue.map(() => {
        return parseInt(maxNum6 * 1.5);
      });
      this.myChart = echarts.init(document.getElementById("echart-market"));
      var option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(0,46, 115, 0.3)",
            },
          },
          formatter: function (params) {
            var str = "";
            str += params[0].name + ": " + params[0].value + " 条";
            return str;
          },
          textStyle: {
            align: "center",
            color: "#5cc1ff",
            fontSize: "16",
          },
          backgroundColor: "rgba(15, 52, 135, 0.5)",
          borderWidth: "1",
          borderColor: "#5cc1ff",
          extraCssText: "box-shadow: 0 0 10px rgba(255, 255, 255, 0.7);",
        },
        //背景颜色，使用时请注释
        // backgroundColor:'#07253a',
        legend: {
          y: "top",
          x: "left",
          show: false,
          icon: "rect",
          textStyle: {
            color: "#fff",
          },
        },
        // color: ["#4effe8", "#0573E9"],
        grid: {
          left: "5%",
          top: "35",
          right: "5",
          width: "90%",
          height: "82%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          show: true,
          axisLine: {
            show: false,
          },
          axisTick: { show: false },
          axisLabel: { textStyle: { color: "#fff" } },
          nameTextStyle: { color: "#fff" },
          data: marketName,
        },
        yAxis: [
          {
            type: "value",
            name: "              发布总量（条）",
            axisLine: {
              show: false,
            },
            min: 0,
            max: maxData[0],
            splitNumber: 2,
            axisTick: { show: false },
            axisLabel: { textStyle: { color: "#fff" } },
            nameTextStyle: { color: "#fff" },
            splitLine: {
              show: false,
              lineStyle: {
                type: "dashed",
                color: "#6a7c89",
              },
            },
          },
        ],
        series: [
          {
            name: "数值",
            type: "bar",
            barMaxWidth: 20,
            label: {
              show: true,
              color: "white",
              position: "top",
            },
            itemStyle: {
              normal: {
                color: function (d) {
                  if (d.data > 0) {
                    return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "#30defd",
                      },
                      {
                        offset: 0.5,
                        color: "#09A1F0",
                      },
                      {
                        offset: 1,
                        color: "#0573E9",
                      },
                    ]);
                  } else {
                    return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: "#30defd",
                      },
                      {
                        offset: 0.5,
                        color: "#09A1F0",
                      },
                      {
                        offset: 1,
                        color: "#0573E9",
                      },
                    ]);
                  }
                },
                // opacity: 0.6,
                barBorderRadius: 15,
              },
              emphasis: {
                opacity: 0.6,
              },
            },
            data: marketValue,
            zlevel: 9,
          },
          {
            name: "最大值",
            type: "bar",
            barMaxWidth: 20,

            itemStyle: {
              normal: { color: "#303E8C", barBorderRadius: 15 },
            },
            barGap: "-100%",
            barCategoryGap: "60%",
            data: maxData,
            animation: true,
          },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      this.myChart.setOption(option);
    },
    setResize() {
      let self = this;
      window.addEventListener(
        "resize",
        debounce(function () {
          if (self.myChart) self.myChart.resize();
        }, 200)
      );
    },
  }
};
</script>

<style lang="scss" scoped>
.img-common {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: -1;
}

.head-idx {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 1.7vh;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  justify-content: center;
  align-items: center;
}

.head-title {
  text-shadow: 0 4px 8px #00000040, 0 8px 16px #00000040;
  font-size: 1.5vh;
  font-style: normal;
  font-weight: Bold;
  line-height: normal;
  background: linear-gradient(188deg, #C9F5FF 11.62%, #00FFFC 30.26%, #00A1FF 94.12%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: flex;
  align-items: center;
}

.btn {
  position: relative;
  width: 6vw;
  height: 2.2vh;
  cursor: pointer;
}
</style>

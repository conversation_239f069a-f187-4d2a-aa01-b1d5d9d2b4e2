/*
 * @Description: base
 * @Author: lins14
 * @Date: 2021-12-10 09:17:54
 * @FilePath: /szxc-saber/src/views/system/user/mixins/base.js
 * @LastEditors: lins14
 * @LastEditTime: 2022-03-10 10:25:30
 */
import { getList } from '@/api/system/user'
import { mapGetters } from 'vuex'
import { getToken } from '@/util/auth'
import UserWrapper from '../components/UserWrapper'
import { handleDownload } from '@/util/download'
import { debounce } from 'lodash'
export default {
  components: { UserWrapper },
  data() {
    return {
      exportIcon: 'el-icon-download',
      disableExport: false,
      cachedSearchForm: {},
      treeDeptId: '',
      selectionList: [],
      query: {},
      form: {},
      search: {},
      initFlag: true,
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      data: []
    }
  },
  computed: {
    ...mapGetters(['permission']),
    ids() {
      return this.selectionList.map(ele => ele.id).join(',')
    }
  },
  methods: {
    treeNodeClick(param) {
      this.page.currentPage = 1
      this.treeDeptId = param.treeDeptId
      this.onLoad(this.page)
    },
    searchReset() {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange(params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange(list) {
      this.selectionList = list
    },
    selectionClear() {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    handleExport: debounce(function() {
      this.$confirm('是否导出用户数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          this.exportIcon = 'el-icon-loading'
          this.disableExport = true
          const exportUrl = '/api/blade-user/export-user'
          const obj2url = obj =>
            Object.keys(obj)
              .map(key => `${key}=${obj[key]}`)
              .join('&')
          const search = this.cachedSearchForm
          const baseParams = {
            [this.website.tokenHeader]: getToken(),
            phone: search.phone || '',
            account: search.account || '',
            labelId: search.labelId || '',
            realName: search.realName || '',
            name: search.name || '',
            deptId: this.treeDeptId,
            userKind: this.userKind
          }
          const result = await handleDownload(
            this.ids ? `${exportUrl}?${obj2url({ ...baseParams, userIds: this.ids })}` : `${exportUrl}?${obj2url(baseParams)}`
          )
          if (result) {
            this.exportIcon = 'el-icon-download'
            this.disableExport = false
          }
        })
        .catch(() => {})
    }),
    currentChange(currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange() {
      this.onLoad(this.page, this.query)
    },
    onLoad(page, params = {}) {
      this.loading = true
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query), this.treeDeptId, this.userKind).then(res => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
        this.cachedSearchForm = Object.assign(params, this.query)
      })
    }
  }
}
